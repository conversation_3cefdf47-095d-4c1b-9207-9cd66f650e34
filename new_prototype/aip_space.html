<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIP空间 - Artifact Workspace</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        /* 基础样式 */
        * {
            box-sizing: border-box;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f9fafb;
            color: #1f2937;
            line-height: 1.5;
        }

        /* 背景动画效果 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 15% 85%, rgba(79, 70, 229, 0.12) 0%, transparent 60%),
                radial-gradient(circle at 85% 15%, rgba(168, 85, 247, 0.1) 0%, transparent 60%),
                radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.05) 0%, transparent 70%);
            pointer-events: none;
            z-index: -1;
            animation: atmosphericFloat 20s ease-in-out infinite alternate;
        }

        @keyframes atmosphericFloat {
            0% {
                transform: translateY(0px) scale(1);
                opacity: 0.8;
            }
            100% {
                transform: translateY(-10px) scale(1.02);
                opacity: 1;
            }
        }

        /* 隐藏滚动条但保持功能 */
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }

        /* 头部样式 */
        .header {
            position: sticky;
            top: 0;
            z-index: 50;
            background-color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(12px);
            border-bottom: 1px solid #e5e7eb;
            padding: 0 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .header-container {
            max-width: 1600px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 4rem;
        }

        /* Logo样式 */
        .logo a {
            font-size: 1.5rem;
            font-weight: 900;
            background: linear-gradient(to right, #2563eb, #9333ea);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-decoration: none;
            transition: all 0.3s;
        }

        .logo a:hover {
            background: linear-gradient(to right, #9333ea, #2563eb);
        }

        /* 主内容区域 */
        .main-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 1.5rem 1rem;
        }

        /* Tab样式 */
        .tab-button {
            padding: 0.75rem 1.5rem;
            color: #4b5563;
            font-weight: 500;
            transition: all 0.2s;
            border-bottom: 2px solid transparent;
            background: none;
            border-top: none;
            border-left: none;
            border-right: none;
            cursor: pointer;
        }
        .tab-button:hover {
            color: #1f2937;
        }
        .tab-button.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }

        /* 播客播放器样式 */
        .podcast-player {
            background: linear-gradient(135deg, #1e1b4b 0%, #312e81 100%);
            padding: 1.5rem;
            border-radius: 1rem;
            border: 1px solid rgba(99, 102, 241, 0.2);
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            min-height: 140px;
        }
        .podcast-player::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(168, 85, 247, 0.1) 0%, transparent 70%);
            animation: pulse 4s ease-in-out infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.5; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }
        .podcast-icon {
            font-size: 3.5rem;
            animation: float 3s ease-in-out infinite;
        }
        .podcast-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin: 1rem 0;
            position: relative;
            z-index: 1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-3px); }
        }
        .podcast-title {
            color: white;
            font-size: 0.875rem;
            font-weight: 600;
            flex: 1;
        }
        .podcast-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
            position: relative;
            z-index: 1;
        }
        .play-button {
            width: 3rem;
            height: 3rem;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
            border: none;
            flex-shrink: 0;
        }
        .play-button:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.6);
        }
        .play-button:active {
            transform: scale(0.95);
        }
        .play-button svg {
            width: 1.5rem;
            height: 1.5rem;
            color: white;
        }
        .progress-container {
            flex: 1;
            position: relative;
        }
        .progress-bar {
            position: relative;
            width: 100%;
            height: 0.5rem;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 9999px;
            overflow: hidden;
            cursor: pointer;
            margin-bottom: 0.5rem;
        }
        .progress-fill {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%);
            border-radius: 9999px;
            transition: width 0.3s;
            box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
        }
        .progress-fill::after {
            content: '';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 0.875rem;
            height: 0.875rem;
            background: white;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }
        .time-display {
            display: flex;
            justify-content: space-between;
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        /* 指标卡片样式 */
        .metric-card {
            background-color: white;
            padding: 1.25rem;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%);
            opacity: 0;
            transition: opacity 0.3s;
        }
        .metric-card:hover {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
            border-color: #ddd6fe;
        }
        .metric-card:hover::before {
            opacity: 1;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .metric-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        .metric-chart-container {
            position: relative;
            height: 4rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .metric-info {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.5rem;
        }
        .metric-status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 600;
            border-radius: 9999px;
            background-color: #fed7aa;
            color: #c2410c;
        }

        /* 文件树样式 */
        .tree-node {
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.25rem;
            transition: background-color 0.15s;
        }
        .tree-node:hover {
            background-color: #f9fafb;
        }
        .tree-node.expanded::before {
            content: '▼ ';
        }
        .tree-node.collapsed::before {
            content: '▶ ';
        }
        .tree-children {
            margin-left: 1rem;
            display: none;
        }
        .tree-children.show {
            display: block;
        }

        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        .data-table th {
            background-color: #f9fafb;
            padding: 0.75rem 1rem;
            text-align: left;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }
        .data-table td {
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            color: #4b5563;
            border-bottom: 1px solid #f3f4f6;
        }
        .data-table tr:hover {
            background-color: #f9fafb;
        }
        .data-table tr.highlighted {
            background-color: #eff6ff;
        }

        /* 决策流水线样式 */
        .pipeline-stage {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        .pipeline-stage:hover {
            transform: scale(1.05);
        }
        .pipeline-node {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            background-color: white;
            border: 2px solid #d1d5db;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }
        .pipeline-node.completed {
            background-color: #10b981;
            border-color: #10b981;
            color: white;
        }
        .pipeline-node.active {
            background-color: #3b82f6;
            border-color: #3b82f6;
            color: white;
        }
        .pipeline-connector {
            position: absolute;
            top: 1.5rem;
            left: 100%;
            width: 100%;
            height: 2px;
            background-color: #d1d5db;
        }
        
        /* 决策清单样式 */
        .question-card {
            background-color: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            border: 1px solid #e5e7eb;
            margin-bottom: 1rem;
        }
        .option-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.2s;
        }
        .option-item:hover {
            border-color: #93c5fd;
            background-color: #eff6ff;
        }
        .option-item.selected {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        
        /* 图片网格样式 */
        .image-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
        }
        .image-thumbnail {
            position: relative;
            aspect-ratio: 1;
            background-color: #f3f4f6;
            border-radius: 0.5rem;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s;
        }
        .image-thumbnail:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        .image-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 按钮样式 */
        button {
            font-family: inherit;
        }

        /* 网格系统 */
        .grid {
            display: grid;
        }
        .grid-cols-4 {
            grid-template-columns: repeat(4, 1fr);
        }
        .grid-cols-12 {
            grid-template-columns: repeat(12, 1fr);
        }
        .col-span-3 {
            grid-column: span 3 / span 3;
        }
        .col-span-9 {
            grid-column: span 9 / span 9;
        }
        .gap-4 {
            gap: 1rem;
        }
        .gap-6 {
            gap: 1.5rem;
        }

        /* 工具类 */
        .hidden {
            display: none;
        }
        .flex {
            display: flex;
        }
        .items-center {
            align-items: center;
        }
        .justify-between {
            justify-content: space-between;
        }
        .justify-center {
            justify-content: center;
        }
        .flex-col {
            flex-direction: column;
        }
        .gap-2 {
            gap: 0.5rem;
        }
        .gap-3 {
            gap: 0.75rem;
        }
        .gap-4 {
            gap: 1rem;
        }
        .gap-8 {
            gap: 2rem;
        }
        .mb-2 {
            margin-bottom: 0.5rem;
        }
        .mb-3 {
            margin-bottom: 0.75rem;
        }
        .mb-4 {
            margin-bottom: 1rem;
        }
        .mb-6 {
            margin-bottom: 1.5rem;
        }
        .mb-8 {
            margin-bottom: 2rem;
        }
        .mt-2 {
            margin-top: 0.5rem;
        }
        .mt-6 {
            margin-top: 1.5rem;
        }
        .mt-8 {
            margin-top: 2rem;
        }
        .ml-1 {
            margin-left: 0.25rem;
        }
        .ml-4 {
            margin-left: 1rem;
        }
        .mr-3 {
            margin-right: 0.75rem;
        }
        .px-2 {
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }
        .px-3 {
            padding-left: 0.75rem;
            padding-right: 0.75rem;
        }
        .px-4 {
            padding-left: 1rem;
            padding-right: 1rem;
        }
        .px-6 {
            padding-left: 1.5rem;
            padding-right: 1.5rem;
        }
        .px-8 {
            padding-left: 2rem;
            padding-right: 2rem;
        }
        .py-1 {
            padding-top: 0.25rem;
            padding-bottom: 0.25rem;
        }
        .py-2 {
            padding-top: 0.5rem;
            padding-bottom: 0.5rem;
        }
        .py-3 {
            padding-top: 0.75rem;
            padding-bottom: 0.75rem;
        }
        .py-0\.5 {
            padding-top: 0.125rem;
            padding-bottom: 0.125rem;
        }
        .py-12 {
            padding-top: 3rem;
            padding-bottom: 3rem;
        }
        .p-4 {
            padding: 1rem;
        }
        .p-6 {
            padding: 1.5rem;
        }
        .pr-6 {
            padding-right: 1.5rem;
        }
        .space-y-2 > * + * {
            margin-top: 0.5rem;
        }
        .space-y-3 > * + * {
            margin-top: 0.75rem;
        }
        .space-y-6 > * + * {
            margin-top: 1.5rem;
        }

        /* 文字样式 */
        .text-xs {
            font-size: 0.75rem;
        }
        .text-sm {
            font-size: 0.875rem;
        }
        .text-lg {
            font-size: 1.125rem;
        }
        .text-xl {
            font-size: 1.25rem;
        }
        .text-2xl {
            font-size: 1.5rem;
        }
        .font-medium {
            font-weight: 500;
        }
        .font-semibold {
            font-weight: 600;
        }
        .font-bold {
            font-weight: 700;
        }
        .font-black {
            font-weight: 900;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .text-gray-500 {
            color: #6b7280;
        }
        .text-gray-600 {
            color: #4b5563;
        }
        .text-gray-700 {
            color: #374151;
        }
        .text-gray-800 {
            color: #1f2937;
        }
        .text-white {
            color: white;
        }
        .text-blue-600 {
            color: #2563eb;
        }
        .text-blue-800 {
            color: #1e40af;
        }
        .text-green-600 {
            color: #059669;
        }
        .text-orange-500 {
            color: #f97316;
        }
        .text-orange-600 {
            color: #ea580c;
        }
        .text-yellow-600 {
            color: #ca8a04;
        }

        /* 边框样式 */
        .border {
            border-width: 1px;
        }
        .border-2 {
            border-width: 2px;
        }
        .border-b {
            border-bottom-width: 1px;
        }
        .border-r {
            border-right-width: 1px;
        }
        .border-gray-100 {
            border-color: #f3f4f6;
        }
        .border-gray-200 {
            border-color: #e5e7eb;
        }
        .border-blue-200 {
            border-color: #bfdbfe;
        }

        /* 背景样式 */
        .bg-white {
            background-color: white;
        }
        .bg-gray-50 {
            background-color: #f9fafb;
        }
        .bg-gray-100 {
            background-color: #f3f4f6;
        }
        .bg-gray-200 {
            background-color: #e5e7eb;
        }
        .bg-blue-50 {
            background-color: #eff6ff;
        }
        .bg-blue-100 {
            background-color: #dbeafe;
        }
        .bg-blue-600 {
            background-color: #2563eb;
        }
        .bg-green-600 {
            background-color: #059669;
        }
        .bg-orange-100 {
            background-color: #fed7aa;
        }
        .bg-orange-500 {
            background-color: #f97316;
        }
        .bg-orange-600 {
            background-color: #ea580c;
        }

        /* 圆角样式 */
        .rounded {
            border-radius: 0.25rem;
        }
        .rounded-lg {
            border-radius: 0.5rem;
        }
        .rounded-xl {
            border-radius: 0.75rem;
        }
        .rounded-2xl {
            border-radius: 1rem;
        }
        .rounded-full {
            border-radius: 9999px;
        }

        /* 阴影样式 */
        .shadow-md {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .shadow-lg {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        .shadow-2xl {
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 过渡样式 */
        .transition-all {
            transition: all 0.3s;
        }
        .transition-colors {
            transition: background-color 0.15s, border-color 0.15s, color 0.15s;
        }
        .transition-shadow {
            transition: box-shadow 0.3s;
        }

        /* Hover样式 */
        .hover\:bg-gray-200:hover {
            background-color: #e5e7eb;
        }
        .hover\:bg-gray-300:hover {
            background-color: #d1d5db;
        }
        .hover\:bg-blue-700:hover {
            background-color: #1d4ed8;
        }
        .hover\:bg-green-700:hover {
            background-color: #047857;
        }
        .hover\:shadow-lg:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        /* 其他样式 */
        .overflow-hidden {
            overflow: hidden;
        }
        .overflow-x-auto {
            overflow-x: auto;
        }
        .relative {
            position: relative;
        }
        .absolute {
            position: absolute;
        }
        .fixed {
            position: fixed;
        }
        .sticky {
            position: sticky;
        }
        .inset-0 {
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
        }
        .top-0 {
            top: 0;
        }
        .left-0 {
            left: 0;
        }
        .right-0 {
            right: 0;
        }
        .z-0 {
            z-index: 0;
        }
        .z-10 {
            z-index: 10;
        }
        .z-50 {
            z-index: 50;
        }
        .w-5 {
            width: 1.25rem;
        }
        .h-5 {
            height: 1.25rem;
        }
        .w-6 {
            width: 1.5rem;
        }
        .h-6 {
            height: 1.5rem;
        }
        .w-8 {
            width: 2rem;
        }
        .h-8 {
            height: 2rem;
        }
        .w-10 {
            width: 2.5rem;
        }
        .h-10 {
            height: 2.5rem;
        }
        .w-24 {
            width: 6rem;
        }
        .h-24 {
            height: 6rem;
        }
        .h-2 {
            height: 0.5rem;
        }
        .h-16 {
            height: 4rem;
        }
        .w-full {
            width: 100%;
        }
        .h-full {
            height: 100%;
        }
        .max-w-md {
            max-width: 28rem;
        }
        .mx-4 {
            margin-left: 1rem;
            margin-right: 1rem;
        }
        .mx-auto {
            margin-left: auto;
            margin-right: auto;
        }
        .flex-1 {
            flex: 1 1 0%;
        }
        .inline-flex {
            display: inline-flex;
        }
        .bg-black {
            background-color: black;
        }
        .bg-opacity-50 {
            background-color: rgba(0, 0, 0, 0.5);
        }
        .aspect-square {
            aspect-ratio: 1 / 1;
        }
        .object-cover {
            object-fit: cover;
        }
        .fill-current {
            fill: currentColor;
        }
        .w-12 {
            width: 3rem;
        }
        .h-12 {
            height: 3rem;
        }
        .h-2\.5 {
            height: 0.625rem;
        }
        .space-y-1 > * + * {
            margin-top: 0.25rem;
        }
        .bg-green-50 {
            background-color: #f0fdf4;
        }
        .bg-orange-50 {
            background-color: #fff7ed;
        }
        .text-green-600 {
            color: #059669;
        }
        .text-orange-600 {
            color: #ea580c;
        }
        .text-orange-400 {
            color: #fb923c;
        }
        .from-orange-400 {
            --tw-gradient-from: #fb923c;
        }
        .to-orange-600 {
            --tw-gradient-to: #ea580c;
        }
        .bg-gradient-to-r {
            background-image: linear-gradient(to right, var(--tw-gradient-from), var(--tw-gradient-to));
        }

        /* Like and Favorite Button Styles */
        .like-btn, .favorite-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.375rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            background-color: white;
            color: #374151;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .like-btn:hover, .favorite-btn:hover {
            background-color: #f9fafb;
            border-color: #9ca3af;
        }

        .like-btn.liked {
            background-color: #3b82f6;
            border-color: #3b82f6;
            color: white;
        }

        .favorite-btn.favorited {
            background-color: #f59e0b;
            border-color: #f59e0b;
            color: white;
        }

        .like-btn .icon, .favorite-btn .icon {
            font-size: 1rem;
        }

        .like-btn .count, .favorite-btn .text {
            font-weight: 500;
        }

        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
            .grid.grid-cols-4 {
                display: block !important;
            }
            
            .grid.grid-cols-4 > * {
                margin-bottom: 1rem;
            }
            
            .grid.grid-cols-4 > *:last-child {
                margin-bottom: 0;
            }
        }
    </style>
</head>
<body>
    <!-- 全局顶部导航栏 -->
    <header class="header sticky top-0 z-50 bg-white/80 backdrop-blur-xl border-b border-gray-100 px-4 shadow-lg">
        <div class="header-container max-w-[1600px] mx-auto flex justify-between items-center h-16">
            <div class="flex items-center gap-8">
                <!-- Logo -->
                <div class="logo">
                    <a href="index.html" class="text-2xl font-black bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent hover:from-purple-600 hover:to-blue-600 transition-all duration-300">Foxu.AI</a>
                </div>
                <!-- 工作空间名称 -->
                <h1 class="text-xl font-bold text-gray-800">2025年Q1市场扩张决策</h1>
            </div>
            
            <div class="flex items-center gap-4">
                <!-- 分享按钮 -->
                <button class="flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors" onclick="showShareModal()">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m9.632 4.316C18.114 15.938 18 15.482 18 15c0-.482.114-.938.316-1.342m0 2.684a3 3 0 110-2.684M8.684 10.658a3 3 0 118.632 0M3 12a9 9 0 1118 0 9 9 0 01-18 0z"/>
                    </svg>
                    <span class="text-sm font-medium">分享</span>
                </button>
                
                <!-- 用户信息区 -->
                <div class="user-info flex items-center gap-3">
                    <img src="assets/avatars/david-chen.svg" alt="用户头像" class="w-8 h-8 rounded-full border-2 border-blue-200">
                    <span class="text-sm font-medium text-gray-700">David Chen</span>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content max-w-[1600px] mx-auto px-4 py-6">
        <!-- 顶部核心信息区 -->
        <section class="top-info-section mb-6">
            <div class="grid grid-cols-4 gap-4">
                <!-- 播客播放器卡片 -->
                <div class="podcast-player">
                    <div class="podcast-icon">🎧</div>
                    <div class="podcast-header">
                        <h3 class="podcast-title">AIP空间播客总结</h3>
                    </div>
                    <div class="podcast-controls">
                        <button class="play-button" onclick="togglePlayPause()">
                            <svg id="playIcon" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8 5v14l11-7z"/>
                            </svg>
                            <svg id="pauseIcon" class="hidden" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                            </svg>
                        </button>
                        <div class="progress-container">
                            <div class="progress-bar" onclick="seekAudio(event)">
                                <div class="progress-fill" style="width: 35%"></div>
                            </div>
                            <div class="time-display">
                                <span>03:45</span>
                                <span>10:30</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 调研成果指标卡片 -->
                <div class="metric-card">
                    <h4 class="metric-label">调研成果</h4>
                    <div class="flex items-center justify-between mb-3">
                        <span class="metric-value">15份</span>
                        <div class="w-12 h-12 bg-blue-50 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="metric-chart-container">
                        <canvas id="credibilityChart"></canvas>
                    </div>
                    <p class="metric-info">可信度：高10份 · 中4份 · 低1份</p>
                </div>

                <!-- 分析产出指标卡片 -->
                <div class="metric-card">
                    <h4 class="metric-label">分析产出</h4>
                    <div class="flex items-center justify-between mb-3">
                        <span class="metric-value">23个</span>
                        <div class="w-12 h-12 bg-green-50 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="space-y-1">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">📊 报告</span>
                            <span class="font-medium text-gray-800">8份</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">📈 图表</span>
                            <span class="font-medium text-gray-800">12张</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">📑 Excel</span>
                            <span class="font-medium text-gray-800">3个</span>
                        </div>
                    </div>
                    <p class="metric-info mt-2">最新：Q1市场扩张综合分析报告</p>
                </div>

                <!-- 决策清单状态卡片 -->
                <div class="metric-card">
                    <h4 class="metric-label">决策清单</h4>
                    <div class="flex items-center justify-between mb-3">
                        <span class="metric-status-badge">会前填写中</span>
                        <div class="w-12 h-12 bg-orange-50 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
                            </svg>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="flex justify-between text-sm mb-2">
                            <span class="text-gray-600">填写进度</span>
                            <span class="font-medium text-gray-800">8/10人</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5 overflow-hidden">
                            <div class="bg-gradient-to-r from-orange-400 to-orange-600 h-full rounded-full transition-all duration-500" style="width: 80%"></div>
                        </div>
                    </div>
                    <p class="metric-info">剩余2人未完成填写</p>
                </div>
            </div>
        </section>

        <!-- 决策流水线概览 -->
        <section class="pipeline-section mb-6">
            <div class="bg-white rounded-xl p-6 border border-gray-200">
                <h3 class="text-lg font-bold text-gray-800 mb-4">决策流水线</h3>
                <div class="flex justify-between items-center relative px-8">
                    <!-- 连接线 -->
                    <div class="absolute top-6 left-0 right-0 h-0.5 bg-gray-300 z-0"></div>
                    
                    <!-- 阶段节点 -->
                    <div class="pipeline-stage z-10" onclick="switchToTab('research')">
                        <div class="pipeline-node completed">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 1 1 0 000 2H6a2 2 0 100 4h2a2 2 0 100-4h1a1 1 0 100-2H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2h-1v1h1v10H6V5h1V4H6z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <span class="mt-2 text-sm font-medium text-gray-700">调研成果</span>
                        <span class="text-xs text-gray-500">15份文件</span>
                    </div>

                    <div class="pipeline-stage z-10" onclick="switchToTab('analysis')">
                        <div class="pipeline-node completed">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                            </svg>
                        </div>
                        <span class="mt-2 text-sm font-medium text-gray-700">分析产出</span>
                        <span class="text-xs text-gray-500">23个成果</span>
                    </div>

                    <div class="pipeline-stage z-10" onclick="switchToTab('briefing')">
                        <div class="pipeline-node active">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <span class="mt-2 text-sm font-medium text-gray-700">决策简报</span>
                        <span class="text-xs text-gray-500">PPT就绪</span>
                    </div>

                    <div class="pipeline-stage z-10" onclick="switchToTab('checklist')">
                        <div class="pipeline-node">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 1 1 0 000 2H6a2 2 0 100 4h2a2 2 0 100-4h1a1 1 0 100-2H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2h-1v1h1v10H6V5h1V4H6z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <span class="mt-2 text-sm font-medium text-gray-700">决策清单</span>
                        <span class="text-xs text-orange-500">填写中</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Tab导航和内容区 -->
        <section class="tab-section">
            <div class="bg-white rounded-xl border border-gray-200">
                <!-- Tab导航栏 -->
                <div class="flex border-b border-gray-200">
                    <button class="tab-button active" onclick="switchToTab('research')">
                        调研成果 <span class="ml-1 text-sm text-gray-500">(15)</span>
                    </button>
                    <button class="tab-button" onclick="switchToTab('analysis')">
                        分析产出 <span class="ml-1 text-sm bg-blue-100 text-blue-800 px-2 py-0.5 rounded text-xs">报告</span>
                    </button>
                    <button class="tab-button" onclick="switchToTab('briefing')">
                        决策简报
                    </button>
                    <button class="tab-button" onclick="switchToTab('checklist')">
                        决策清单 <span class="ml-1 text-sm bg-orange-100 text-orange-800 px-2 py-0.5 rounded text-xs">会前填写中</span>
                    </button>
                </div>

                <!-- Tab内容区 -->
                <div class="tab-contents p-6">
                    <!-- 调研成果Tab -->
                    <div id="research-tab" class="tab-content active">
                        <div class="grid grid-cols-12 gap-6">
                            <!-- 左侧文件树 -->
                            <div class="col-span-3 border-r border-gray-200 pr-6">
                                <h4 class="font-semibold text-gray-700 mb-4">文件目录</h4>
                                <div class="tree-node collapsed" onclick="toggleTree(this)">
                                    <span class="font-medium">全部文件 (15)</span>
                                </div>
                                <div class="tree-children" id="fileTree">
                                    <div class="tree-node ml-4" onclick="selectFile(1)">文件001</div>
                                    <div class="tree-node ml-4" onclick="selectFile(2)">文件002</div>
                                    <div class="tree-node ml-4" onclick="selectFile(3)">文件003</div>
                                    <div class="tree-node ml-4" onclick="selectFile(4)">文件004</div>
                                    <div class="tree-node ml-4" onclick="selectFile(5)">文件005</div>
                                </div>
                            </div>

                            <!-- 右侧文件表格 -->
                            <div class="col-span-9">
                                <h4 class="font-semibold text-gray-700 mb-4">文件详情</h4>
                                <div class="overflow-x-auto">
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>文件编号</th>
                                                <th>文件名</th>
                                                <th>文件发布机构</th>
                                                <th>搜集时间</th>
                                                <th>可信度</th>
                                                <th>相关性评估</th>
                                                <th>文件字数</th>
                                                <th>阅读时间</th>
                                                <th>点赞/收藏</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr data-file-id="1">
                                                <td>文件001</td>
                                                <td>2025年欧洲市场展望报告</td>
                                                <td>McKinsey</td>
                                                <td>2024-12-15</td>
                                                <td><span class="text-green-600 font-medium">高</span></td>
                                                <td>95%</td>
                                                <td>12,580</td>
                                                <td>约6分钟</td>
                                                <td>
                                                    <button class="like-btn" onclick="toggleLike(this)">
                                                        <span class="icon">👍</span>
                                                        <span class="count">12</span>
                                                    </button>
                                                    <button class="favorite-btn" onclick="toggleFavorite(this)">
                                                        <span class="icon">⭐</span>
                                                        <span class="text">收藏</span>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr data-file-id="2">
                                                <td>文件002</td>
                                                <td>德国消费者行为分析</td>
                                                <td>Euromonitor</td>
                                                <td>2024-12-10</td>
                                                <td><span class="text-green-600 font-medium">高</span></td>
                                                <td>88%</td>
                                                <td>8,320</td>
                                                <td>约4分钟</td>
                                                <td>
                                                    <button class="like-btn" onclick="toggleLike(this)">
                                                        <span class="icon">👍</span>
                                                        <span class="count">8</span>
                                                    </button>
                                                    <button class="favorite-btn" onclick="toggleFavorite(this)">
                                                        <span class="icon">⭐</span>
                                                        <span class="text">收藏</span>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr data-file-id="3">
                                                <td>文件003</td>
                                                <td>美国市场准入指南</td>
                                                <td>US Commerce</td>
                                                <td>2024-11-28</td>
                                                <td><span class="text-yellow-600 font-medium">中</span></td>
                                                <td>82%</td>
                                                <td>6,450</td>
                                                <td>约3分钟</td>
                                                <td>
                                                    <button class="like-btn" onclick="toggleLike(this)">
                                                        <span class="icon">👍</span>
                                                        <span class="count">15</span>
                                                    </button>
                                                    <button class="favorite-btn" onclick="toggleFavorite(this)">
                                                        <span class="icon">⭐</span>
                                                        <span class="text">收藏</span>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr data-file-id="4">
                                                <td>文件004</td>
                                                <td>英国脱欧后贸易政策分析</td>
                                                <td>KPMG</td>
                                                <td>2024-12-05</td>
                                                <td><span class="text-green-600 font-medium">高</span></td>
                                                <td>91%</td>
                                                <td>9,200</td>
                                                <td>约5分钟</td>
                                                <td>
                                                    <button class="like-btn" onclick="toggleLike(this)">
                                                        <span class="icon">👍</span>
                                                        <span class="count">6</span>
                                                    </button>
                                                    <button class="favorite-btn" onclick="toggleFavorite(this)">
                                                        <span class="icon">⭐</span>
                                                        <span class="text">收藏</span>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr data-file-id="5">
                                                <td>文件005</td>
                                                <td>日本市场进入策略指南</td>
                                                <td>JETRO</td>
                                                <td>2024-11-20</td>
                                                <td><span class="text-yellow-600 font-medium">中</span></td>
                                                <td>79%</td>
                                                <td>7,830</td>
                                                <td>约4分钟</td>
                                                <td>
                                                    <button class="like-btn" onclick="toggleLike(this)">
                                                        <span class="icon">👍</span>
                                                        <span class="count">22</span>
                                                    </button>
                                                    <button class="favorite-btn" onclick="toggleFavorite(this)">
                                                        <span class="icon">⭐</span>
                                                        <span class="text">收藏</span>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- 文件预览区 -->
                                <div class="mt-6 p-4 bg-gray-50 rounded-lg" id="filePreview" style="display: none;">
                                    <h5 class="font-semibold text-gray-700 mb-2">文件预览</h5>
                                    <div class="text-sm text-gray-600">
                                        <p>请选择左侧文件查看详情...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分析产出Tab -->
                    <div id="analysis-tab" class="tab-content">
                        <div class="grid grid-cols-12 gap-6">
                            <!-- 左侧分类 -->
                            <div class="col-span-3 border-r border-gray-200 pr-6">
                                <h4 class="font-semibold text-gray-700 mb-4">产出分类</h4>
                                <div class="space-y-2">
                                    <!-- 报告分类 -->
                                    <div class="tree-node collapsed" onclick="toggleAnalysisTree(this, 'reports')">
                                        <span class="font-medium">报告 (8)</span>
                                    </div>
                                    <div class="tree-children" id="reports-tree">
                                        <div class="tree-node ml-4" onclick="selectAnalysisFile('reports', 1)">报告001</div>
                                        <div class="tree-node ml-4" onclick="selectAnalysisFile('reports', 2)">报告002</div>
                                        <div class="tree-node ml-4" onclick="selectAnalysisFile('reports', 3)">报告003</div>
                                        <div class="tree-node ml-4" onclick="selectAnalysisFile('reports', 4)">报告004</div>
                                        <div class="tree-node ml-4" onclick="selectAnalysisFile('reports', 5)">报告005</div>
                                        <div class="tree-node ml-4" onclick="selectAnalysisFile('reports', 6)">报告006</div>
                                        <div class="tree-node ml-4" onclick="selectAnalysisFile('reports', 7)">报告007</div>
                                        <div class="tree-node ml-4" onclick="selectAnalysisFile('reports', 8)">报告008</div>
                                    </div>
                                    
                                    <!-- 图片分类 -->
                                    <div class="tree-node collapsed" onclick="toggleAnalysisTree(this, 'images')">
                                        <span class="font-medium">图片 (12)</span>
                                    </div>
                                  
                                    <!-- Excel分类 -->
                                    <div class="tree-node collapsed" onclick="toggleAnalysisTree(this, 'excel')">
                                        <span class="font-medium">Excel (3)</span>
                                    </div>
                                    <div class="tree-children" id="excel-tree">
                                        <div class="tree-node ml-4" onclick="selectAnalysisFile('excel', 1)">Excel001</div>
                                        <div class="tree-node ml-4" onclick="selectAnalysisFile('excel', 2)">Excel002</div>
                                        <div class="tree-node ml-4" onclick="selectAnalysisFile('excel', 3)">Excel003</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 右侧内容区 -->
                            <div class="col-span-9">
                                <!-- 报告列表 -->
                                <div id="reports-content" class="analysis-content">
                                    <h4 class="font-semibold text-gray-700 mb-4">分析报告</h4>
                                    <div class="overflow-x-auto">
                                        <table class="data-table">
                                            <thead>
                                                <tr>
                                                    <th>文件编号</th>
                                                    <th>文件名</th>
                                                    <th>生成时间</th>
                                                    <th>文件字数</th>
                                                    <th>阅读时长</th>
                                                    <th>点赞/收藏</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>报告001</td>
                                                    <td>Q1市场扩张综合分析报告</td>
                                                    <td>2024-12-20 14:30</td>
                                                    <td>15,280</td>
                                                    <td>约8分钟</td>
                                                    <td>
                                                        <button class="like-btn" onclick="toggleLike(this)">
                                                            <span class="icon">👍</span>
                                                            <span class="count">28</span>
                                                        </button>
                                                        <button class="favorite-btn" onclick="toggleFavorite(this)">
                                                            <span class="icon">⭐</span>
                                                            <span class="text">收藏</span>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>报告002</td>
                                                    <td>德国市场深度分析</td>
                                                    <td>2024-12-19 10:15</td>
                                                    <td>10,450</td>
                                                    <td>约5分钟</td>
                                                    <td>
                                                        <button class="like-btn" onclick="toggleLike(this)">
                                                            <span class="icon">👍</span>
                                                            <span class="count">19</span>
                                                        </button>
                                                        <button class="favorite-btn" onclick="toggleFavorite(this)">
                                                            <span class="icon">⭐</span>
                                                            <span class="text">收藏</span>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>报告003</td>
                                                    <td>美国市场准入可行性研究</td>
                                                    <td>2024-12-18 16:22</td>
                                                    <td>12,890</td>
                                                    <td>约7分钟</td>
                                                    <td>
                                                        <button class="like-btn" onclick="toggleLike(this)">
                                                            <span class="icon">👍</span>
                                                            <span class="count">35</span>
                                                        </button>
                                                        <button class="favorite-btn" onclick="toggleFavorite(this)">
                                                            <span class="icon">⭐</span>
                                                            <span class="text">收藏</span>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>报告004</td>
                                                    <td>英国脱欧影响评估报告</td>
                                                    <td>2024-12-17 11:08</td>
                                                    <td>8,760</td>
                                                    <td>约4分钟</td>
                                                    <td>
                                                        <button class="like-btn" onclick="toggleLike(this)">
                                                            <span class="icon">👍</span>
                                                            <span class="count">14</span>
                                                        </button>
                                                        <button class="favorite-btn" onclick="toggleFavorite(this)">
                                                            <span class="icon">⭐</span>
                                                            <span class="text">收藏</span>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>报告005</td>
                                                    <td>日本市场消费者行为分析</td>
                                                    <td>2024-12-16 13:45</td>
                                                    <td>9,320</td>
                                                    <td>约5分钟</td>
                                                    <td>
                                                        <button class="like-btn" onclick="toggleLike(this)">
                                                            <span class="icon">👍</span>
                                                            <span class="count">21</span>
                                                        </button>
                                                        <button class="favorite-btn" onclick="toggleFavorite(this)">
                                                            <span class="icon">⭐</span>
                                                            <span class="text">收藏</span>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>报告006</td>
                                                    <td>竞争对手战略分析报告</td>
                                                    <td>2024-12-15 09:30</td>
                                                    <td>11,560</td>
                                                    <td>约6分钟</td>
                                                    <td>
                                                        <button class="like-btn" onclick="toggleLike(this)">
                                                            <span class="icon">👍</span>
                                                            <span class="count">17</span>
                                                        </button>
                                                        <button class="favorite-btn" onclick="toggleFavorite(this)">
                                                            <span class="icon">⭐</span>
                                                            <span class="text">收藏</span>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>报告007</td>
                                                    <td>供应链风险评估报告</td>
                                                    <td>2024-12-14 15:12</td>
                                                    <td>7,240</td>
                                                    <td>约4分钟</td>
                                                    <td>
                                                        <button class="like-btn" onclick="toggleLike(this)">
                                                            <span class="icon">👍</span>
                                                            <span class="count">9</span>
                                                        </button>
                                                        <button class="favorite-btn" onclick="toggleFavorite(this)">
                                                            <span class="icon">⭐</span>
                                                            <span class="text">收藏</span>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>报告008</td>
                                                    <td>市场定位策略建议书</td>
                                                    <td>2024-12-13 10:55</td>
                                                    <td>6,890</td>
                                                    <td>约3分钟</td>
                                                    <td>
                                                        <button class="like-btn" onclick="toggleLike(this)">
                                                            <span class="icon">👍</span>
                                                            <span class="count">31</span>
                                                        </button>
                                                        <button class="favorite-btn" onclick="toggleFavorite(this)">
                                                            <span class="icon">⭐</span>
                                                            <span class="text">收藏</span>
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- 图片网格 -->
                                <div id="images-content" class="analysis-content hidden">
                                    <h4 class="font-semibold text-gray-700 mb-4">分析图表</h4>
                                    <div class="image-grid">
                                        <div class="image-thumbnail">
                                            <img src="assets/charts/market-share.png" alt="市场份额分析">
                                        </div>
                                        <div class="image-thumbnail">
                                            <img src="assets/charts/growth-trend.png" alt="增长趋势">
                                        </div>
                                        <div class="image-thumbnail">
                                            <img src="assets/charts/competitor-analysis.png" alt="竞争分析">
                                        </div>
                                    </div>
                                </div>

                                <!-- Excel文件列表 -->
                                <div id="excel-content" class="analysis-content hidden">
                                    <h4 class="font-semibold text-gray-700 mb-4">数据表格</h4>
                                    <div class="overflow-x-auto">
                                        <table class="data-table">
                                            <thead>
                                                <tr>
                                                    <th>文件编号</th>
                                                    <th>文件名</th>
                                                    <th>生成时间</th>
                                                    <th>文件大小</th>
                                                    <th>工作表数量</th>
                                                    <th>点赞/收藏</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>Excel001</td>
                                                    <td>市场数据分析表.xlsx</td>
                                                    <td>2024-12-18 16:45</td>
                                                    <td>2.3MB</td>
                                                    <td>5个工作表</td>
                                                    <td>
                                                        <button class="like-btn" onclick="toggleLike(this)">
                                                            <span class="icon">👍</span>
                                                            <span class="count">43</span>
                                                        </button>
                                                        <button class="favorite-btn" onclick="toggleFavorite(this)">
                                                            <span class="icon">⭐</span>
                                                            <span class="text">收藏</span>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Excel002</td>
                                                    <td>竞争对手价格监控.xlsx</td>
                                                    <td>2024-12-17 14:20</td>
                                                    <td>1.8MB</td>
                                                    <td>3个工作表</td>
                                                    <td>
                                                        <button class="like-btn" onclick="toggleLike(this)">
                                                            <span class="icon">👍</span>
                                                            <span class="count">26</span>
                                                        </button>
                                                        <button class="favorite-btn" onclick="toggleFavorite(this)">
                                                            <span class="icon">⭐</span>
                                                            <span class="text">收藏</span>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Excel003</td>
                                                    <td>Q1预算规划表.xlsx</td>
                                                    <td>2024-12-16 09:30</td>
                                                    <td>0.9MB</td>
                                                    <td>2个工作表</td>
                                                    <td>
                                                        <button class="like-btn" onclick="toggleLike(this)">
                                                            <span class="icon">👍</span>
                                                            <span class="count">18</span>
                                                        </button>
                                                        <button class="favorite-btn" onclick="toggleFavorite(this)">
                                                            <span class="icon">⭐</span>
                                                            <span class="text">收藏</span>
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 决策简报Tab -->
                    <div id="briefing-tab" class="tab-content">
                        <div class="text-center py-12">
                            <div class="mb-6">
                                <svg class="w-24 h-24 mx-auto text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"/>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-800 mb-4">2025年Q1市场扩张决策简报</h3>
                            <p class="text-gray-600 mb-8">PPT预览区域 - 支持翻页和全屏演示</p>
                            <div class="inline-flex gap-4">
                                <button class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                    开始演示
                                </button>
                                <button class="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors">
                                    下载PPT
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 决策清单Tab -->
                    <div id="checklist-tab" class="tab-content">
                        <!-- 清单概览 -->
                        <div class="bg-blue-50 rounded-lg p-6 mb-6">
                            <div class="flex justify-between items-center">
                                <div>
                                    <h3 class="text-xl font-bold text-gray-800 mb-2">Q1市场扩张决策清单</h3>
                                    <p class="text-gray-600" id="stageDescription">等待管理员启动决策流程</p>
                                </div>
                                <div class="text-right">
                                    <span class="text-sm text-gray-500">当前状态</span>
                                    <p class="text-lg font-semibold" id="currentStatus">准备中</p>
                                    <p class="text-sm text-gray-600" id="progressInfo">等待启动</p>
                                </div>
                            </div>
                        </div>

                        <!-- 管理员控制区 -->
                        <div class="bg-gray-50 rounded-lg p-4 mb-6 flex justify-between items-center">
                            <span class="text-sm text-gray-600">管理员控制</span>
                            <div class="flex gap-3" id="adminControls">
                                <button class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors" onclick="startPreFillPhase()">
                                    启动会前填写
                                </button>
                            </div>
                        </div>

                        <!-- 阶段0：准备阶段 -->
                        <div id="stage-0" class="decision-stage">
                            <div class="text-center py-12">
                                <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                    <svg class="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"/>
                                    </svg>
                                </div>
                                <h3 class="text-2xl font-bold text-gray-800 mb-4">决策清单准备就绪</h3>
                                <p class="text-gray-600 mb-8 max-w-md mx-auto">
                                    所有问题已准备完毕，等待管理员启动会前填写阶段。参与者将在独立环境中填写问卷。
                                </p>
                                <div class="bg-white rounded-lg p-6 max-w-lg mx-auto">
                                    <h4 class="font-semibold text-gray-700 mb-3">本次决策清单包含：</h4>
                                    <ul class="space-y-2 text-sm text-gray-600">
                                        <li class="flex items-center gap-2">
                                            <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                            市场优先级选择问题
                                        </li>
                                        <li class="flex items-center gap-2">
                                            <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                            预算投入评估问题
                                        </li>
                                        <li class="flex items-center gap-2">
                                            <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                            风险评估问题
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 阶段1：会前填写 -->
                        <div id="stage-1" class="decision-stage hidden">
                            <div class="space-y-6">
                                <!-- 问题1 -->
                                <div class="question-card">
                                    <h4 class="text-lg font-semibold text-gray-800 mb-4">1. 您认为Q1应该优先进入哪个市场？</h4>
                                    <div class="space-y-3">
                                        <label class="option-item">
                                            <input type="radio" name="q1" value="germany" class="mr-3">
                                            <span>德国市场 - 欧洲最大经济体，制造业发达</span>
                                        </label>
                                        <label class="option-item">
                                            <input type="radio" name="q1" value="usa" class="mr-3">
                                            <span>美国市场 - 全球最大消费市场，创新活跃</span>
                                        </label>
                                        <label class="option-item">
                                            <input type="radio" name="q1" value="uk" class="mr-3">
                                            <span>英国市场 - 金融中心，服务业发达</span>
                                        </label>
                                        <label class="option-item">
                                            <input type="radio" name="q1" value="japan" class="mr-3">
                                            <span>日本市场 - 亚洲发达市场，技术领先</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- 问题2 -->
                                <div class="question-card">
                                    <h4 class="text-lg font-semibold text-gray-800 mb-4">2. 初期市场投入预算建议（万元）</h4>
                                    <div class="flex items-center gap-4">
                                        <input type="range" min="50" max="500" value="200" class="flex-1" id="budgetSlider">
                                        <span class="text-xl font-semibold text-blue-600" id="budgetValue">200万元</span>
                                    </div>
                                    <div class="flex justify-between text-sm text-gray-500 mt-2">
                                        <span>50万</span>
                                        <span>500万</span>
                                    </div>
                                </div>

                                <!-- 问题3 -->
                                <div class="question-card">
                                    <h4 class="text-lg font-semibold text-gray-800 mb-4">3. 市场进入风险评估（1-5星）</h4>
                                    <div class="flex items-center gap-2">
                                        <span class="text-sm text-gray-600 mr-4">风险程度：</span>
                                        <div class="flex gap-1" id="riskRating">
                                            <span class="star cursor-pointer text-2xl text-gray-300 hover:text-yellow-500" onclick="setRating(1)">★</span>
                                            <span class="star cursor-pointer text-2xl text-gray-300 hover:text-yellow-500" onclick="setRating(2)">★</span>
                                            <span class="star cursor-pointer text-2xl text-gray-300 hover:text-yellow-500" onclick="setRating(3)">★</span>
                                            <span class="star cursor-pointer text-2xl text-gray-300 hover:text-yellow-500" onclick="setRating(4)">★</span>
                                            <span class="star cursor-pointer text-2xl text-gray-300 hover:text-yellow-500" onclick="setRating(5)">★</span>
                                        </div>
                                        <span class="text-sm text-gray-500 ml-2">(1=低风险，5=高风险)</span>
                                    </div>
                                </div>

                                <!-- 提交按钮 -->
                                <div class="flex justify-center mt-8">
                                    <button class="px-8 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors" onclick="submitPreFillForm()">
                                        提交我的意见
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 阶段2：会中讨论 -->
                        <div id="stage-2" class="decision-stage hidden">
                            <div class="grid grid-cols-12 gap-6">
                                <!-- 左侧：问题与数据可视化 -->
                                <div class="col-span-8">
                                    <div class="bg-white rounded-lg border border-gray-200 p-6">
                                        <!-- 匿名/实名切换 -->
                                        <div class="flex justify-between items-center mb-6">
                                            <h3 class="text-lg font-bold text-gray-800">实时投票结果</h3>
                                            <button class="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium transition-colors" onclick="toggleAnonymous()">
                                                <span id="anonymousToggle">🎭 匿名显示</span>
                                            </button>
                                        </div>

                                        <!-- 当前讨论问题 -->
                                        <div class="mb-6">
                                            <div class="flex items-center gap-3 mb-4">
                                                <span class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</span>
                                                <h4 class="text-lg font-semibold text-gray-800">Q1应该优先进入哪个市场？</h4>
                                            </div>
                                            
                                            <!-- 平均分柱状图 -->
                                            <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                                <h5 class="font-medium text-gray-700 mb-2">市场优先级平均评分</h5>
                                                <div style="height: 200px; position: relative;">
                                                    <canvas id="averageScoreChart"></canvas>
                                                </div>
                                            </div>
                                            
                                            <!-- 详细统计 -->
                                            <div class="grid grid-cols-2 gap-4 mt-4">
                                                <!-- 投票分布饼图 -->
                                                <div class="bg-white p-4 rounded-lg border border-gray-200">
                                                    <h5 class="font-medium text-gray-700 mb-3">投票分布</h5>
                                                    <div class="flex items-center justify-between mb-3">
                                                        <div class="w-20 h-20">
                                                            <canvas id="pieChart"></canvas>
                                                        </div>
                                                        <div class="flex-1 ml-4 space-y-1 text-sm">
                                                            <div class="flex justify-between items-center">
                                                                <div class="flex items-center gap-2">
                                                                    <span class="w-3 h-3 bg-blue-500 rounded-full"></span>
                                                                    <span>🇩🇪 德国</span>
                                                                </div>
                                                                <span class="font-semibold">40%</span>
                                                            </div>
                                                            <div class="flex justify-between items-center">
                                                                <div class="flex items-center gap-2">
                                                                    <span class="w-3 h-3 bg-green-500 rounded-full"></span>
                                                                    <span>🇺🇸 美国</span>
                                                                </div>
                                                                <span class="font-semibold">30%</span>
                                                            </div>
                                                            <div class="flex justify-between items-center">
                                                                <div class="flex items-center gap-2">
                                                                    <span class="w-3 h-3 bg-yellow-500 rounded-full"></span>
                                                                    <span>🇬🇧 英国</span>
                                                                </div>
                                                                <span class="font-semibold">20%</span>
                                                            </div>
                                                            <div class="flex justify-between items-center">
                                                                <div class="flex items-center gap-2">
                                                                    <span class="w-3 h-3 bg-red-500 rounded-full"></span>
                                                                    <span>🇯🇵 日本</span>
                                                                </div>
                                                                <span class="font-semibold">10%</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <!-- 预算建议范围条 -->
                                                <div class="bg-white p-4 rounded-lg border border-gray-200">
                                                    <h5 class="font-medium text-gray-700 mb-3">预算建议分布</h5>
                                                    <div class="mb-4">
                                                        <div class="text-center mb-2">
                                                            <div class="text-2xl font-bold text-blue-600">225万</div>
                                                            <div class="text-sm text-gray-500">平均建议</div>
                                                        </div>
                                                        
                                                        <!-- 预算范围条 -->
                                                        <div class="relative">
                                                            <div class="w-full h-6 bg-gray-200 rounded-full relative overflow-hidden">
                                                                <!-- 范围背景 -->
                                                                <div class="absolute inset-0 bg-gradient-to-r from-blue-200 via-blue-300 to-blue-200 rounded-full"></div>
                                                                <!-- 平均值指示器 -->
                                                                <div class="absolute top-0 h-full w-1 bg-blue-600 rounded-full" style="left: 38.8%;"></div>
                                                            </div>
                                                            
                                                            <!-- 刻度标签 -->
                                                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                                                <span>150万</span>
                                                                <span class="text-blue-600 font-semibold">225万</span>
                                                                <span>350万</span>
                                                            </div>
                                                            
                                                            <!-- 范围标签 -->
                                                            <div class="flex justify-between text-xs text-gray-400 mt-1">
                                                                <span>最低建议</span>
                                                                <span>最高建议</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 右侧：讨论区 -->
                                <div class="col-span-4">
                                    <div class="bg-white rounded-lg border border-gray-200 p-4">
                                        <h4 class="font-semibold text-gray-800 mb-4">实时讨论</h4>
                                        
                                        <!-- 讨论消息 -->
                                        <div class="space-y-4 mb-4 max-h-96 overflow-y-auto" id="discussionMessages">
                                            <div class="flex gap-3">
                                                <img src="assets/avatars/alex-director.svg" alt="Alex" class="w-8 h-8 rounded-full">
                                                <div class="flex-1">
                                                    <div class="flex items-center gap-2 mb-1">
                                                        <span class="font-medium text-sm">Alex</span>
                                                        <span class="text-xs text-gray-500">2分钟前</span>
                                                    </div>
                                                    <p class="text-sm text-gray-700">德国市场确实有优势，但我们需要考虑监管环境的复杂性</p>
                                                    <div class="flex items-center gap-2 mt-1">
                                                        <button class="text-xs text-blue-600 hover:text-blue-800">👍 3</button>
                                                        <button class="text-xs text-gray-500 hover:text-gray-700">回复</button>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="flex gap-3">
                                                <img src="assets/avatars/emma-germany.svg" alt="Emma" class="w-8 h-8 rounded-full">
                                                <div class="flex-1">
                                                    <div class="flex items-center gap-2 mb-1">
                                                        <span class="font-medium text-sm">Emma</span>
                                                        <span class="text-xs text-gray-500">1分钟前</span>
                                                    </div>
                                                    <p class="text-sm text-gray-700">@Alex 同意，但德国的制造业基础和我们的产品契合度很高</p>
                                                    <div class="flex items-center gap-2 mt-1">
                                                        <button class="text-xs text-blue-600 hover:text-blue-800">👍 2</button>
                                                        <button class="text-xs text-gray-500 hover:text-gray-700">回复</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 输入框 -->
                                        <div class="border-t border-gray-200 pt-4">
                                            <div class="flex gap-2">
                                                <input type="text" placeholder="参与讨论..." class="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                <button class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors">发送</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 阶段3：会后决议 -->
                        <div id="stage-3" class="decision-stage hidden">
                            <div class="space-y-6">
                                <div class="bg-green-50 rounded-lg p-6">
                                    <h3 class="text-lg font-bold text-gray-800 mb-2">📋 决议记录</h3>
                                    <p class="text-gray-600">基于会前问卷和会中讨论，请记录最终决议</p>
                                </div>

                                <!-- 问题1决议 -->
                                <div class="question-card">
                                    <h4 class="text-lg font-semibold text-gray-800 mb-4">1. 优先市场选择</h4>
                                    
                                    <!-- 会前结果展示 -->
                                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                        <h5 class="font-medium text-gray-700 mb-2">会前投票结果：</h5>
                                        <div class="grid grid-cols-2 gap-4 text-sm">
                                            <div>德国市场: <span class="font-semibold">40%</span></div>
                                            <div>美国市场: <span class="font-semibold">30%</span></div>
                                            <div>英国市场: <span class="font-semibold">20%</span></div>
                                            <div>日本市场: <span class="font-semibold">10%</span></div>
                                        </div>
                                    </div>
                                    
                                    <!-- 最终决议输入 -->
                                    <div class="border-l-4 border-blue-500 pl-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">最终决议：</label>
                                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="">请选择最终决议...</option>
                                            <option value="germany">确定进入德国市场</option>
                                            <option value="usa">确定进入美国市场</option>
                                            <option value="uk">确定进入英国市场</option>
                                            <option value="japan">确定进入日本市场</option>
                                            <option value="multiple">同时进入多个市场</option>
                                            <option value="delayed">推迟决定</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 问题2决议 -->
                                <div class="question-card">
                                    <h4 class="text-lg font-semibold text-gray-800 mb-4">2. 预算投入决议</h4>
                                    
                                    <!-- 会前结果展示 -->
                                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                        <h5 class="font-medium text-gray-700 mb-2">会前建议统计：</h5>
                                        <div class="text-sm">
                                            <div>平均建议: <span class="font-semibold">225万元</span></div>
                                            <div>建议范围: <span class="font-semibold">150万 - 350万元</span></div>
                                        </div>
                                    </div>
                                    
                                    <!-- 最终决议输入 -->
                                    <div class="border-l-4 border-blue-500 pl-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">最终预算决议：</label>
                                        <div class="flex items-center gap-4">
                                            <input type="number" min="50" max="500" placeholder="输入最终预算" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <span class="text-gray-600">万元</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 决议备注 -->
                                <div class="question-card">
                                    <h4 class="text-lg font-semibold text-gray-800 mb-4">决议备注</h4>
                                    <textarea rows="4" placeholder="记录重要的讨论要点、决议依据或后续行动计划..." class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                                </div>

                                <!-- 保存决议 -->
                                <div class="flex justify-center gap-4 mt-8">
                                    <button class="px-6 py-3 bg-gray-600 text-white rounded-lg font-medium hover:bg-gray-700 transition-colors">
                                        保存草稿
                                    </button>
                                    <button class="px-8 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors" onclick="finalizeDecision()">
                                        确认并发布决议
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 分享模态框 -->
    <div id="shareModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-6 max-w-md w-full mx-4 shadow-2xl">
            <h3 class="text-xl font-bold text-gray-800 mb-4">分享AIP空间</h3>
            <p class="text-gray-600 mb-4">生成分享链接，让其他人可以查看此AIP空间的内容</p>
            
            <div class="bg-gray-50 p-4 rounded-lg mb-4">
                <div class="flex items-center gap-3">
                    <input type="text" value="https://foxu.ai/share/aip/2025q1-expansion" 
                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm" readonly>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors"
                            onclick="copyShareLink()">
                        复制
                    </button>
                </div>
            </div>
            
            <div class="text-sm text-gray-500 mb-4">
                <p class="mb-2">• 桌面端访问者将获得完整只读权限</p>
                <p>• 移动端访问者将看到优化的播客视图</p>
            </div>
            
            <button class="w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
                    onclick="closeShareModal()">
                关闭
            </button>
        </div>
    </div>

    <script>
        // Tab切换功能
        function switchToTab(tabName) {
            // 隐藏所有tab内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有tab按钮的active状态
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的tab
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 设置对应按钮为active
            const buttons = document.querySelectorAll('.tab-button');
            const tabIndex = ['research', 'analysis', 'briefing', 'checklist'].indexOf(tabName);
            if (tabIndex !== -1) {
                buttons[tabIndex].classList.add('active');
            }
        }

        // 播放器控制
        let isPlaying = false;
        function togglePlayPause() {
            const playIcon = document.getElementById('playIcon');
            const pauseIcon = document.getElementById('pauseIcon');
            
            if (isPlaying) {
                playIcon.classList.remove('hidden');
                pauseIcon.classList.add('hidden');
            } else {
                playIcon.classList.add('hidden');
                pauseIcon.classList.remove('hidden');
            }
            isPlaying = !isPlaying;
        }

        function seekAudio(event) {
            const progressBar = event.currentTarget;
            const rect = progressBar.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const width = rect.width;
            const percentage = (x / width) * 100;
            
            progressBar.querySelector('.progress-fill').style.width = percentage + '%';
        }

        // 文件树控制
        function toggleTree(element) {
            element.classList.toggle('collapsed');
            element.classList.toggle('expanded');
            const children = element.nextElementSibling;
            if (children) {
                children.classList.toggle('show');
            }
        }

        // 文件选择
        function selectFile(fileId) {
            // 移除所有高亮
            document.querySelectorAll('.data-table tr').forEach(tr => {
                tr.classList.remove('highlighted');
            });
            
            // 高亮选中的文件
            const selectedRow = document.querySelector(`tr[data-file-id="${fileId}"]`);
            if (selectedRow) {
                selectedRow.classList.add('highlighted');
                
                // 显示预览区
                const preview = document.getElementById('filePreview');
                preview.style.display = 'block';
                preview.querySelector('.text-gray-600').innerHTML = 
                    `<p class="font-semibold mb-2">文件00${fileId} 预览内容</p>
                     <p>这里将显示选中文件的详细内容预览...</p>`;
            }
        }

        // 分析产出树状展开控制
        function toggleAnalysisTree(element, category) {
            element.classList.toggle('collapsed');
            element.classList.toggle('expanded');
            const treeId = category + '-tree';
            const children = document.getElementById(treeId);
            if (children) {
                children.classList.toggle('show');
            }
            
            // 同时显示对应的内容区
            showAnalysisCategory(category);
        }

        // 分析产出分类切换
        function showAnalysisCategory(category) {
            // 隐藏所有内容
            document.querySelectorAll('.analysis-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // 显示选中的分类
            const contentId = category + '-content';
            const content = document.getElementById(contentId);
            if (content) {
                content.classList.remove('hidden');
            }
        }

        // 选择分析文件
        function selectAnalysisFile(category, fileId) {
            // 移除所有高亮
            document.querySelectorAll('#' + category + '-tree .tree-node').forEach(node => {
                node.classList.remove('highlighted');
            });
            
            // 高亮选中的文件
            event.target.classList.add('highlighted');
            
            // 如果是报告或Excel，高亮表格中对应的行
            if (category === 'reports' || category === 'excel') {
                const tables = document.querySelectorAll('.analysis-content:not(.hidden) .data-table tr');
                tables.forEach((tr, index) => {
                    tr.classList.remove('highlighted');
                    if (index === fileId) { // fileId对应表格行索引
                        tr.classList.add('highlighted');
                    }
                });
            }
        }

        // 分享功能
        function showShareModal() {
            document.getElementById('shareModal').classList.remove('hidden');
        }

        function closeShareModal() {
            document.getElementById('shareModal').classList.add('hidden');
        }

        function copyShareLink() {
            const input = document.querySelector('#shareModal input');
            input.select();
            document.execCommand('copy');
            
            // 显示复制成功提示
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = '已复制';
            button.classList.add('bg-green-600');
            
            setTimeout(() => {
                button.textContent = originalText;
                button.classList.remove('bg-green-600');
            }, 2000);
        }

        // 初始化可信度图表
        const ctx = document.getElementById('credibilityChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['高', '中', '低'],
                datasets: [{
                    data: [10, 4, 1],
                    backgroundColor: ['#10b981', '#f59e0b', '#ef4444'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // 默认显示报告内容
        showAnalysisCategory('reports');

        // 决策清单状态管理
        let currentStage = 0;
        let isAnonymous = true;
        let selectedRating = 0;
        let averageScoreChartInstance = null;
        let pieChartInstance = null;

        // 阶段切换函数
        function switchDecisionStage(stage) {
            // 隐藏所有阶段
            document.querySelectorAll('.decision-stage').forEach(stageDiv => {
                stageDiv.classList.add('hidden');
            });
            
            // 显示目标阶段
            document.getElementById(`stage-${stage}`).classList.remove('hidden');
            currentStage = stage;
            
            // 更新状态显示
            updateStageStatus(stage);
            
            // 更新管理员控制按钮
            updateAdminControls(stage);
        }

        // 更新阶段状态显示
        function updateStageStatus(stage) {
            const statusElement = document.getElementById('currentStatus');
            const descriptionElement = document.getElementById('stageDescription');
            const progressElement = document.getElementById('progressInfo');
            
            switch(stage) {
                case 0:
                    statusElement.textContent = '准备中';
                    statusElement.className = 'text-lg font-semibold text-gray-600';
                    descriptionElement.textContent = '等待管理员启动决策流程';
                    progressElement.textContent = '等待启动';
                    break;
                case 1:
                    statusElement.textContent = '会前填写中';
                    statusElement.className = 'text-lg font-semibold text-orange-600';
                    descriptionElement.textContent = '请在会议前完成所有问题的填写，以便会中讨论';
                    progressElement.textContent = '8/10人已完成';
                    break;
                case 2:
                    statusElement.textContent = '会中讨论';
                    statusElement.className = 'text-lg font-semibold text-blue-600';
                    descriptionElement.textContent = '正在进行实时讨论和结果分析';
                    progressElement.textContent = '实时更新中';
                    break;
                case 3:
                    statusElement.textContent = '会后决议';
                    statusElement.className = 'text-lg font-semibold text-green-600';
                    descriptionElement.textContent = '基于讨论结果记录最终决议';
                    progressElement.textContent = '管理员填写中';
                    break;
            }
        }

        // 更新管理员控制按钮
        function updateAdminControls(stage) {
            const controlsElement = document.getElementById('adminControls');
            
            switch(stage) {
                case 0:
                    controlsElement.innerHTML = `
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors" onclick="startPreFillPhase()">
                            启动会前填写
                        </button>
                    `;
                    break;
                case 1:
                    controlsElement.innerHTML = `
                        <button class="px-4 py-2 bg-green-600 text-white rounded-lg text-sm hover:bg-green-700 transition-colors" onclick="startDiscussionPhase()">
                            进入会中讨论
                        </button>
                    `;
                    break;
                case 2:
                    controlsElement.innerHTML = `
                        <button class="px-4 py-2 bg-orange-600 text-white rounded-lg text-sm hover:bg-orange-700 transition-colors" onclick="startDecisionPhase()">
                            进入会后决议
                        </button>
                    `;
                    break;
                case 3:
                    controlsElement.innerHTML = `
                        <button class="px-4 py-2 bg-gray-600 text-white rounded-lg text-sm hover:bg-gray-700 transition-colors" onclick="resetDecisionProcess()">
                            重置流程
                        </button>
                    `;
                    break;
            }
        }

        // 启动会前填写阶段
        function startPreFillPhase() {
            switchDecisionStage(1);
        }

        // 启动会中讨论阶段
        function startDiscussionPhase() {
            switchDecisionStage(2);
            // 初始化投票图表
            initVotingChart();
        }

        // 启动会后决议阶段
        function startDecisionPhase() {
            switchDecisionStage(3);
        }

        // 重置决策流程
        function resetDecisionProcess() {
            switchDecisionStage(0);
        }

        // 星级评分功能
        function setRating(rating) {
            selectedRating = rating;
            const stars = document.querySelectorAll('#riskRating .star');
            stars.forEach((star, index) => {
                if (index < rating) {
                    star.classList.remove('text-gray-300');
                    star.classList.add('text-yellow-500');
                } else {
                    star.classList.remove('text-yellow-500');
                    star.classList.add('text-gray-300');
                }
            });
        }

        // 滑块交互
        document.addEventListener('DOMContentLoaded', function() {
            const budgetSlider = document.getElementById('budgetSlider');
            const budgetValue = document.getElementById('budgetValue');
            
            if (budgetSlider && budgetValue) {
                budgetSlider.addEventListener('input', function() {
                    budgetValue.textContent = this.value + '万元';
                });
            }
        });

        // 提交会前填写表单
        function submitPreFillForm() {
            // 收集表单数据
            const marketChoice = document.querySelector('input[name="q1"]:checked');
            const budgetValue = document.getElementById('budgetSlider').value;
            
            if (!marketChoice) {
                alert('请选择优先市场');
                return;
            }
            
            if (selectedRating === 0) {
                alert('请完成风险评估');
                return;
            }
            
            // 模拟提交成功
            alert('意见提交成功！感谢您的参与。');
            
            // 可以在这里添加实际的数据提交逻辑
            console.log('提交的数据:', {
                market: marketChoice.value,
                budget: budgetValue,
                risk: selectedRating
            });
        }

        // 匿名/实名切换
        function toggleAnonymous() {
            isAnonymous = !isAnonymous;
            const toggleElement = document.getElementById('anonymousToggle');
            
            if (isAnonymous) {
                toggleElement.textContent = '🎭 匿名显示';
            } else {
                toggleElement.textContent = '👤 显示姓名';
            }
            
            // 这里可以添加更新图表显示的逻辑
            updateVotingChart();
        }

        // 初始化投票图表
        function initVotingChart() {
            // 初始化平均分柱状图
            initAverageScoreChart();
            // 初始化饼图
            initPieChart();
        }

        // 初始化平均分柱状图
        function initAverageScoreChart() {
            const ctx = document.getElementById('averageScoreChart');
            if (!ctx) return;
            
            // 销毁现有图表实例
            if (averageScoreChartInstance) {
                averageScoreChartInstance.destroy();
            }
            
            const chartCtx = ctx.getContext('2d');
            averageScoreChartInstance = new Chart(chartCtx, {
                type: 'bar',
                data: {
                    labels: ['德国市场', '美国市场', '英国市场', '日本市场'],
                    datasets: [{
                        label: '平均评分',
                        data: [4.2, 3.8, 3.1, 2.5],
                        backgroundColor: [
                            '#3b82f6',
                            '#10b981', 
                            '#f59e0b',
                            '#ef4444'
                        ],
                        borderRadius: 8,
                        borderSkipped: false,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `平均评分: ${context.parsed.y}分`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 5,
                            ticks: {
                                stepSize: 1,
                                callback: function(value) {
                                    return value + '分';
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    elements: {
                        bar: {
                            borderWidth: 0
                        }
                    }
                }
            });
        }

        // 初始化饼图
        function initPieChart() {
            const ctx = document.getElementById('pieChart');
            if (!ctx) return;
            
            // 销毁现有图表实例
            if (pieChartInstance) {
                pieChartInstance.destroy();
            }
            
            const chartCtx = ctx.getContext('2d');
            pieChartInstance = new Chart(chartCtx, {
                type: 'doughnut',
                data: {
                    labels: ['德国', '美国', '英国', '日本'],
                    datasets: [{
                        data: [40, 30, 20, 10],
                        backgroundColor: [
                            '#3b82f6',
                            '#10b981',
                            '#f59e0b', 
                            '#ef4444'
                        ],
                        borderWidth: 2,
                        borderColor: '#ffffff',
                        cutout: '60%'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.label}: ${context.parsed}%`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // 更新投票图表
        function updateVotingChart() {
            // 这里可以根据匿名/实名状态更新图表显示
            console.log('更新图表显示模式:', isAnonymous ? '匿名' : '实名');
        }

        // 最终确认决议
        function finalizeDecision() {
            if (confirm('确认发布决议吗？发布后将不能修改。')) {
                alert('决议已发布并通知所有参与者！');
                // 这里可以添加实际的决议发布逻辑
            }
        }
    </script>
</body>
</html>
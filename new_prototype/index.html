<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Foxu.AI - 团队情报中心</title>
    <style>
        /* 全局重置和基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f9fafb;
            color: #1f2937;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 背景动画效果 */
        .background-animation {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
            opacity: 0.03;
            overflow: hidden;
        }

        .background-animation::before,
        .background-animation::after {
            content: "";
            position: absolute;
            width: 600px;
            height: 600px;
            border-radius: 50%;
            animation: atmosphericFloat 20s ease-in-out infinite;
        }

        .background-animation::before {
            background: radial-gradient(circle, #667eea 0%, transparent 70%);
            top: -300px;
            left: -300px;
        }

        .background-animation::after {
            background: radial-gradient(circle, #764ba2 0%, transparent 70%);
            bottom: -300px;
            right: -300px;
            animation-delay: -10s;
        }

        @keyframes atmosphericFloat {
            0%, 100% { transform: translate(0, 0) scale(1); }
            33% { transform: translate(30px, -30px) scale(1.1); }
            66% { transform: translate(-20px, 20px) scale(0.9); }
        }

        /* 全局导航栏 */
        .navbar {
            position: sticky;
            top: 0;
            height: 60px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(12px);
            border-bottom: 1px solid #e5e7eb;
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: 700;
            background: linear-gradient(135deg, #2563eb, #9333ea);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-right: 48px;
        }

        .navbar-menu {
            display: flex;
            gap: 32px;
            flex: 1;
        }

        .navbar-item {
            color: #374151;
            text-decoration: none;
            font-size: 15px;
            font-weight: 500;
            transition: color 0.2s;
            position: relative;
        }

        .navbar-item:hover {
            color: #2563eb;
        }

        .navbar-item.active {
            color: #2563eb;
        }

        .navbar-item.active::after {
            content: '';
            position: absolute;
            bottom: -18px;
            left: 0;
            right: 0;
            height: 3px;
            background: #2563eb;
            border-radius: 3px 3px 0 0;
        }

        .navbar-actions {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .notification-badge {
            position: relative;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .notification-badge:hover {
            background: #e5e7eb;
            transform: scale(1.05);
        }

        .notification-badge.has-notifications {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
        }

        .notification-count {
            position: absolute;
            top: -4px;
            right: -4px;
            background: #dc2626;
            color: white;
            font-size: 11px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            border: 2px solid white;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .user-avatar:hover {
            transform: scale(1.05);
        }

        /* 团队绩效概览 */
        .team-performance {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 24px 60px;
        }

        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 24px;
        }

        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            border-radius: 12px;
            color: white;
            position: relative;
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .metric-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .metric-card::after {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: shimmer 3s linear infinite;
        }

        @keyframes shimmer {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .metric-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .metric-value {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .metric-label {
            font-size: 14px;
            opacity: 0.9;
        }

        .metric-trend {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .trend-up { color: #10b981; }
        .trend-down { color: #dc2626; }
        .trend-neutral { color: #6b7280; }

        .quick-actions {
            display: flex;
            gap: 16px;
            justify-content: flex-end;
        }

        .quick-action-btn {
            /* width: 240px; */
            display: flex;
            align-items: center;
            
            gap: 8px;
            padding: 10px 20px;
            border-radius: 8px;
            background: white;
            border: 1px solid #e5e7eb;
            color: #374151;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
        }

        .quick-action-btn:hover {
            background: #f3f4f6;
            border-color: #d1d5db;
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        /* 我的AI团队 */
        .my-ai-team {
            background: #f9fafb;
            padding: 24px 60px;
            border-bottom: 1px solid #e5e7eb;
        }

        .my-team-container {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .team-member-card {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            transition: all 0.3s;
        }

        .team-member-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.07);
        }

        .agent-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .agent-info .agent-name {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
        }

        .certification-status {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 500;
            padding: 2px 8px;
            border-radius: 12px;
        }

        .status-certified {
            background-color: #ecfdf5;
            color: #065f46;
        }

        .status-pending {
            background-color: #fffbeb;
            color: #b45309;
        }

        .team-dialog-btn {
            margin-left: auto;
        }

        .filter-btn {
            padding: 6px 16px;
            border-radius: 6px;
            background: transparent;
            border: 1px solid #e5e7eb;
            color: #374151;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-btn:hover {
            background: #f3f4f6;
        }

        .filter-btn.active {
            background: #e0e7ff;
            border-color: #3b82f6;
            color: #1d4ed8;
            font-weight: 600;
        }
        
        .info-stream-header {
            max-width: 1200px;
            margin: 0 auto 16px auto;
        }

        .info-stream-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        /* 筛选器 */
        .filter-bar {

            background: white;
            height: 60px;
            padding: 0 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #e5e7eb;
            position: sticky;
            top: 60px;
            z-index: 100;
        }

        .filter-group {
            display: flex;
            gap: 12px;
        }

        .filter-select {
            padding: 6px 16px;
            border-radius: 6px;
            background: #f3f4f6;
            border: 1px solid #e5e7eb;
            color: #374151;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 140px;
        }

        .filter-select:hover {
            background: #e5e7eb;
        }

        .filter-select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
        }

        .delegate-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 20px;
            border-radius: 8px;
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            border: none;
            transition: all 0.3s;
            text-decoration: none;
        }

        .delegate-btn:hover {
            box-shadow: 0 10px 20px rgba(37, 99, 235, 0.3);
            transform: translateY(-2px);
        }

        /* 信息流区域 */
        .info-stream {
            padding: 24px;
            min-height: calc(100vh - 340px);
        }

        .info-stream-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* 通用卡片样式 */
        .info-card {
            background: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            transition: all 0.3s;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .info-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* AIP空间卡片 */
        .aip-card {
            position: relative;
        }

        .aip-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .aip-meta {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .ai-agent-badge {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: #6b7280;
        }

        .knowledge-tag {
            padding: 4px 12px;
            background: #ede9fe;
            color: #7c3aed;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .aip-content {
            margin-bottom: 20px;
        }

        .aip-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
        }

        .audio-player {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            background: #f9fafb;
            border-radius: 8px;
            margin-bottom: 12px;
        }

        .play-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
        }

        .play-icon, .pause-icon {
            width: 16px;
            height: 16px;
        }

        .play-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
        }

        .progress-bar {
            flex: 1;
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            position: relative;
            cursor: pointer;
        }

        .progress-bar-fill {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 35%;
            background: linear-gradient(90deg, #3b82f6, #2563eb);
            border-radius: 2px;
            transition: width 0.3s;
        }

        .core-insights {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
        }

        .insight-item {
            flex: 1;
            padding: 12px;
            background: #f0f9ff;
            border-left: 3px solid #3b82f6;
            border-radius: 4px;
        }

        .insight-label {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 4px;
        }

        .insight-value {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .aip-actions {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            border-radius: 6px;
            background: #f3f4f6;
            border: 1px solid #e5e7eb;
            color: #374151;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .action-btn:hover {
            background: #e5e7eb;
            transform: translateY(-1px);
        }

        .interaction-stats {
            display: flex;
            gap: 16px;
        }

        .interaction-btn {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 6px 12px;
            border-radius: 6px;
            background: transparent;
            border: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .interaction-btn:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .interaction-btn.active {
            background: #fef3c7;
            border-color: #fbbf24;
            color: #92400e;
        }

        /* 情报预警卡片 */
        .alert-card {
            position: relative;
            border-left-width: 4px;
        }

        .alert-card.high-priority {
            border-left-color: #dc2626;
            background: #fef2f2;
        }

        .alert-card.medium-priority {
            border-left-color: #f59e0b;
            background: #fffbeb;
        }

        .alert-card.opportunity {
            border-left-color: #10b981;
            background: #f0fdf4;
        }

        .alert-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .alert-icon {
            font-size: 24px;
        }

        .alert-meta {
            flex: 1;
        }

        .alert-agent {
            font-size: 14px;
            color: #6b7280;
        }

        .alert-level {
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .alert-content {
            margin-bottom: 16px;
        }

        .alert-title {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .alert-summary {
            font-size: 14px;
            color: #374151;
            line-height: 1.5;
        }

        .alert-actions {
            display: flex;
            gap: 12px;
        }

        /* 用户活动卡片 */
        .activity-card {
            padding: 16px 24px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .activity-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .activity-content {
            flex: 1;
            font-size: 14px;
            color: #374151;
        }

        .activity-user {
            font-weight: 600;
            color: #1f2937;
        }

        .activity-target {
            color: #2563eb;
            text-decoration: none;
            font-weight: 500;
        }

        .activity-target:hover {
            text-decoration: underline;
        }

        .activity-time {
            font-size: 12px;
            color: #9ca3af;
        }

        /* 响应式设计 */
        @media (max-width: 1199px) {
            .performance-metrics {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 767px) {
            .navbar-menu {
                display: none;
            }

            .performance-metrics {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .quick-actions {
                flex-direction: column;
            }

            .filter-group {
                flex-wrap: wrap;
            }

            .core-insights {
                flex-direction: column;
            }

            .action-buttons {
                flex-direction: column;
                width: 100%;
            }

            .action-btn {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- 背景动画 -->
    <div class="background-animation"></div>

    <!-- 全局导航栏 -->
    <nav class="navbar">
        <div class="navbar-brand">Foxu.AI</div>
        <div class="navbar-menu">
            <a href="#" class="navbar-item active">首页</a>
            <a href="task_delegation.html" class="navbar-item">任务委托中心</a>
            <a href="knowledge_base_explorer.html" class="navbar-item">知识库</a>
            <a href="cadre_competency_learning.html" class="navbar-item">干部能力与学习系统</a>
            <a href="cadre_competency_admin.html" class="navbar-item">干部能力配置</a>
            <a href="team_management.html" class="navbar-item">企业管理</a>
        </div>
        <div class="navbar-actions">
            <div class="notification-badge has-notifications">
                <span>🔔</span>
                <span class="notification-count">3</span>
            </div>
            <div class="user-avatar">JD</div>
        </div>
    </nav>

    <!-- 团队绩效概览 -->
    <section class="team-performance">
        <div class="performance-metrics">
            <div class="metric-card">
                <div class="metric-icon">💰</div>
                <div class="metric-value">¥3.2万</div>
                <div class="metric-label">月度投入成本</div>
                <div class="metric-trend trend-down">↘️ -12%</div>
            </div>
            <div class="metric-card">
                <div class="metric-icon">💎</div>
                <div class="metric-value">¥235.3万</div>
                <div class="metric-label">累计创造价值</div>
                <div class="metric-trend trend-up">↗️ +28%</div>
            </div>
            <div class="metric-card">
                <div class="metric-icon">📈</div>
                <div class="metric-value">73.5倍</div>
                <div class="metric-label">投资回报率 (ROI)</div>
                <div class="metric-trend trend-up">↗️ +15%</div>
            </div>
            <div class="metric-card">
                <div class="metric-icon">✅</div>
                <div class="metric-value">23个</div>
                <div class="metric-label">本月完成任务</div>
                <div class="metric-trend trend-neutral">➡️ 持平</div>
            </div>
        </div>
        <div class="quick-actions">
            <a href="#" class="quick-action-btn">
                <span>📊</span>
                <span>管理我的AI团队</span>
            </a>
            <a href="#" class="quick-action-btn">
                <span>💬</span>
                <span>与AI对话</span>
            </a>
            <a href="#" class="quick-action-btn">
                <span>🎯</span>
                <span>发起新分析</span>
            </a>
            <a href="#" class="quick-action-btn">
                <span>📈</span>
                <span>详细报告</span>
            </a>
        </div>
    </section>

    <!-- 我的AI团队 -->
    <section class="my-ai-team">
        <div class="my-team-container">
            <div class="team-member-card">
                <div class="agent-avatar">🌍</div>
                <div class="agent-info">
                    <div class="agent-name">全球战略官A</div>
                    <div class="certification-status status-certified"><span>✅</span><span>已认证</span></div>
                </div>
            </div>
            <div class="team-member-card">
                <div class="agent-avatar">🎯</div>
                <div class="agent-info">
                    <div class="agent-name">德国市场顾问</div>
                    <div class="certification-status status-pending"><span>⚠️</span><span>待学习</span></div>
                </div>
            </div>
            <a href="#" class="quick-action-btn team-dialog-btn">
                <span>💬</span>
                <span>与我的团队对话</span>
            </a>
        </div>
    </section>

    <!-- 筛选器 -->
    <div class="filter-bar">
        <div class="filter-group">
            <select class="filter-select" id="agentFilter">
                <option value="all">全部AI代理</option>
                <option value="global-strategist">全球战略官</option>
                <option value="market-expert">市场拓展专家</option>
                <option value="intel-officer">市场情报官</option>
            </select>
            <select class="filter-select" id="productLineFilter">
                <option value="all">全部产品线</option>
                <option value="product-a">产品线A</option>
                <option value="product-b">产品线B</option>
                <option value="product-c">产品线C</option>
            </select>
            <select class="filter-select" id="knowledgeFilter">
                <option value="all">全部知识库</option>
                <option value="strategy">企业战略库</option>
                <option value="market">目标市场库</option>
                <option value="product">产品库</option>
                <option value="company">公司库</option>
            </select>
        </div>
        <a href="task_delegation.html" class="delegate-btn">
            <span>+</span>
            <span>任务委托中心</span>
        </a>
    </div>

    <!-- 信息流区域 -->
    <main class="info-stream">
        <div class="info-stream-header">
            <h3>来自您的AI团队的动态</h3>
        </div>
        <div class="info-stream-container">
            <!-- AIP空间卡片 -->
            <div class="info-card aip-card">
                <div class="aip-header">
                    <div class="aip-meta">
                        <div class="ai-agent-badge">
                            <span>🌍</span>
                            <span>全球战略官</span>
                        </div>
                        <span class="knowledge-tag">企业战略库</span>
                    </div>
                </div>
                <div class="aip-content">
                    <h3 class="aip-title">A2 - 全球商机扫描报告</h3>
                    <div class="audio-player">
                        <button class="play-btn">
                            <svg class="play-icon" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8 5v14l11-7z" />
                            </svg>
                        </button>
                        <div class="progress-bar">
                            <div class="progress-bar-fill"></div>
                        </div>
                        <span style="font-size: 12px; color: #6b7280;">2:35</span>
                    </div>
                    <div class="core-insights">
                        <div class="insight-item">
                            <div class="insight-label">核心洞察</div>
                            <div class="insight-value">德国市场准入窗口期仅剩6个月</div>
                        </div>
                        <div class="insight-item">
                            <div class="insight-label">关键建议</div>
                            <div class="insight-value">立即启动市场进入计划</div>
                        </div>
                    </div>
                </div>
                <div class="aip-actions">
                    <div class="action-buttons">
                        <a href="aip_space.html" class="action-btn">
                            <span>📂</span>
                            <span>查看AIP空间</span>
                        </a>
                        <a href="#" class="action-btn">
                            <span>🖥️</span>
                            <span>打开决策简报</span>
                        </a>
                        <a href="#" class="action-btn">
                            <span>💬</span>
                            <span>与AI讨论</span>
                        </a>
                    </div>
                    <div class="interaction-stats">
                        <button class="interaction-btn">
                            <span>👍</span>
                            <span>12</span>
                        </button>
                        <button class="interaction-btn active">
                            <span>⭐</span>
                            <span>8</span>
                        </button>
                        <button class="interaction-btn">
                            <span>💬</span>
                            <span>3</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 高优先级预警卡片 -->
            <div class="info-card alert-card high-priority">
                <div class="alert-header">
                    <span class="alert-icon">🚨</span>
                    <div class="alert-meta">
                        <div class="alert-agent">市场情报官</div>
                        <div class="alert-level" style="color: #dc2626;">紧急预警</div>
                    </div>
                </div>
                <div class="alert-content">
                    <h4 class="alert-title">主要竞争对手宣布重大收购</h4>
                    <p class="alert-summary">德国竞品公司Techno GmbH宣布收购本地渠道商，可能影响我们Q2市场进入策略。需要立即调整市场策略并评估影响。</p>
                </div>
                <div class="alert-actions">
                    <a href="#" class="action-btn">
                        <span>📄</span>
                        <span>查看详情</span>
                    </a>
                    <a href="#" class="action-btn">
                        <span>👥</span>
                        <span>指派给同事</span>
                    </a>
                    <a href="#" class="action-btn">
                        <span>💬</span>
                        <span>与AI讨论</span>
                    </a>
                </div>
            </div>

            <!-- 中优先级预警卡片 -->
            <div class="info-card alert-card medium-priority">
                <div class="alert-header">
                    <span class="alert-icon">⚠️</span>
                    <div class="alert-meta">
                        <div class="alert-agent">市场情报官</div>
                        <div class="alert-level" style="color: #f59e0b;">重要通知</div>
                    </div>
                </div>
                <div class="alert-content">
                    <h4 class="alert-title">欧盟新数据保护法规即将生效</h4>
                    <p class="alert-summary">2025年Q3起，欧盟将实施新的数据保护法规，需要提前3个月完成合规准备工作。</p>
                </div>
                <div class="alert-actions">
                    <a href="#" class="action-btn">
                        <span>📄</span>
                        <span>查看详情</span>
                    </a>
                    <a href="#" class="action-btn">
                        <span>👥</span>
                        <span>指派给同事</span>
                    </a>
                    <a href="#" class="action-btn">
                        <span>💬</span>
                        <span>与AI讨论</span>
                    </a>
                </div>
            </div>

            <!-- 用户活动卡片 -->
            <div class="info-card activity-card">
                <div class="activity-avatar">LW</div>
                <div class="activity-content">
                    <span class="activity-user">李伟</span>
                    <span>收藏了</span>
                    <a href="#" class="activity-target">《东南亚市场扩张机会分析》</a>
                </div>
                <div class="activity-time">5分钟前</div>
            </div>

            <!-- 机会型预警卡片 -->
            <div class="info-card alert-card opportunity">
                <div class="alert-header">
                    <span class="alert-icon">💡</span>
                    <div class="alert-meta">
                        <div class="alert-agent">市场拓展专家</div>
                        <div class="alert-level" style="color: #10b981;">市场机会</div>
                    </div>
                </div>
                <div class="alert-content">
                    <h4 class="alert-title">新加坡政府推出科技企业扶持计划</h4>
                    <p class="alert-summary">新加坡政府宣布为AI企业提供税收优惠和资金支持，申请窗口期为3个月，与我们的产品高度匹配。</p>
                </div>
                <div class="alert-actions">
                    <a href="#" class="action-btn">
                        <span>📄</span>
                        <span>查看详情</span>
                    </a>
                    <a href="#" class="action-btn">
                        <span>👥</span>
                        <span>指派给同事</span>
                    </a>
                    <a href="#" class="action-btn">
                        <span>💬</span>
                        <span>与AI讨论</span>
                    </a>
                </div>
            </div>

            <!-- AIP空间卡片2 -->
            <div class="info-card aip-card">
                <div class="aip-header">
                    <div class="aip-meta">
                        <div class="ai-agent-badge">
                            <span>🎯</span>
                            <span>市场拓展专家</span>
                        </div>
                        <span class="knowledge-tag">目标市场库</span>
                    </div>
                </div>
                <div class="aip-content">
                    <h3 class="aip-title">东南亚市场进入策略分析</h3>
                    <div class="audio-player">
                        <button class="play-btn">
                            <svg class="play-icon" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8 5v14l11-7z" />
                            </svg>
                        </button>
                        <div class="progress-bar">
                            <div class="progress-bar-fill" style="width: 0%;"></div>
                        </div>
                        <span style="font-size: 12px; color: #6b7280;">3:48</span>
                    </div>
                    <div class="core-insights">
                        <div class="insight-item">
                            <div class="insight-label">市场规模</div>
                            <div class="insight-value">预计2025年达$850亿</div>
                        </div>
                        <div class="insight-item">
                            <div class="insight-label">进入难度</div>
                            <div class="insight-value">中等 (评分: 6.5/10)</div>
                        </div>
                    </div>
                </div>
                <div class="aip-actions">
                    <div class="action-buttons">
                        <a href="aip_space.html" class="action-btn">
                            <span>📂</span>
                            <span>查看AIP空间</span>
                        </a>
                        <a href="#" class="action-btn">
                            <span>🖥️</span>
                            <span>打开决策简报</span>
                        </a>
                        <a href="#" class="action-btn">
                            <span>💬</span>
                            <span>与AI讨论</span>
                        </a>
                    </div>
                    <div class="interaction-stats">
                        <button class="interaction-btn">
                            <span>👍</span>
                            <span>24</span>
                        </button>
                        <button class="interaction-btn">
                            <span>⭐</span>
                            <span>15</span>
                        </button>
                        <button class="interaction-btn">
                            <span>💬</span>
                            <span>7</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 用户活动卡片2 -->
            <div class="info-card activity-card">
                <div class="activity-avatar">ZY</div>
                <div class="activity-content">
                    <span class="activity-user">张悦</span>
                    <span>评论了</span>
                    <a href="#" class="activity-target">《德国市场准入窗口期分析》</a>
                    <span>："建议重点关注法规合规问题"</span>
                </div>
                <div class="activity-time">15分钟前</div>
            </div>

            <!-- 用户活动卡片3 -->
            <div class="info-card activity-card">
                <div class="activity-avatar">CX</div>
                <div class="activity-content">
                    <span class="activity-user">陈星</span>
                    <span>点赞了</span>
                    <a href="#" class="activity-target">《Q2产品线竞争态势报告》</a>
                </div>
                <div class="activity-time">30分钟前</div>
            </div>
        </div>
    </main>

    <script>
        // 播放按钮交互
        document.querySelectorAll('.play-btn').forEach(btn => {
            let isPlaying = false;
            btn.addEventListener('click', function() {
                isPlaying = !isPlaying;
                if (isPlaying) {
                    this.innerHTML = `
                        <svg class="pause-icon" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z" />
                        </svg>
                    `;
                } else {
                    this.innerHTML = `
                        <svg class="play-icon" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M8 5v14l11-7z" />
                        </svg>
                    `;
                }
            });
        });

        // 筛选器交互
        document.querySelectorAll('.filter-select').forEach(select => {
            select.addEventListener('change', function() {
                console.log(`${this.id} 已选择: ${this.value}`);
                // 这里可以添加筛选逻辑
            });
        });

        // 互动按钮交互
        document.querySelectorAll('.interaction-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.classList.toggle('active');
                const countSpan = this.querySelector('span:last-child');
                const currentCount = parseInt(countSpan.textContent);
                countSpan.textContent = this.classList.contains('active') ? currentCount + 1 : currentCount - 1;
            });
        });

        // 进度条交互
        document.querySelectorAll('.progress-bar').forEach(bar => {
            bar.addEventListener('click', function(e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const width = rect.width;
                const percentage = (x / width) * 100;
                this.querySelector('.progress-bar-fill').style.width = percentage + '%';
            });
        });

        // 通知徽章动画
        const notificationBadge = document.querySelector('.notification-badge');
        setInterval(() => {
            notificationBadge.style.transform = 'scale(1.1)';
            setTimeout(() => {
                notificationBadge.style.transform = 'scale(1)';
            }, 200);
        }, 5000);
    </script>
</body>
</html>
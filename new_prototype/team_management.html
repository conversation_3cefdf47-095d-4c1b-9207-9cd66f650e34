<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Foxu.AI - 企业管理中心</title>
    <style>
        /* 全局重置和基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f9fafb;
            color: #1f2937;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 背景动画效果 */
        .background-animation {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
            opacity: 0.03;
            overflow: hidden;
        }

        .background-animation::before,
        .background-animation::after {
            content: "";
            position: absolute;
            width: 600px;
            height: 600px;
            border-radius: 50%;
            animation: atmosphericFloat 20s ease-in-out infinite;
        }

        .background-animation::before {
            background: radial-gradient(circle, #667eea 0%, transparent 70%);
            top: -300px;
            left: -300px;
        }

        .background-animation::after {
            background: radial-gradient(circle, #764ba2 0%, transparent 70%);
            bottom: -300px;
            right: -300px;
            animation-delay: -10s;
        }

        @keyframes atmosphericFloat {
            0%, 100% { transform: translate(0, 0) scale(1); }
            33% { transform: translate(30px, -30px) scale(1.1); }
            66% { transform: translate(-20px, 20px) scale(0.9); }
        }

        /* 全局导航栏 */
        .navbar {
            position: sticky;
            top: 0;
            height: 60px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(12px);
            border-bottom: 1px solid #e5e7eb;
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: 700;
            background: linear-gradient(135deg, #2563eb, #9333ea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-right: 48px;
        }

        .navbar-menu {
            display: flex;
            gap: 32px;
            flex: 1;
        }

        .navbar-item {
            color: #374151;
            text-decoration: none;
            font-size: 15px;
            font-weight: 500;
            transition: color 0.2s;
            position: relative;
        }

        .navbar-item:hover {
            color: #2563eb;
        }

        .navbar-item.active {
            color: #2563eb;
        }

        .navbar-item.active::after {
            content: '';
            position: absolute;
            bottom: -18px;
            left: 0;
            right: 0;
            height: 3px;
            background: #2563eb;
            border-radius: 3px 3px 0 0;
        }

        .navbar-actions {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .notification-badge {
            position: relative;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .notification-badge:hover {
            background: #e5e7eb;
            transform: scale(1.05);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .user-avatar:hover {
            transform: scale(1.05);
        }

        /* 主内容区域布局 */
        .main-container {
            display: flex;
            height: calc(100vh - 60px);
        }

        /* 左侧导航栏 */
        .sidebar {
            width: 25%;
            min-width: 320px;
            background: white;
            border-right: 1px solid #e5e7eb;
            overflow-y: auto;
            padding: 24px;
        }

        .sidebar-header {
            margin-bottom: 24px;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .sidebar-subtitle {
            font-size: 14px;
            color: #6b7280;
        }

        /* 导航菜单 */
        .nav-menu {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s;
            border: 2px solid transparent;
            background: #f9fafb;
        }

        .nav-item:hover {
            background: #f3f4f6;
            border-color: #e5e7eb;
        }

        .nav-item.active {
            background: #eff6ff;
            border-color: #3b82f6;
            color: #1d4ed8;
        }

        .nav-item-icon {
            font-size: 24px;
            margin-right: 16px;
            min-width: 32px;
        }

        .nav-item-content {
            flex: 1;
        }

        .nav-item-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .nav-item-description {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
        }

        .nav-item-stats {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 4px;
        }

        .nav-item-number {
            font-size: 18px;
            font-weight: 700;
            color: #2563eb;
        }

        .nav-item-label {
            font-size: 10px;
            color: #6b7280;
            text-align: right;
        }

        /* 右侧内容区域 */
        .content {
            flex: 1;
            background: #f9fafb;
            overflow-y: auto;
            padding: 24px;
        }

        /* 模块卡片样式 */
        .module-card {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            margin: 0 auto;
        }

        .module-header {
            display: flex;
            align-items: center;
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 2px solid #f3f4f6;
        }

        .module-icon {
            font-size: 48px;
            margin-right: 24px;
        }

        .module-info {
            flex: 1;
        }

        .module-title {
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .module-description {
            font-size: 16px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 成员管理界面样式 */
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding: 16px 20px;
            background: #f8fafc;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
        }

        .toolbar-left {
            display: flex;
            gap: 12px;
        }

        .toolbar-right {
            position: relative;
            width: 300px;
        }

        .btn {
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .search-input {
            width: 100%;
            padding: 10px 40px 10px 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .search-input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
        }

        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table thead {
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
        }

        .table th {
            padding: 16px;
            text-align: left;
            font-size: 12px;
            font-weight: 700;
            color: #374151;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table td {
            padding: 16px;
            border-bottom: 1px solid #f3f4f6;
            font-size: 14px;
        }

        .table tbody tr:hover {
            background: #f9fafb;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-checkbox {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .user-details {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 2px;
        }

        .user-email {
            font-size: 12px;
            color: #6b7280;
        }

        .role-badge {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
        }

        .role-super-admin {
            background: #fef3c7;
            color: #92400e;
        }

        .role-admin {
            background: #dbeafe;
            color: #1e40af;
        }

        .role-member {
            background: #d1fae5;
            color: #065f46;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-active .status-dot {
            background: #10b981;
        }

        .status-paused .status-dot {
            background: #f59e0b;
        }

        .status-pending .status-dot {
            background: #6b7280;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .permissions-summary {
            font-size: 12px;
            color: #6b7280;
        }

        .action-btn {
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            background: #f3f4f6;
            color: #374151;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
        }

        .action-btn:hover {
            background: #e5e7eb;
            color: #1f2937;
        }

        /* 计费管理界面样式 */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }

        .dashboard-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .dashboard-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .dashboard-card-icon {
            font-size: 24px;
            margin-right: 12px;
        }

        .dashboard-card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .package-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .package-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
        }

        .package-label {
            color: #6b7280;
        }

        .package-value {
            font-weight: 600;
            color: #1f2937;
        }

        .usage-progress {
            margin: 16px 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f3f4f6;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #2563eb);
            transition: width 0.3s ease;
        }

        .progress-text {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: #f8fafc;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }

        .stat-title {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 8px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
        }

        .stat-change {
            font-size: 12px;
            color: #10b981;
            margin-top: 4px;
        }

        .warning-section {
            background: #fef7cd;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 16px;
            margin-top: 20px;
        }

        .warning-title {
            display: flex;
            align-items: center;
            font-weight: 600;
            color: #92400e;
            margin-bottom: 8px;
        }

        .warning-list {
            list-style: none;
            font-size: 14px;
            color: #78350f;
        }

        .warning-list li {
            margin: 4px 0;
            padding-left: 16px;
            position: relative;
        }

        .warning-list li::before {
            content: '•';
            position: absolute;
            left: 0;
            color: #f59e0b;
        }

        /* 系统设置界面样式 */
        .settings-section {
            margin-bottom: 32px;
        }

        .settings-header {
            margin-bottom: 24px;
        }

        .settings-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .settings-description {
            font-size: 14px;
            color: #6b7280;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-input,
        .form-select {
            width: 100%;
            padding: 10px 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .settings-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e5e7eb;
        }

        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .modal-overlay.active {
            display: flex;
        }

        .modal {
            background: white;
            border-radius: 16px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
            padding: 24px 32px;
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 700;
            color: #1f2937;
        }

        .modal-body {
            flex: 1;
            overflow-y: auto;
            padding: 32px;
        }

        .modal-footer {
            padding: 24px 32px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        /* 响应式设计 */
        @media (max-width: 1199px) {
            .sidebar {
                width: 30%;
                min-width: 280px;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 767px) {
            .navbar-menu {
                display: none;
            }

            .main-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                border-right: none;
                border-bottom: 1px solid #e5e7eb;
            }

            .content {
                padding: 16px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .checkbox-group {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 背景动画 -->
    <div class="background-animation"></div>

    <!-- 全局导航栏 -->
    <nav class="navbar">
        <div class="navbar-brand">Foxu.AI</div>
        <div class="navbar-menu">
            <a href="index.html" class="navbar-item">首页</a>
            <a href="task_delegation.html" class="navbar-item">任务委托中心</a>
            <a href="knowledge_base_explorer.html" class="navbar-item">知识库</a>
            <a href="cadre_competency_learning.html" class="navbar-item">干部能力与学习系统</a>
            <a href="cadre_competency_admin.html" class="navbar-item">干部能力配置</a>
            <a href="#" class="navbar-item active">企业管理</a>
        </div>
        <div class="navbar-actions">
            <div class="notification-badge">
                <span>🔔</span>
            </div>
            <div class="user-avatar">JD</div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-container">
        <!-- 左侧导航栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1 class="sidebar-title">🏢 企业管理中心</h1>
                <p class="sidebar-subtitle">管理团队成员、权限分配、计费监控和系统设置</p>
            </div>

            <div class="nav-menu">
                <div class="nav-item active" data-module="members">
                    <div class="nav-item-icon">👥</div>
                    <div class="nav-item-content">
                        <div class="nav-item-title">成员与权限</div>
                        <div class="nav-item-description">管理团队成员、分配AI代理权限、设置角色</div>
                    </div>
                    <div class="nav-item-stats">
                        <div class="nav-item-number">15</div>
                        <div class="nav-item-label">活跃成员</div>
                    </div>
                </div>

                <div class="nav-item" data-module="billing">
                    <div class="nav-item-icon">💰</div>
                    <div class="nav-item-content">
                        <div class="nav-item-title">计费与点数</div>
                        <div class="nav-item-description">查看套餐信息、监控点数使用、成本分析</div>
                    </div>
                    <div class="nav-item-stats">
                        <div class="nav-item-number">73%</div>
                        <div class="nav-item-label">本月使用率</div>
                    </div>
                </div>

                <div class="nav-item" data-module="settings">
                    <div class="nav-item-icon">⚙️</div>
                    <div class="nav-item-content">
                        <div class="nav-item-title">系统设置</div>
                        <div class="nav-item-description">企业信息配置、通知偏好、界面个性化</div>
                    </div>
                    <div class="nav-item-stats">
                        <div class="nav-item-number">5</div>
                        <div class="nav-item-label">待配置项</div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- 右侧内容区域 -->
        <main class="content" id="contentArea">
            <!-- 成员与权限管理界面 -->
            <div class="module-card" id="membersModule">
                <div class="module-header">
                    <span class="module-icon">👥</span>
                    <div class="module-info">
                        <h2 class="module-title">成员与权限管理</h2>
                        <p class="module-description">管理企业团队成员，分配AI代理访问权限，设置用户角色和状态</p>
                    </div>
                </div>

                <!-- 操作工具栏 -->
                <div class="toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="openInviteModal()">
                            <span>➕</span> 邀请成员
                        </button>
                        <button class="btn btn-secondary">
                            <span>📁</span> 批量导入
                        </button>
                        <button class="btn btn-secondary">
                            <span>🔄</span> 同步权限
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <input type="text" class="search-input" placeholder="搜索成员...">
                        <span class="search-icon">🔍</span>
                    </div>
                </div>

                <!-- 成员列表表格 -->
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" class="user-checkbox">
                                </th>
                                <th>姓名/邮箱</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>AI代理权限</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <input type="checkbox" class="user-checkbox">
                                </td>
                                <td>
                                    <div class="user-info">
                                        <div class="user-details">
                                            <div class="user-name">张三</div>
                                            <div class="user-email"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="role-badge role-super-admin">超级管理员</span>
                                </td>
                                <td>
                                    <div class="status-indicator status-active">
                                        <div class="status-dot"></div>
                                        <span>活跃</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="permissions-summary">
                                        全部代理<br>
                                        <span style="color: #6b7280; font-size: 11px;">(3个产品线)</span>
                                    </div>
                                </td>
                                <td>
                                    <button class="action-btn" onclick="openPermissionModal('张三', '<EMAIL>')">编辑</button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input type="checkbox" class="user-checkbox">
                                </td>
                                <td>
                                    <div class="user-info">
                                        <div class="user-details">
                                            <div class="user-name">李四</div>
                                            <div class="user-email"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="role-badge role-admin">管理员</span>
                                </td>
                                <td>
                                    <div class="status-indicator status-active">
                                        <div class="status-dot"></div>
                                        <span>活跃</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="permissions-summary">
                                        部分代理<br>
                                        <span style="color: #6b7280; font-size: 11px;">(2个产品线)</span>
                                    </div>
                                </td>
                                <td>
                                    <button class="action-btn" onclick="openPermissionModal('李四', '<EMAIL>')">编辑</button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input type="checkbox" class="user-checkbox">
                                </td>
                                <td>
                                    <div class="user-info">
                                        <div class="user-details">
                                            <div class="user-name">王五</div>
                                            <div class="user-email"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="role-badge role-member">成员</span>
                                </td>
                                <td>
                                    <div class="status-indicator status-paused">
                                        <div class="status-dot"></div>
                                        <span>暂停</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="permissions-summary">
                                        市场专家<br>
                                        <span style="color: #6b7280; font-size: 11px;">(产品线A)</span>
                                    </div>
                                </td>
                                <td>
                                    <button class="action-btn" onclick="openPermissionModal('王五', '<EMAIL>')">编辑</button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input type="checkbox" class="user-checkbox">
                                </td>
                                <td>
                                    <div class="user-info">
                                        <div class="user-details">
                                            <div class="user-name">赵六</div>
                                            <div class="user-email"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="role-badge role-member">成员</span>
                                </td>
                                <td>
                                    <div class="status-indicator status-pending">
                                        <div class="status-dot"></div>
                                        <span>待激活</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="permissions-summary">
                                        无权限<br>
                                        <span style="color: #ef4444; font-size: 11px;">邀请链接: 2天后过期</span>
                                    </div>
                                </td>
                                <td>
                                    <button class="action-btn" onclick="openPermissionModal('赵六', '<EMAIL>')">编辑</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- 邀请成员弹窗 -->
    <div class="modal-overlay" id="inviteModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">邀请新成员</h3>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">邀请方式</label>
                    <div style="display: flex; flex-direction: column; gap: 16px;">
                        <label style="display: flex; align-items: center; gap: 8px; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; cursor: pointer;">
                            <input type="radio" name="inviteMethod" value="email" checked>
                            <div>
                                <div style="font-weight: 600;">📧 邮件邀请</div>
                                <div style="font-size: 12px; color: #6b7280;">通过邮箱地址发送邀请链接</div>
                            </div>
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; cursor: pointer;">
                            <input type="radio" name="inviteMethod" value="link">
                            <div>
                                <div style="font-weight: 600;">🔗 邀请链接</div>
                                <div style="font-size: 12px; color: #6b7280;">生成带有过期时间的邀请链接</div>
                            </div>
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; cursor: pointer;">
                            <input type="radio" name="inviteMethod" value="batch">
                            <div>
                                <div style="font-weight: 600;">📁 批量邀请</div>
                                <div style="font-size: 12px; color: #6b7280;">上传CSV文件批量导入成员信息</div>
                            </div>
                        </label>
                    </div>
                </div>
                
                <div class="form-group" id="emailInviteForm">
                    <label class="form-label">邮箱地址</label>
                    <input type="email" class="form-input" placeholder="<EMAIL>">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeInviteModal()">取消</button>
                <button class="btn btn-primary">发送邀请</button>
            </div>
        </div>
    </div>

    <!-- 权限编辑弹窗 -->
    <div class="modal-overlay" id="permissionModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title" id="permissionModalTitle">编辑成员权限</h3>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">基本信息</label>
                    <div class="form-grid">
                        <div>
                            <label class="form-label" style="font-size: 12px; margin-bottom: 4px;">姓名</label>
                            <input type="text" class="form-input" id="memberName" placeholder="李四">
                        </div>
                        <div>
                            <label class="form-label" style="font-size: 12px; margin-bottom: 4px;">邮箱</label>
                            <input type="email" class="form-input" id="memberEmail" placeholder="<EMAIL>">
                        </div>
                    </div>
                    <div class="form-grid" style="margin-top: 12px;">
                        <div>
                            <label class="form-label" style="font-size: 12px; margin-bottom: 4px;">部门</label>
                            <input type="text" class="form-input" placeholder="市场部">
                        </div>
                        <div>
                            <label class="form-label" style="font-size: 12px; margin-bottom: 4px;">职位</label>
                            <input type="text" class="form-input" placeholder="市场总监">
                        </div>
                    </div>
                    <div style="margin-top: 12px;">
                        <label class="form-label" style="font-size: 12px; margin-bottom: 8px;">状态管理</label>
                        <div style="display: flex; gap: 16px; align-items: center;">
                            <label style="display: flex; align-items: center; gap: 6px; cursor: pointer;">
                                <input type="radio" name="memberStatus" value="pending">
                                <span class="status-indicator status-pending" style="font-size: 12px;">
                                    <div class="status-dot"></div>
                                    <span>待激活</span>
                                </span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 6px; cursor: pointer;">
                                <input type="radio" name="memberStatus" value="active" checked>
                                <span class="status-indicator status-active" style="font-size: 12px;">
                                    <div class="status-dot"></div>
                                    <span>活跃</span>
                                </span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 6px; cursor: pointer;">
                                <input type="radio" name="memberStatus" value="paused">
                                <span class="status-indicator status-paused" style="font-size: 12px;">
                                    <div class="status-dot"></div>
                                    <span>暂停</span>
                                </span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 6px; cursor: pointer;">
                                <input type="radio" name="memberStatus" value="inactive">
                                <span class="status-indicator" style="font-size: 12px;">
                                    <div class="status-dot" style="background: #6b7280;"></div>
                                    <span>已离职</span>
                                </span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">角色权限</label>
                    <div style="display: flex; flex-direction: column; gap: 12px;">
                        <label style="display: flex; align-items: center; gap: 8px; padding: 8px; border-radius: 6px; cursor: pointer;">
                            <input type="radio" name="userRole" value="super-admin">
                            <div>
                                <div style="font-weight: 600;">超级管理员</div>
                                <div style="font-size: 12px; color: #6b7280;">拥有所有权限，包括计费管理</div>
                            </div>
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; padding: 8px; border-radius: 6px; cursor: pointer;">
                            <input type="radio" name="userRole" value="admin" checked>
                            <div>
                                <div style="font-weight: 600;">管理员</div>
                                <div style="font-size: 12px; color: #6b7280;">可管理成员和权限，但不能修改计费</div>
                            </div>
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; padding: 8px; border-radius: 6px; cursor: pointer;">
                            <input type="radio" name="userRole" value="member">
                            <div>
                                <div style="font-weight: 600;">成员</div>
                                <div style="font-size: 12px; color: #6b7280;">普通用户，只能访问被授权的功能</div>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">AI代理权限分配</label>
                    <div style="background: #f8fafc; border-radius: 8px; padding: 16px;">
                        <div style="margin-bottom: 16px;">
                            <div style="font-weight: 600; margin-bottom: 8px;">📦 产品线A</div>
                            <div style="margin-left: 16px; display: flex; flex-direction: column; gap: 8px;">
                                <label class="checkbox-item">
                                    <input type="checkbox" checked> 🌍 全球战略官A
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" checked> 🎯 市场拓展专家
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" checked> 📊 市场情报官A
                                </label>
                            </div>
                        </div>
                        <div>
                            <div style="font-weight: 600; margin-bottom: 8px;">📦 产品线B</div>
                            <div style="margin-left: 16px; display: flex; flex-direction: column; gap: 8px;">
                                <label class="checkbox-item">
                                    <input type="checkbox"> 🌍 全球战略官B
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox"> 🎯 市场拓展专家
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox"> 📊 市场情报官B
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closePermissionModal()">取消</button>
                <button class="btn btn-primary">保存更改</button>
            </div>
        </div>
    </div>

    <script>
        // 模块切换功能
        function initModuleSwitcher() {
            const navItems = document.querySelectorAll('.nav-item');
            const contentArea = document.getElementById('contentArea');

            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除所有active类
                    navItems.forEach(nav => nav.classList.remove('active'));
                    // 添加active类
                    this.classList.add('active');

                    // 获取模块类型
                    const moduleType = this.dataset.module;
                    
                    // 切换内容
                    switch(moduleType) {
                        case 'members':
                            showMembersModule();
                            break;
                        case 'billing':
                            showBillingModule();
                            break;
                        case 'settings':
                            showSettingsModule();
                            break;
                    }
                });
            });
        }

        // 显示成员管理模块
        function showMembersModule() {
            document.getElementById('contentArea').innerHTML = `
                <div class="module-card" id="membersModule">
                    <div class="module-header">
                        <span class="module-icon">👥</span>
                        <div class="module-info">
                            <h2 class="module-title">成员与权限管理</h2>
                            <p class="module-description">管理企业团队成员，分配AI代理访问权限，设置用户角色和状态</p>
                        </div>
                    </div>

                    <!-- 操作工具栏 -->
                    <div class="toolbar">
                        <div class="toolbar-left">
                            <button class="btn btn-primary" onclick="openInviteModal()">
                                <span>➕</span> 邀请成员
                            </button>
                            <button class="btn btn-secondary">
                                <span>📁</span> 批量导入
                            </button>
                            <button class="btn btn-secondary">
                                <span>🔄</span> 同步权限
                            </button>
                        </div>
                        <div class="toolbar-right">
                            <input type="text" class="search-input" placeholder="搜索成员...">
                            <span class="search-icon">🔍</span>
                        </div>
                    </div>

                    <!-- 成员列表表格 -->
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" class="user-checkbox">
                                    </th>
                                    <th>姓名/邮箱</th>
                                    <th>角色</th>
                                    <th>状态</th>
                                    <th>AI代理权限</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="user-checkbox">
                                    </td>
                                    <td>
                                        <div class="user-info">
                                            <div class="user-details">
                                                <div class="user-name">张三</div>
                                                <div class="user-email"><EMAIL></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="role-badge role-super-admin">超级管理员</span>
                                    </td>
                                    <td>
                                        <div class="status-indicator status-active">
                                            <div class="status-dot"></div>
                                            <span>活跃</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="permissions-summary">
                                            全部代理<br>
                                            <span style="color: #6b7280; font-size: 11px;">(3个产品线)</span>
                                        </div>
                                    </td>
                                    <td>
                                        <button class="action-btn" onclick="openPermissionModal('张三', '<EMAIL>')">编辑</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="user-checkbox">
                                    </td>
                                    <td>
                                        <div class="user-info">
                                            <div class="user-details">
                                                <div class="user-name">李四</div>
                                                <div class="user-email"><EMAIL></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="role-badge role-admin">管理员</span>
                                    </td>
                                    <td>
                                        <div class="status-indicator status-active">
                                            <div class="status-dot"></div>
                                            <span>活跃</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="permissions-summary">
                                            部分代理<br>
                                            <span style="color: #6b7280; font-size: 11px;">(2个产品线)</span>
                                        </div>
                                    </td>
                                    <td>
                                        <button class="action-btn" onclick="openPermissionModal('李四', '<EMAIL>')">编辑</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="user-checkbox">
                                    </td>
                                    <td>
                                        <div class="user-info">
                                            <div class="user-details">
                                                <div class="user-name">王五</div>
                                                <div class="user-email"><EMAIL></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="role-badge role-member">成员</span>
                                    </td>
                                    <td>
                                        <div class="status-indicator status-paused">
                                            <div class="status-dot"></div>
                                            <span>暂停</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="permissions-summary">
                                            市场专家<br>
                                            <span style="color: #6b7280; font-size: 11px;">(产品线A)</span>
                                        </div>
                                    </td>
                                    <td>
                                        <button class="action-btn" onclick="openPermissionModal('王五', '<EMAIL>')">编辑</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="user-checkbox">
                                    </td>
                                    <td>
                                        <div class="user-info">
                                            <div class="user-details">
                                                <div class="user-name">赵六</div>
                                                <div class="user-email"><EMAIL></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="role-badge role-member">成员</span>
                                    </td>
                                    <td>
                                        <div class="status-indicator status-pending">
                                            <div class="status-dot"></div>
                                            <span>待激活</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="permissions-summary">
                                            无权限<br>
                                            <span style="color: #ef4444; font-size: 11px;">邀请链接: 2天后过期</span>
                                        </div>
                                    </td>
                                    <td>
                                        <button class="action-btn" onclick="openPermissionModal('赵六', '<EMAIL>')">编辑</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        // 显示计费管理模块
        function showBillingModule() {
            document.getElementById('contentArea').innerHTML = `
                <div class="module-card">
                    <div class="module-header">
                        <span class="module-icon">💰</span>
                        <div class="module-info">
                            <h2 class="module-title">计费与点数管理</h2>
                            <p class="module-description">监控套餐使用情况，查看点数消耗详情，进行成本分析和预算规划</p>
                        </div>
                    </div>

                    <!-- 套餐概览仪表盘 -->
                    <div class="dashboard-grid">
                        <!-- 当前套餐信息 -->
                        <div class="dashboard-card">
                            <div class="dashboard-card-header">
                                <span class="dashboard-card-icon">🏢</span>
                                <span class="dashboard-card-title">当前套餐信息</span>
                            </div>
                            <div class="package-info">
                                <div class="package-item">
                                    <span class="package-label">套餐类型</span>
                                    <span class="package-value">企业版专业套餐</span>
                                </div>
                                <div class="package-item">
                                    <span class="package-label">包含AI代理</span>
                                    <span class="package-value">3个</span>
                                </div>
                                <div class="package-item">
                                    <span class="package-label">月度配额</span>
                                    <span class="package-value">5,000点数</span>
                                </div>
                                <div class="package-item">
                                    <span class="package-label">到期时间</span>
                                    <span class="package-value">2025-02-15</span>
                                </div>
                            </div>
                            <div style="margin-top: 16px; display: flex; gap: 8px;">
                                <button class="btn btn-primary" style="font-size: 12px; padding: 8px 12px;">续费管理</button>
                                <button class="btn btn-secondary" style="font-size: 12px; padding: 8px 12px;">升级套餐</button>
                            </div>
                        </div>

                        <!-- 本月使用统计 -->
                        <div class="dashboard-card">
                            <div class="dashboard-card-header">
                                <span class="dashboard-card-icon">📊</span>
                                <span class="dashboard-card-title">本月使用统计</span>
                            </div>
                            <div class="usage-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 73.2%"></div>
                                </div>
                                <div class="progress-text">
                                    <span>已用: 3,660 / 5,000</span>
                                    <span>73.2%</span>
                                </div>
                            </div>
                            <div class="package-info">
                                <div class="package-item">
                                    <span class="package-label">剩余点数</span>
                                    <span class="package-value">1,340</span>
                                </div>
                                <div class="package-item">
                                    <span class="package-label">预计不足时间</span>
                                    <span class="package-value" style="color: #f59e0b;">7天后</span>
                                </div>
                                <div class="package-item">
                                    <span class="package-label">日均消耗</span>
                                    <span class="package-value">122点数</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 点数消耗详情 -->
                    <div class="dashboard-card" style="margin-bottom: 24px;">
                        <div class="dashboard-card-header">
                            <span class="dashboard-card-icon">📈</span>
                            <span class="dashboard-card-title">Tokens消耗详情</span>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-title">按代理分类</div>
                                <div class="stat-value">45%</div>
                                <div class="stat-change">全球战略官消耗最多</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-title">按任务类型</div>
                                <div class="stat-value">A1</div>
                                <div class="stat-change">消耗最高的任务</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-title">Artifacts生成</div>
                                <div class="stat-value">847</div>
                                <div class="stat-change">本月总计</div>
                            </div>
                        </div>
                        
                        <!-- 详细Tokens消耗记录表格 -->
                        <div class="table-container" style="margin-top: 20px;">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>日期时间</th>
                                        <th>代理实例</th>
                                        <th>任务类型</th>
                                        <th>Artifacts数量</th>
                                        <th>Input Tokens</th>
                                        <th>Output Tokens</th>
                                        <th>总消耗</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>01-15 14:32</td>
                                        <td>全球战略官A</td>
                                        <td>A1: 产业链定位</td>
                                        <td><span style="font-weight: 600; color: #2563eb;">15</span></td>
                                        <td>23,450</td>
                                        <td>89,670</td>
                                        <td><span style="font-weight: 600;">113,120</span></td>
                                    </tr>
                                    <tr>
                                        <td>01-14 16:18</td>
                                        <td>德国市场顾问</td>
                                        <td>A4: 竞品分析</td>
                                        <td><span style="font-weight: 600; color: #2563eb;">12</span></td>
                                        <td>18,230</td>
                                        <td>67,890</td>
                                        <td><span style="font-weight: 600;">86,120</span></td>
                                    </tr>
                                    <tr>
                                        <td>01-14 10:45</td>
                                        <td>市场情报官A</td>
                                        <td>T2: 情报处理</td>
                                        <td><span style="font-weight: 600; color: #2563eb;">8</span></td>
                                        <td>12,650</td>
                                        <td>34,280</td>
                                        <td><span style="font-weight: 600;">46,930</span></td>
                                    </tr>
                                    <tr>
                                        <td>01-13 09:22</td>
                                        <td>全球战略官B</td>
                                        <td>GGP: 增长战略</td>
                                        <td><span style="font-weight: 600; color: #7c3aed;">28</span></td>
                                        <td>45,780</td>
                                        <td>152,340</td>
                                        <td><span style="font-weight: 600; color: #7c3aed;">198,120</span> <span style="font-size: 11px; color: #7c3aed;">(增值)</span></td>
                                    </tr>
                                    <tr>
                                        <td>01-12 15:56</td>
                                        <td>法国市场顾问</td>
                                        <td>A6: 定价策略</td>
                                        <td><span style="font-weight: 600; color: #2563eb;">10</span></td>
                                        <td>15,890</td>
                                        <td>58,420</td>
                                        <td><span style="font-weight: 600;">74,310</span></td>
                                    </tr>
                                    <tr>
                                        <td>01-11 11:33</td>
                                        <td>市场情报官B</td>
                                        <td>DIP: 深度分析</td>
                                        <td><span style="font-weight: 600; color: #7c3aed;">18</span></td>
                                        <td>28,940</td>
                                        <td>95,670</td>
                                        <td><span style="font-weight: 600; color: #7c3aed;">124,610</span> <span style="font-size: 11px; color: #7c3aed;">(增值)</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 成本分摊分析 -->
                        <div style="margin-top: 24px; padding: 20px; background: #f8fafc; border-radius: 8px;">
                            <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 16px; color: #374151;">💡 成本分摊建议</h4>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px;">
                                <div style="text-align: center;">
                                    <div style="font-size: 18px; font-weight: 700; color: #2563eb;">¥18,500</div>
                                    <div style="font-size: 12px; color: #6b7280;">全球战略官 (45%)</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="font-size: 18px; font-weight: 700; color: #10b981;">¥15,200</div>
                                    <div style="font-size: 12px; color: #6b7280;">市场拓展专家 (37%)</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="font-size: 18px; font-weight: 700; color: #f59e0b;">¥7,300</div>
                                    <div style="font-size: 12px; color: #6b7280;">市场情报官 (18%)</div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            `;
        }

        // 显示系统设置模块
        function showSettingsModule() {
            document.getElementById('contentArea').innerHTML = `
                <div class="module-card">
                    <div class="module-header">
                        <span class="module-icon">⚙️</span>
                        <div class="module-info">
                            <h2 class="module-title">系统设置与配置</h2>
                            <p class="module-description">配置企业基本信息，设置通知偏好，个性化系统界面和功能</p>
                        </div>
                    </div>

                    <!-- 企业信息设置 -->
                    <div class="settings-section">
                        <div class="settings-header">
                            <h3 class="settings-title">企业信息</h3>
                            <p class="settings-description">配置企业的基本信息和联系方式</p>
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">企业全称</label>
                                <input type="text" class="form-input" value="北京XX科技有限公司">
                            </div>
                            <div class="form-group">
                                <label class="form-label">企业简称</label>
                                <input type="text" class="form-input" value="XX科技">
                            </div>
                            <div class="form-group">
                                <label class="form-label">行业分类</label>
                                <select class="form-select">
                                    <option value="manufacturing">制造业</option>
                                    <option value="technology">科技行业</option>
                                    <option value="finance">金融服务</option>
                                    <option value="retail">零售贸易</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">企业规模</label>
                                <select class="form-select">
                                    <option value="small">50人以下</option>
                                    <option value="medium" selected>50-500人</option>
                                    <option value="large">500人以上</option>
                                </select>
                            </div>
                        </div>

                        <h4 style="font-size: 16px; font-weight: 600; margin: 24px 0 16px 0;">主要联系人</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">姓名</label>
                                <input type="text" class="form-input" value="张总">
                            </div>
                            <div class="form-group">
                                <label class="form-label">职位</label>
                                <input type="text" class="form-input" value="CEO">
                            </div>
                            <div class="form-group">
                                <label class="form-label">电话</label>
                                <input type="text" class="form-input" value="138****8888">
                            </div>
                            <div class="form-group">
                                <label class="form-label">邮箱</label>
                                <input type="email" class="form-input" value="<EMAIL>">
                            </div>
                        </div>
                    </div>

                    <!-- 通知偏好设置 -->
                    <div class="settings-section">
                        <div class="settings-header">
                            <h3 class="settings-title">通知偏好</h3>
                            <p class="settings-description">设置系统通知的接收方式和频率</p>
                        </div>

                        <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">邮件通知设置</h4>
                        <div class="checkbox-group">
                            <label class="checkbox-item">
                                <input type="checkbox" checked>
                                <span>新成员加入通知</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" checked>
                                <span>权限变更通知</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" checked>
                                <span>点数不足预警</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" checked>
                                <span>套餐到期提醒</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" checked>
                                <span>异常使用报告</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox">
                                <span>每日使用统计</span>
                            </label>
                        </div>

                        <h4 style="font-size: 16px; font-weight: 600; margin: 24px 0 16px 0;">站内通知设置</h4>
                        <div class="checkbox-group">
                            <label class="checkbox-item">
                                <input type="checkbox" checked>
                                <span>AI代理工作状态更新</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" checked>
                                <span>任务委托进度提醒</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" checked>
                                <span>系统重要公告</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" checked>
                                <span>安全相关提醒</span>
                            </label>
                        </div>
                    </div>

                    <!-- 界面设置 -->
                    <div class="settings-section">
                        <div class="settings-header">
                            <h3 class="settings-title">界面设置</h3>
                            <p class="settings-description">个性化系统界面和显示偏好</p>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">时区设置</label>
                                <select class="form-select">
                                    <option value="UTC+8" selected>UTC+8 北京时间</option>
                                    <option value="UTC+0">UTC+0 格林威治时间</option>
                                    <option value="UTC-5">UTC-5 纽约时间</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">语言设置</label>
                                <select class="form-select">
                                    <option value="zh-CN" selected>简体中文</option>
                                    <option value="zh-TW">繁体中文</option>
                                    <option value="en-US">English</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">主题设置</label>
                                <select class="form-select">
                                    <option value="light" selected>浅色主题</option>
                                    <option value="dark">深色主题</option>
                                    <option value="auto">跟随系统</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="settings-actions">
                        <button class="btn btn-secondary">重置</button>
                        <button class="btn btn-primary">保存设置</button>
                    </div>
                </div>
            `;
        }

        // 弹窗相关函数
        function openInviteModal() {
            document.getElementById('inviteModal').classList.add('active');
        }

        function closeInviteModal() {
            document.getElementById('inviteModal').classList.remove('active');
        }

        function openPermissionModal(name, email) {
            document.getElementById('permissionModal').classList.add('active');
            document.getElementById('permissionModalTitle').textContent = `编辑成员权限 - ${name}`;
            document.getElementById('memberName').value = name;
            document.getElementById('memberEmail').value = email;
        }

        function closePermissionModal() {
            document.getElementById('permissionModal').classList.remove('active');
        }

        // 点击遮罩关闭弹窗
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-overlay')) {
                e.target.classList.remove('active');
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initModuleSwitcher();
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Foxu.AI - 任务委托中心</title>
    <style>
        /* Global Styles */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; background-color: #f9fafb; color: #1f2937; line-height: 1.6; overflow-x: hidden; }
        
        /* Background Animation */
        .background-animation { position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: -1; opacity: 0.03; overflow: hidden; }
        .background-animation::before, .background-animation::after { content: ""; position: absolute; width: 600px; height: 600px; border-radius: 50%; animation: atmosphericFloat 20s ease-in-out infinite; }
        .background-animation::before { background: radial-gradient(circle, #667eea 0%, transparent 70%); top: -300px; left: -300px; }
        .background-animation::after { background: radial-gradient(circle, #764ba2 0%, transparent 70%); bottom: -300px; right: -300px; animation-delay: -10s; }
        @keyframes atmosphericFloat { 0%, 100% { transform: translate(0, 0) scale(1); } 33% { transform: translate(30px, -30px) scale(1.1); } 66% { transform: translate(-20px, 20px) scale(0.9); } }

        /* Navbar */
        .navbar { position: sticky; top: 0; height: 60px; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(12px); border-bottom: 1px solid #e5e7eb; z-index: 1000; display: flex; align-items: center; padding: 0 24px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); }
        .navbar-brand { display: flex; align-items: center; font-size: 20px; font-weight: 700; background: linear-gradient(135deg, #2563eb, #9333ea); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; margin-right: 48px; }
        .navbar-menu { display: flex; gap: 32px; flex: 1; }
        .navbar-item { color: #374151; text-decoration: none; font-size: 15px; font-weight: 500; transition: color 0.2s; position: relative; }
        .navbar-item:hover { color: #2563eb; }
        .navbar-item.active { color: #2563eb; }
        .navbar-item.active::after { content: ''; position: absolute; bottom: -18px; left: 0; right: 0; height: 3px; background: #2563eb; border-radius: 3px 3px 0 0; }
        .navbar-actions { display: flex; align-items: center; gap: 24px; }
        .notification-badge { position: relative; width: 40px; height: 40px; border-radius: 50%; background: #f3f4f6; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.3s; }
        .user-avatar { width: 40px; height: 40px; border-radius: 50%; background: linear-gradient(135deg, #667eea, #764ba2); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; cursor: pointer; }

        /* Main Content Area */
        .main-container { display: flex; height: calc(100vh - 60px); }

        /* Left Sidebar */
        .sidebar { width: 20%; min-width: 300px; background: white; border-right: 1px solid #e5e7eb; overflow-y: auto; padding: 24px; box-shadow: 1px 0 5px rgba(0,0,0,0.03); /* Added subtle shadow */ }
        .sidebar-search { position: relative; margin-bottom: 24px; }
        .sidebar-search input { width: 100%; padding: 10px 40px 10px 16px; border: 1px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: all 0.3s; }
        .sidebar-search input:focus { outline: none; border-color: #2563eb; box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1); }
        .sidebar-search::after { content: '🔍'; position: absolute; right: 12px; top: 50%; transform: translateY(-50%); font-size: 16px; color: #6b7280; }
        .tree-nav { font-size: 14px; }
        .tree-node { margin-bottom: 12px; /* Increased spacing between nodes */ }
        .tree-node-header { display: flex; align-items: center; padding: 10px 12px; /* Adjusted padding */ margin: 2px 0; border-radius: 8px; /* Slightly more rounded corners */ cursor: pointer; transition: all 0.2s ease-in-out; /* Smoother transition */ user-select: none; }
        .tree-node-header:hover { background: #eef2ff; /* Lighter blue on hover */ color: #3b82f6; /* Blue text on hover */ box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* Added subtle shadow */ }
        .tree-node-header.active { background: #e0e7ff; /* More prominent active background */ color: #2563eb; /* Darker blue for active text */ font-weight: 600; /* Bolder active text */ }
        .tree-node-icon { font-size: 18px; /* Slightly larger icon */ margin-right: 10px; /* More space */ color: #6b7280; /* Default icon color */ }
        .tree-node-header.active .tree-node-icon { color: #2563eb; /* Active icon color */ }
        .tree-node-label { flex: 1; font-weight: 500; }
        .tree-node-count { background: #e5e7eb; color: #374151; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 600; }
        .tree-node-toggle { font-size: 14px; /* Slightly larger toggle */ color: #6b7280; margin-right: 6px; /* Adjusted margin */ transition: transform 0.2s ease-in-out; }
        .tree-node.expanded .tree-node-toggle { transform: rotate(90deg); }
        .tree-children { padding-left: 30px; /* Increased indentation */ display: none; }
        .tree-node.expanded .tree-children { display: block; }
        .tree-leaf { display: flex; align-items: center; padding: 8px 12px; margin: 2px 0; border-radius: 6px; cursor: pointer; transition: all 0.2s ease-in-out; }
        .tree-leaf:hover { background: #f3f4f6; color: #2563eb; /* Blue text on hover */ box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* Added subtle shadow */ }
        .tree-leaf.active { background: #dbeafe; color: #1d4ed8; font-weight: 500; /* Slightly bolder active text */ }
        .tree-leaf-icon { font-size: 16px; margin-right: 8px; color: #6b7280; /* Default icon color */ }
        .tree-leaf.active .tree-leaf-icon { color: #1d4ed8; /* Active icon color */ }
        .tree-leaf-status { margin-left: auto; width: 10px; /* Increased from 8px */ height: 10px; /* Increased from 8px */ border-radius: 50%; border: 1px solid rgba(0,0,0,0.1); /* Added border */ }
        .status-active { background: #10b981; }
        .status-paused { background: #f59e0b; }
        .status-error { background: #dc2626; }
        .add-agent-btn { display: flex; align-items: center; padding: 8px 12px; margin: 2px 0; border-radius: 6px; cursor: pointer; transition: all 0.2s; border: 1px dashed #d1d5db; background: #f9fafb; color: #6b7280; font-size: 14px; }
        .add-agent-btn:hover { background: #f3f4f6; border-color: #2563eb; color: #2563eb; }
        .add-icon { font-size: 16px; margin-right: 8px; }

        /* Right Content Area */
        .content { flex: 1; background: #f9fafb; overflow-y: auto; padding: 24px; }
        .agent-card { background: white; border-radius: 16px; padding: 20px 32px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); margin: 0 auto; }
        .agent-header { display: flex; align-items: center; margin-bottom: 24px; padding-bottom: 10px; border-bottom: 1px solid #e5e7eb; }
        .agent-icon { font-size: 48px; margin-right: 20px; }
        .agent-info { flex: 1; }
        .agent-name { font-size: 20px; font-weight: 600; }
        .agent-meta { display: flex; align-items: center; gap: 16px; font-size: 14px; color: #6b7280; }
        .agent-status { display: flex; align-items: center; gap: 6px; }

        /* Collaboration Seat Management */
        .collaboration-seat { background: #f9fafb; border-radius: 12px; padding: 16px; margin-bottom: 24px; border: 1px solid #e5e7eb; }
        .collaboration-seat .section-title { border-bottom: none; margin-bottom: 12px; padding-bottom: 0; }
        .seat-details { display: flex; align-items: center; gap: 16px; }
        .seat-avatar { width: 48px; height: 48px; border-radius: 50%; background: linear-gradient(135deg, #a78bfa, #9333ea); color: white; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 20px; }
        .seat-info .seat-owner { font-size: 16px; font-weight: 600; }
        .certification-status { display: flex; align-items: center; gap: 4px; font-size: 12px; font-weight: 500; padding: 2px 8px; border-radius: 12px; margin-top: 4px; }
        .status-certified { background-color: #ecfdf5; color: #065f46; }
        .status-pending { background-color: #fffbeb; color: #b45309; }
        .seat-actions { margin-left: auto; display: flex; gap: 12px; }
        .seat-action-btn { padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; border: 1px solid #d1d5db; background: white; color: #374151; }
        .seat-action-btn:hover { background: #f3f4f6; }

        /* Agent Section (General) */
        .agent-section { background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 12px; padding: 20px; margin-bottom: 16px; }
        .section-title { display: flex; align-items: center; gap: 8px; font-size: 16px; font-weight: 600; color: #374151; margin-bottom: 12px; padding-bottom: 12px; border-bottom: 1px solid #e5e7eb; }
        .section-title span:first-child { font-size: 18px; }

        /* Unified Task Table Styles */
        .task-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
            font-size: 14px;
        }
        .task-table th, .task-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            vertical-align: middle;
        }
        .task-table th {
            background-color: #f9fafb;
            font-weight: 600;
            color: #6b7280;
        }
        .task-table .task-name-cell {
            font-weight: 500;
            color: #1f2937;
        }
        .task-table .status-cell .status-idle {
            color: #6b7280;
        }
        .task-table .status-cell .status-progress .progress-owner {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 4px;
        }
        .progress-bar-container { width: 100%; height: 8px; background: #e5e7eb; border-radius: 4px; overflow: hidden; }
        .progress-bar-fill { height: 100%; background: linear-gradient(90deg, #3b82f6, #2563eb); border-radius: 4px; }
        .task-table .history-cell a {
            color: #2563eb;
            text-decoration: none;
            font-weight: 500;
        }
        .task-table .history-cell a:hover { text-decoration: underline; }
        .task-table .action-cell .btn-delegate {
            background: #2563eb;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: background 0.2s;
        }
        .task-table .action-cell .btn-delegate:hover { background: #1d4ed8; }
        .task-table .action-cell .status-inprogress {
            color: #6b7280;
            font-style: italic;
            font-size: 13px;
        }

        /* History Modal Styles */
        .history-modal .modal-body {
            padding: 20px;
        }
        .history-modal .history-table {
            width: 100%;
            border-collapse: collapse;
        }
        .history-modal .history-table th, .history-modal .history-table td {
            padding: 10px;
            border-bottom: 1px solid #e5e7eb;
            text-align: left;
            font-size: 14px;
        }
        .history-modal .history-table th {
            background-color: #f9fafb;
            font-weight: 600;
            color: #6b7280;
        }
        .history-modal .history-table td a {
            color: #2563eb;
            text-decoration: none;
        }
        .history-modal .history-table td a:hover {
            text-decoration: underline;
        }

        /* Modal styles are kept from previous version */
        .modal-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); display: none; align-items: center; justify-content: center; z-index: 2000; }
        .modal-overlay.active { display: flex; }
        .modal { background: white; border-radius: 16px; width: 90%; max-width: 800px; max-height: 90vh; overflow: hidden; display: flex; flex-direction: column; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1); }
        .modal-header { padding: 24px 32px; border-bottom: 1px solid #e5e7eb; }
        .modal-title { font-size: 20px; font-weight: 700; }
        .steps { display: flex; align-items: center; gap: 32px; }
        .step { display: flex; align-items: center; gap: 8px; color: #9ca3af; font-size: 14px; }
        .step-number { width: 32px; height: 32px; border-radius: 50%; background: #e5e7eb; display: flex; align-items: center; justify-content: center; font-weight: 600; transition: all 0.3s; }
        .step.active .step-number { background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; }
        .step.completed .step-number { background: #10b981; color: white; }
        .step-label { font-weight: 500; transition: color 0.3s; }
        .step.active .step-label { color: #1f2937; }
        .modal-body { flex: 1; overflow-y: auto; padding: 32px; }
        .task-list { display: flex; flex-direction: column; gap: 16px; }
        .task-option { border: 2px solid #e5e7eb; border-radius: 12px; padding: 20px; cursor: pointer; transition: all 0.3s; }
        .task-option:hover { border-color: #93c5fd; background: #f0f9ff; }
        .task-option.selected { border-color: #2563eb; background: #eff6ff; }
        .task-option-header { display: flex; align-items: center; gap: 12px; margin-bottom: 8px; }
        .task-code { background: #e5e7eb; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 600; color: #374151; }
        .task-name { font-size: 16px; font-weight: 600; color: #1f2937; }
        .task-description { font-size: 14px; color: #6b7280; line-height: 1.5; }
        .form-group { margin-bottom: 24px; }
        .form-label { display: block; font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 8px; }
        .form-hint { font-size: 12px; color: #6b7280; margin-top: 4px; }
        .form-input, .form-select, .form-textarea { width: 100%; padding: 10px 16px; border: 1px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: all 0.3s; }
        .form-input:focus, .form-select:focus, .form-textarea:focus { outline: none; border-color: #2563eb; box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1); }
        .form-textarea { resize: vertical; min-height: 100px; }
        .form-tags { display: flex; flex-wrap: wrap; gap: 8px; padding: 8px; border: 1px solid #e5e7eb; border-radius: 8px; min-height: 44px; }
        .tag { background: #e0e7ff; color: #3730a3; padding: 4px 12px; border-radius: 4px; font-size: 14px; display: flex; align-items: center; gap: 4px; }
        .tag-remove { cursor: pointer; font-size: 12px; }
        .summary-section { background: #f3f4f6; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
        .summary-row { display: flex; padding: 8px 0; }
        .summary-label { font-size: 14px; color: #6b7280; width: 140px; }
        .summary-value { flex: 1; font-size: 14px; color: #1f2937; font-weight: 500; }
        .modal-footer { padding: 24px 32px; border-top: 1px solid #e5e7eb; display: flex; justify-content: space-between; align-items: center; }
        .modal-actions { display: flex; gap: 12px; }
        .btn { padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 600; cursor: pointer; border: none; }
        .btn-secondary { background: #f3f4f6; color: #374151; }
        .btn-secondary:hover { background: #e5e7eb; }
        .btn-primary { background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; }
        .btn-primary:hover { transform: translateY(-1px); box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3); }
        .btn-primary:disabled { opacity: 0.5; cursor: not-allowed; transform: none; }
        @keyframes successFadeIn { from { opacity: 0; transform: scale(0.8); } to { opacity: 1; transform: scale(1); } }
        @keyframes checkmarkStroke { 0% { stroke-dashoffset: 100; } 100% { stroke-dashoffset: 0; } }
        .success-checkmark { animation: successFadeIn 0.5s ease-out; }
        .success-checkmark svg path { stroke-dasharray: 100; stroke-dashoffset: 100; animation: checkmarkStroke 0.8s ease-out 0.3s forwards; }
        .empty-state { text-align: center; padding: 80px 40px; }
        .empty-icon { font-size: 64px; margin-bottom: 16px; opacity: 0.3; }
        .empty-title { font-size: 20px; font-weight: 600; color: #374151; margin-bottom: 8px; }
        .empty-description { font-size: 14px; color: #6b7280; }
        @media (max-width: 1199px) { .sidebar { width: 35%; min-width: 280px; } .task-grid { grid-template-columns: repeat(2, 1fr); } }
        @media (max-width: 767px) { .navbar-menu { display: none; } .main-container { flex-direction: column; } .sidebar { width: 100%; height: auto; border-right: none; border-bottom: 1px solid #e5e7eb; } .content { padding: 16px; } .task-grid { grid-template-columns: 1fr; } .steps { gap: 16px; } .step-label { display: none; } }
    </style>
</head>
<body>
    <!-- Background Animation -->
    <div class="background-animation"></div>

    <!-- Global Navigation Bar -->
    <nav class="navbar">
        <div class="navbar-brand">Foxu.AI</div>
        <div class="navbar-menu">
            <a href="index.html" class="navbar-item">首页</a>
            <a href="#" class="navbar-item active">任务委托中心</a>
            <a href="knowledge_base_explorer.html" class="navbar-item">知识库</a>
            <a href="cadre_competency_learning.html" class="navbar-item">干部能力与学习系统</a>
            <a href="team_management.html" class="navbar-item">企业管理</a>
        </div>
        <div class="navbar-actions">
            <div class="notification-badge"><span>🔔</span></div>
            <div class="user-avatar">JD</div>
        </div>
    </nav>

    <!-- Main Content Area -->
    <div class="main-container">
        <!-- Left Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-search">
                <input type="text" placeholder="搜索AI代理...">
            </div>
            
            <div class="tree-nav">
                <!-- 产品线A -->
                <div class="tree-node expanded">
                    <div class="tree-node-header">
                        <span class="tree-node-toggle">▶</span>
                        <span class="tree-node-icon">📦</span>
                        <span class="tree-node-label">产品线A</span>
                        <span class="tree-node-count">6</span>
                    </div>
                    <div class="tree-children">
                        <!-- 固定的全球战略官 -->
                        <div class="tree-leaf active" data-agent-type="global-strategist" data-agent-id="global-a">
                            <span class="tree-leaf-icon">🌍</span>
                            <span>全球战略官</span>
                            <span class="tree-leaf-status status-active"></span>
                        </div>
                        <!-- 固定的市场情报官 -->
                        <div class="tree-leaf" data-agent-type="intel-officer" data-agent-id="intel-a">
                            <span class="tree-leaf-icon">📊</span>
                            <span>市场情报官</span>
                            <span class="tree-leaf-status status-active"></span>
                        </div>
                        <!-- 市场顾问 -->
                        <div class="tree-leaf" data-agent-type="market-expert" data-agent-id="market-de-a">
                            <span class="tree-leaf-icon">🇩🇪</span>
                            <span>德国市场顾问</span>
                            <span class="tree-leaf-status status-active"></span>
                        </div>
                        <div class="tree-leaf" data-agent-type="market-expert" data-agent-id="market-fr-a">
                            <span class="tree-leaf-icon">🇫🇷</span>
                            <span>法国市场顾问</span>
                            <span class="tree-leaf-status status-active"></span>
                        </div>
                        <div class="tree-leaf" data-agent-type="market-expert" data-agent-id="market-us-a">
                            <span class="tree-leaf-icon">🇺🇸</span>
                            <span>美国市场顾问</span>
                            <span class="tree-leaf-status status-active"></span>
                        </div>
                        <div class="tree-leaf" data-agent-type="market-expert" data-agent-id="market-uk-a">
                            <span class="tree-leaf-icon">🇬🇧</span>
                            <span>英国市场顾问</span>
                            <span class="tree-leaf-status status-active"></span>
                        </div>
                        <!-- Add Market Advisor Button -->
                        <div class="add-agent-btn" onclick="showAddMarketAdvisorModal('产品线A')">
                            <span class="add-icon">➕</span>
                            <span>添加市场顾问</span>
                        </div>
                    </div>
                </div>
                
                <!-- 产品线B -->
                <div class="tree-node expanded">
                    <div class="tree-node-header">
                        <span class="tree-node-toggle">▶</span>
                        <span class="tree-node-icon">📦</span>
                        <span class="tree-node-label">产品线B</span>
                        <span class="tree-node-count">4</span>
                    </div>
                    <div class="tree-children">
                        <!-- Fixed Global Strategist -->
                        <div class="tree-leaf" data-agent-type="global-strategist" data-agent-id="global-b">
                            <span class="tree-leaf-icon">🌍</span>
                            <span>全球战略官</span>
                            <span class="tree-leaf-status status-active"></span>
                        </div>
                        <!-- Fixed Intelligence Officer -->
                        <div class="tree-leaf" data-agent-type="intel-officer" data-agent-id="intel-b">
                            <span class="tree-leaf-icon">📊</span>
                            <span>市场情报官</span>
                            <span class="tree-leaf-status status-active"></span>
                        </div>
                        <!-- Market Advisors -->
                        <div class="tree-leaf" data-agent-type="market-expert" data-agent-id="market-jp-b">
                            <span class="tree-leaf-icon">🇯🇵</span>
                            <span>日本市场顾问</span>
                            <span class="tree-leaf-status status-active"></span>
                        </div>
                        <div class="tree-leaf" data-agent-type="market-expert" data-agent-id="market-kr-b">
                            <span class="tree-leaf-icon">🇰🇷</span>
                            <span>韩国市场顾问</span>
                            <span class="tree-leaf-status status-paused"></span>
                        </div>
                        <!-- Add Market Advisor Button -->
                        <div class="add-agent-btn" onclick="showAddMarketAdvisorModal('产品线B')">
                            <span class="add-icon">➕</span>
                            <span>添加市场顾问</span>
                        </div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Right Content Area -->
        <main class="content" id="contentArea">
            <!-- Default display for the first agent's details -->
            <div class="agent-card" id="global-strategist-card">
                <div class="agent-header">
                    <span class="agent-icon">🌍</span>
                    <div class="agent-info">
                        <h2 class="agent-name">全球战略官A</h2>
                        <div class="agent-meta">
                            <span>产品线A</span>
                            <span>|</span>
                            <span class="agent-status">
                                <span class="tree-leaf-status status-active"></span>
                                活跃中
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Collaboration Seat Management -->
                <div class="agent-section collaboration-seat">
                    <h3 class="section-title">
                        <span>👤</span>
                        <span>协作席位管理</span>
                    </h3>
                    <div class="seat-details">
                        <div class="seat-avatar">李</div>
                        <div class="seat-info">
                            <div class="seat-owner">李经理</div>
                            <div class="certification-status status-certified"><span>✅</span><span>已认证</span></div>
                        </div>
                        <div class="seat-actions">
                            <button class="seat-action-btn">更换负责人</button>
                        </div>
                    </div>
                </div>
                
                <!-- REFACTORED: Unified Task Table -->
                <div class="agent-section tasks">
                    <h3 class="section-title"><span>🎯</span><span>任务能力清单</span></h3>
                    <table class="task-table">
                        <thead>
                            <tr>
                                <th>任务名称</th>
                                <th>当前状态</th>
                                <th>历史记录</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="task-name-cell">A0: 全球商业环境分析</td>
                                <td class="status-cell"><span class="status-idle">空闲</span></td>
                                <td class="history-cell"><a href="#" onclick="openHistoryModal('A0: 全球商业环境分析', 3)">3 次</a></td>
                                <td class="action-cell"><button class="btn-delegate" onclick="openTaskModal('global-strategist', '全球战略官A', 'A0: 全球商业环境分析')">委托任务</button></td>
                            </tr>
                            <tr>
                                <td class="task-name-cell">A2: 全球竞争格局分析</td>
                                <td class="status-cell">
                                    <a href="aip_space.html" class="status-progress-link">
                                        <div class="status-progress">
                                            <div class="progress-bar-container">
                                                <div class="progress-bar-fill" style="width: 60%;"></div>
                                            </div>
                                            <div class="progress-date">开始日期: 2024-07-15</div>
                                        </div>
                                    </a>
                                </td>
                                <td class="history-cell"><a href="#" onclick="openHistoryModal('A2: 全球竞争格局分析', 5)">5 次</a></td>
                                <td class="action-cell"><span class="status-inprogress">进行中...</span></td>
                            </tr>
                            <tr>
                                <td class="task-name-cell">GGP: 集团增长组合战略规划 (增值)</td>
                                <td class="status-cell"><span class="status-idle">空闲</span></td>
                                <td class="history-cell"><a href="#" onclick="openHistoryModal('GGP: 集团增长组合战略规划 (增值)', 0)">0 次</a></td>
                                <td class="action-cell"><button class="btn-delegate" onclick="openTaskModal('global-strategist', '全球战略官A', 'GGP: 集团增长组合战略规划 (增值)')">委托任务</button></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal for task delegation -->
    <div class="modal-overlay" id="taskModal">
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title">发起分析任务</h2>
                <div class="steps">
                    <div class="step active" id="step1">
                        <span class="step-number">1</span>
                        <span class="step-label">选择分析任务</span>
                    </div>
                    <div class="step" id="step2">
                        <span class="step-number">2</span>
                        <span class="step-label">配置任务参数</span>
                    </div>
                    <div class="step" id="step3">
                        <span class="step-number">3</span>
                        <span class="step-label">回顾与确认</span>
                    </div>
                </div>
            </div>

            <div class="modal-body">
                <!-- Step 1: Select Task -->
                <div id="step1Content" class="step-content">
                    <div class="task-list" id="taskList">
                        <!-- Task list will be dynamically populated by JS -->
                    </div>
                </div>

                <!-- Step 2: Configure Task Parameters -->
                <div id="step2Content" class="step-content" style="display: none;">
                    <form id="taskForm">
                        <div class="form-group">
                            <label class="form-label">任务名称</label>
                            <input type="text" class="form-input" id="taskNameInput" placeholder="例如：2025年Q1德国市场进入策略研究">
                            <p class="form-hint">为本次任务设置一个易于识别的名称</p>
                        </div>

                        <div class="form-group">
                            <label class="form-label">执行代理</label>
                            <input type="text" class="form-input" id="agentDisplay" readonly>
                        </div>

                        <div id="dynamicParams">
                            <!-- Dynamic parameters will be populated by JS -->
                        </div>
                    </form>
                </div>

                <!-- Step 3: Review and Confirm -->
                <div id="step3Content" class="step-content" style="display: none;">
                    <div class="summary-section">
                        <div class="summary-row">
                            <span class="summary-label">委托代理:</span>
                            <span class="summary-value" id="summaryAgent"></span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">所属产品线:</span>
                            <span class="summary-value" id="summaryProductLine"></span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">分析任务:</span>
                            <span class="summary-value" id="summaryTask"></span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">任务名称:</span>
                            <span class="summary-value" id="summaryTaskName"></span>
                        </div>
                    </div>
                    
                    <div class="summary-section">
                        <h4 style="margin-bottom: 12px;">核心参数</h4>
                        <div id="summaryParams">
                            <!-- Parameter summary will be populated by JS -->
                        </div>
                    </div>

                    <div class="summary-section">
                        <div class="summary-row">
                            <span class="summary-label">预期产出:</span>
                            <span class="summary-value">一个包含完整分析的AIP空间</span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">预计耗时:</span>
                            <span class="summary-value">3-5个工作日</span>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Success Feedback -->
                <div id="step4Content" class="step-content" style="display: none;">
                    <div style="text-align: center; padding: 60px 20px;">
                        <div class="success-checkmark" style="width: 80px; height: 80px; margin: 0 auto 24px; background: linear-gradient(135deg, #10b981, #059669); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <svg style="width: 48px; height: 48px; color: white;" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <h3 style="font-size: 24px; font-weight: 700; color: #1f2937; margin-bottom: 16px;">任务委托成功！</h3>
                        <p style="font-size: 16px; color: #6b7280; margin-bottom: 40px;">您的分析任务已成功提交给AI代理，预计将在3-5个工作日内完成。</p>
                        
                        <div style="display: flex; gap: 16px; justify-content: center;">
                            <a href="index.html" class="btn btn-secondary" style="display: inline-flex; align-items: center; gap: 8px; padding: 12px 24px; text-decoration: none;">
                                <svg style="width: 20px; height: 20px;" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                                </svg>
                                <span>跳转到首页</span>
                            </a>
                            <button onclick="location.reload()" class="btn btn-primary" style="display: inline-flex; align-items: center; gap: 8px; padding: 12px 24px;">
                                <svg style="width: 20px; height: 20px;" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <span>继续委托任务</span>
                            </button>
                        </div>
                        
                        <div style="margin-top: 40px; padding: 20px; background: #f3f4f6; border-radius: 8px;">
                            <p style="font-size: 14px; color: #374151; margin-bottom: 8px;">
                                <strong>提示：</strong>您可以在以下位置查看任务进度：
                            </p>
                            <ul style="list-style: none; font-size: 14px; color: #6b7280;">
                                <li style="padding: 4px 0;">• 首页信息流中的“任务进行中”卡片</li>
                                <li style="padding: 4px 0;">• AI交互中心的通知提醒</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeTaskModal()">取消</button>
                <div class="modal-actions">
                    <button class="btn btn-secondary" id="prevBtn" onclick="previousStep()" style="display: none;">上一步</button>
                    <button class="btn btn-primary" id="nextBtn" onclick="nextStep()">下一步</button>
                </div>
            </div>
        </div>
    </div>

    <!-- History Modal -->
    <div class="modal-overlay" id="historyModal">
        <div class="modal history-modal">
            <div class="modal-header">
                <h2 class="modal-title" id="historyModalTitle">任务历史</h2>
            </div>
            <div class="modal-body">
                <table class="history-table">
                    <thead>
                        <tr>
                            <th>任务日期</th>
                            <th>完成状态</th>
                            <th>查看详情</th>
                        </tr>
                    </thead>
                    <tbody id="historyTableBody">
                        <!-- History items will be populated by JS -->
                    </tbody>
                </table>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeHistoryModal()">关闭</button>
            </div>
        </div>
    </div>

    <script>
        // Current selected agent info
        let currentAgent = {
            type: 'global-strategist',
            id: 'global-a',
            name: '全球战略官A',
            productLine: '产品线A'
        };

        // Current step in the modal
        let currentStep = 1;

        // Selected task in the modal
        let selectedTask = null;

        // Task data (for dynamic population of task list)
        const taskData = {
            'global-strategist': [
                { code: 'A0', name: '全球商业环境分析', description: '分析全球宏观经济、政策法规、行业趋势等商业环境要素' },
                { code: 'A1', name: '产业链定位与机会评估', description: '该分析将帮助您在宏观价值链中找到最有利的出海角色和商业模式' },
                { code: 'A2', name: '全球竞争格局分析', description: '深入分析全球主要竞争对手的战略布局和竞争优势' },
                { code: 'A3', name: '目标市场研究', description: '针对特定国家或地区进行深度市场研究和机会评估' },
                { code: 'GGP', name: '集团增长组合战略规划', description: '为集团制定多元化的增长战略组合（增值服务）', premium: true },
                { code: 'SNT', name: '新赛道评估与优选', description: '评估新业务领域的潜力并推荐最优选择（增值服务）', premium: true }
            ],
            'market-expert': [
                { code: 'A4', name: '竞品分析', description: '深入分析目标市场的主要竞争对手产品和策略' },
                { code: 'A5', name: '客户画像分析', description: '构建目标市场的详细客户画像和需求分析' },
                { code: 'A6', name: '定价策略研究', description: '基于市场环境制定最优的产品定价策略' },
                { code: 'A7', name: '渠道策略分析', description: '评估和推荐最适合的市场进入渠道' },
                { code: 'A8', name: '营销策略研究', description: '制定本地化的营销推广策略和方案' },
                { code: 'A9', name: '风险评估', description: '识别和评估市场进入的各类风险因素' },
                { code: 'MES', name: '市场准入路径与策略', description: '制定完整的市场准入计划（增值服务）', premium: true },
                { code: 'PSE', name: '合作伙伴筛选与评估', description: '寻找和评估最佳的本地合作伙伴（增值服务）', premium: true }
            ],
            'intel-officer': [
                { code: 'T1-T4', name: '常规情报处理与归档', description: '持续监控和处理市场情报（默认进行中）' },
                { code: 'DIP', name: '深度情报分析报告', description: '对特定主题进行深度情报挖掘和分析（增值服务）', premium: true },
                { code: 'CIS', name: '定制化情报筛选', description: '根据设定的关键词提供精细化情报推送（增值服务）', premium: true },
                { code: 'RMA', name: '实时市场预警', description: '针对特定市场提供更频繁的预警通知（增值服务）', premium: true }
            ]
        };

        // Initialize event listeners
        function initEventListeners() {
            // Tree navigation interaction
            document.querySelectorAll('.tree-node-header').forEach(header => {
                header.addEventListener('click', function() {
                    const node = this.parentElement;
                    node.classList.toggle('expanded');
                });
            });

            // Agent selection
            document.querySelectorAll('.tree-leaf').forEach(leaf => {
                leaf.addEventListener('click', function() {
                    // Remove active class from all leaves
                    document.querySelectorAll('.tree-leaf').forEach(l => l.classList.remove('active'));
                    // Add active class to the clicked leaf
                    this.classList.add('active');
                    
                    // Get agent info
                    const agentType = this.dataset.agentType;
                    const agentId = this.dataset.agentId;
                    const agentName = this.querySelector('span:nth-child(2)').textContent;
                    
                    // Determine product line based on agentId (simplified for prototype)
                    let productLine = '产品线A';
                    if (agentId && (agentId.includes('-b') || agentId.includes('jp') || agentId.includes('kr'))) {
                        productLine = '产品线B';
                    }
                    
                    // Update current agent info
                    currentAgent = {
                        type: agentType,
                        id: agentId,
                        name: agentName,
                        productLine: productLine
                    };
                    
                    // Display the corresponding agent card
                    showAgentCard(agentType, agentId);
                });
            });
        }

        // Initialize event listeners when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            initEventListeners();
            // Show default agent card on load
            showAgentCard(currentAgent.type, currentAgent.id);
        });

        // Function to display agent cards dynamically
        function showAgentCard(agentType, agentId) {
            const contentArea = document.getElementById('contentArea');
            let agentCardHtml = '';

            // Dummy data for executive and certification status
            const executiveData = {
                'global-a': { name: '李经理', certified: true },
                'market-de-a': { name: '王总监', certified: false },
                'intel-a': { name: '张总', certified: true },
                'global-b': { name: '赵总', certified: true },
                'market-jp-b': { name: '孙经理', certified: false },
                'market-kr-b': { name: '周总', certified: true },
                'market-fr-a': { name: '刘经理', certified: true },
                'market-us-a': { name: '陈总', certified: false },
                'market-uk-a': { name: '吴经理', certified: true },
                'intel-b': { name: '郑总', certified: true }
            };
            const currentExecutive = executiveData[agentId] || { name: '未指派', certified: false };
            const certificationStatusClass = currentExecutive.certified ? 'status-certified' : 'status-pending';
            const certificationStatusText = currentExecutive.certified ? '✅ 已认证' : '⚠️ 待学习';
            const seatActionBtn = currentExecutive.certified ? 
                `<button class="seat-action-btn">更换负责人</button>` : 
                `<button class="seat-action-btn">指派学习任务</button>`;
            const seatDetailsHtml = currentExecutive.name === '未指派' ? 
                `<div class="seat-details"><div class="seat-avatar">+</div><div class="seat-info"><div class="seat-owner">席位空缺</div></div><div class="seat-actions"><button class="seat-action-btn">指派负责人</button></div></div>` : 
                `<div class="seat-details"><div class="seat-avatar">${currentExecutive.name.charAt(0)}</div><div class="seat-info"><div class="seat-owner">${currentExecutive.name}</div><div class="certification-status ${certificationStatusClass}"><span>${certificationStatusText.split(' ')[0]}</span><span>${certificationStatusText.split(' ')[1]}</span></div></div><div class="seat-actions">${seatActionBtn}</div></div>`;

            // Dummy task data for the unified table
            const unifiedTaskData = {
                'global-strategist': [
                    { name: 'A0: 全球商业环境分析', status: 'idle', history: 3, executive: '李经理' },
                    { name: 'A1: 产业链定位与机会评估', status: 'idle', history: 1, executive: '李经理' },
                    { name: 'A2: 全球竞争格局分析', status: 'progress', progress: 60, history: 5, executive: '李经理', startDate: '2024-07-15' },
                    { name: 'A3: 目标市场研究', status: 'idle', history: 2, executive: '李经理' },
                    { name: 'GGP: 集团增长组合战略规划 (增值)', status: 'idle', history: 0, executive: '李经理' },
                    { name: 'SNT: 新赛道评估与优选 (增值)', status: 'idle', history: 0, executive: '李经理' }
                ],
                'market-expert': [
                    { name: 'A4: 竞品分析', status: 'idle', history: 4, executive: '王总监' },
                    { name: 'A5: 客户画像分析', status: 'progress', progress: 30, history: 2, executive: '王总监', startDate: '2024-07-20' },
                    { name: 'A6: 定价策略研究', status: 'idle', history: 1, executive: '王总监' },
                    { name: 'A7: 渠道策略分析', status: 'idle', history: 0, executive: '王总监' },
                    { name: 'MES: 市场准入路径与策略 (增值)', status: 'idle', history: 0, executive: '王总监' }
                ],
                'intel-officer': [
                    { name: 'T1-T4: 常规情报处理与归档', status: 'progress', progress: 90, history: 999, executive: '张总', startDate: '2024-07-01' },
                    { name: 'DIP: 深度情报分析报告 (增值)', status: 'idle', history: 5, executive: '张总' },
                    { name: 'CIS: 定制化情报筛选 (增值)', status: 'idle', history: 12, executive: '张总' }
                ]
            };

            const tasksForAgent = unifiedTaskData[agentType] || [];
            const taskTableRows = tasksForAgent.map(task => {
                const statusHtml = task.status === 'idle' ? 
                    `<span class="status-idle">空闲</span>` : 
                    `<a href="aip_space.html" class="status-progress-link">
                        <div class="status-progress">
                            <div class="progress-bar-container">
                                <div class="progress-bar-fill" style="width: ${task.progress || 0}%;"></div>
                            </div>
                            <div class="progress-date">开始日期: ${task.startDate || 'N/A'}</div>
                        </div>
                    </a>`;
                const actionHtml = task.status === 'idle' ? 
                    `<button class="btn-delegate" onclick="openTaskModal('${agentType}', '${currentAgent.name}', '${task.name}')">委托任务</button>` : 
                    `<span class="status-inprogress">进行中...</span>`;
                
                return `
                    <tr>
                        <td class="task-name-cell">${task.name}</td>
                        <td class="status-cell">${statusHtml}</td>
                        <td class="history-cell"><a href="#" onclick="openHistoryModal('${task.name}', ${task.history})">${task.history} 次</a></td>
                        <td class="action-cell">${actionHtml}</td>
                    </tr>
                `;
            }).join('');

            agentCardHtml = `
                <div class="agent-card" id="${agentId}-card">
                    <div class="agent-header">
                        <span class="agent-icon">${agentType === 'global-strategist' ? '🌍' : agentType === 'market-expert' ? '🎯' : '📊'}</span>
                        <div class="agent-info">
                            <h2 class="agent-name">${currentAgent.name}</h2>
                            <div class="agent-meta">
                                <span>${currentAgent.productLine}</span>
                                <span>|</span>
                                <span class="agent-status">
                                    <span class="tree-leaf-status status-active"></span>
                                    活跃中
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Collaboration Seat Management -->
                    <div class="agent-section collaboration-seat">
                        <h3 class="section-title">
                            <span>👤</span>
                            <span>协作席位管理</span>
                        </h3>
                        ${seatDetailsHtml}
                    </div>
                    
                    <!-- Unified Task Table -->
                    <div class="agent-section tasks">
                        <h3 class="section-title"><span>🎯</span><span>任务能力清单</span></h3>
                        <table class="task-table">
                            <thead>
                                <tr>
                                    <th>任务名称</th>
                                    <th>当前状态</th>
                                    <th>历史记录</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${taskTableRows}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
            contentArea.innerHTML = agentCardHtml;
        }

        // Open task modal
        function openTaskModal(agentType, agentName, taskName) {
            document.getElementById('taskModal').classList.add('active');
            currentStep = 1;
            showStep(1);
            
            // Populate task list (now only one task selected from the table)
            const taskList = document.getElementById('taskList');
            taskList.innerHTML = `
                <div class="task-option selected" data-task-code="" data-task-name="${taskName}">
                    <div class="task-option-header">
                        <span class="task-code">${taskName.split(':')[0]}</span>
                        <span class="task-name">${taskName}</span>
                    </div>
                    <p class="task-description">${taskName}的详细描述...</p>
                </div>
            `;
            selectedTask = { code: taskName.split(':')[0], name: taskName };
        }

        // Close task modal
        function closeTaskModal() {
            document.getElementById('taskModal').classList.remove('active');
            currentStep = 1;
            selectedTask = null;
            document.querySelectorAll('.step').forEach(s => s.classList.remove('completed'));
            document.querySelector('#step1').classList.add('active');
            document.querySelector('#step2').classList.remove('active');
            document.querySelector('#step3').classList.remove('active');
            document.querySelector('.modal-footer').style.display = 'flex';
        }

        // Show step in modal
        function showStep(step) {
            document.querySelectorAll('.step').forEach((s, index) => {
                if (index + 1 < step) { s.classList.add('completed'); s.classList.remove('active'); }
                else if (index + 1 === step) { s.classList.add('active'); s.classList.remove('completed'); }
                else { s.classList.remove('active', 'completed'); }
            });
            document.querySelectorAll('.step-content').forEach(content => { content.style.display = 'none'; });
            document.getElementById(`step${step}Content`).style.display = 'block';
            if (step === 4) { document.querySelector('.modal-footer').style.display = 'none'; } 
            else { document.querySelector('.modal-footer').style.display = 'flex'; document.getElementById('prevBtn').style.display = step > 1 ? 'block' : 'none'; document.getElementById('nextBtn').textContent = step === 3 ? '确认委托' : '下一步'; }
            if (step === 2) { fillDynamicParams(); document.getElementById('agentDisplay').value = currentAgent.name; }
            if (step === 3) { fillSummary(); }
        }

        // Fill dynamic parameters for task configuration (simplified for prototype)
        function fillDynamicParams() {
            const dynamicParams = document.getElementById('dynamicParams');
            dynamicParams.innerHTML = `
                <div class="form-group">
                    <label class="form-label">补充说明</label>
                    <textarea class="form-textarea" id="additionalNotes" placeholder="请提供任何额外的要求或说明（可选）"></textarea>
                </div>
            `;
        }

        // Fill summary for review step (simplified for prototype)
        function fillSummary() {
            document.getElementById('summaryAgent').textContent = currentAgent.name;
            document.getElementById('summaryProductLine').textContent = currentAgent.productLine;
            document.getElementById('summaryTask').textContent = selectedTask.name;
            document.getElementById('summaryTaskName').textContent = document.getElementById('taskNameInput').value || '未命名任务';
            document.getElementById('summaryParams').innerHTML = `<div class="summary-row"><span class="summary-label">参数配置:</span><span class="summary-value">使用默认配置</span></div>`;
        }

        // Next step in modal
        function nextStep() {
            if (currentStep === 1 && !selectedTask) {
                alert('请选择一个分析任务');
                return;
            }
            if (currentStep < 3) {
                currentStep++;
                showStep(currentStep);
            } else if (currentStep === 3) {
                currentStep = 4;
                showStep(4);
            }
        }

        // Previous step in modal
        function previousStep() {
            if (currentStep > 1) {
                currentStep--;
                showStep(currentStep);
            }
        }

        // Close modal on overlay click
        document.getElementById('taskModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeTaskModal();
            }
        });

        // Dummy function for adding market advisor
        function showAddMarketAdvisorModal(productLine) {
            alert(`即将为${productLine}添加新的市场顾问\n\n添加功能正在开发中...`);
        }

        // History Modal Functions
        function openHistoryModal(taskName, historyCount) {
            document.getElementById('historyModal').classList.add('active');
            document.getElementById('historyModalTitle').textContent = `${taskName} - 历史记录`;
            const historyTableBody = document.getElementById('historyTableBody');
            let historyRows = '';
            // Dummy data for history
            for (let i = 0; i < historyCount; i++) {
                const date = new Date(2024, 0, 1 + i).toISOString().split('T')[0]; // Example dates
                const status = i % 2 === 0 ? '已完成' : '已完成 (部分)';
                historyRows += `
                    <tr>
                        <td>${date}</td>
                        <td>${status}</td>
                        <td><a href="aip_space.html">查看详情</a></td>
                    </tr>
                `;
            }
            historyTableBody.innerHTML = historyRows;
        }

        function closeHistoryModal() {
            document.getElementById('historyModal').classList.remove('active');
        }
    </script>
</body>
</html>
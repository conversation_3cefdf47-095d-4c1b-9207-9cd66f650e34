<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Foxu.AI - 知识库浏览器</title>
    <style>
        /* 全局重置和基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f9fafb;
            color: #1f2937;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 背景动画效果 */
        .background-animation {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
            opacity: 0.03;
            overflow: hidden;
        }

        .background-animation::before,
        .background-animation::after {
            content: "";
            position: absolute;
            width: 600px;
            height: 600px;
            border-radius: 50%;
            animation: atmosphericFloat 20s ease-in-out infinite;
        }

        .background-animation::before {
            background: radial-gradient(circle, #667eea 0%, transparent 70%);
            top: -300px;
            left: -300px;
        }

        .background-animation::after {
            background: radial-gradient(circle, #764ba2 0%, transparent 70%);
            bottom: -300px;
            right: -300px;
            animation-delay: -10s;
        }

        @keyframes atmosphericFloat {
            0%, 100% { transform: translate(0, 0) scale(1); }
            33% { transform: translate(30px, -30px) scale(1.1); }
            66% { transform: translate(-20px, 20px) scale(0.9); }
        }

        /* 全局导航栏 */
        .navbar {
            position: sticky;
            top: 0;
            height: 60px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(12px);
            border-bottom: 1px solid #e5e7eb;
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: 700;
            background: linear-gradient(135deg, #2563eb, #9333ea);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-right: 48px;
        }

        .navbar-menu {
            display: flex;
            gap: 32px;
            flex: 1;
        }

        .navbar-item {
            color: #374151;
            text-decoration: none;
            font-size: 15px;
            font-weight: 500;
            transition: color 0.2s;
            position: relative;
        }

        .navbar-item:hover {
            color: #2563eb;
        }

        .navbar-item.active {
            color: #2563eb;
        }

        .navbar-item.active::after {
            content: '';
            position: absolute;
            bottom: -18px;
            left: 0;
            right: 0;
            height: 3px;
            background: #2563eb;
            border-radius: 3px 3px 0 0;
        }

        .navbar-actions {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .notification-badge {
            position: relative;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .notification-badge:hover {
            background: #e5e7eb;
            transform: scale(1.05);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .user-avatar:hover {
            transform: scale(1.05);
        }

        /* 页面头部 */
        /* .page-header {
            background: white;
            padding: 16px 24px;
            border-bottom: 1px solid #e5e7eb;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 8px;
        }

        .breadcrumb a {
            color: #2563eb;
            text-decoration: none;
        } */

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .page-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .page-description {
            font-size: 14px;
            color: #6b7280;
        }

        /* 主内容区域 -90 */
        .main-container {
            display: flex;
            height: calc(100vh - 60px);
        }

        /* 左侧树状导航 */
        .sidebar {
            width: 20%;
            min-width: 300px;
            background: white;
            border-right: 1px solid #e5e7eb;
            overflow-y: auto;
            padding: 24px;
        }

        .sidebar-search {
            position: relative;
            margin-bottom: 24px;
        }

        .sidebar-search input {
            width: 100%;
            padding: 10px 40px 10px 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .sidebar-search input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .sidebar-search::after {
            content: '🔍';
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 16px;
            color: #6b7280;
        }

        /* 树状导航样式 */
        .tree-nav {
            font-size: 14px;
        }

        .tree-node {
            margin-bottom: 4px;
        }

        .tree-node-header {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            user-select: none;
        }

        .tree-node-header:hover {
            background: #f3f4f6;
        }

        .tree-node-header.active {
            background: #eff6ff;
            color: #2563eb;
        }

        .tree-node-icon {
            font-size: 16px;
            margin-right: 8px;
        }

        .tree-node-label {
            flex: 1;
            font-weight: 500;
        }

        .tree-node-count {
            background: #e5e7eb;
            color: #374151;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .tree-node-toggle {
            font-size: 12px;
            color: #6b7280;
            margin-right: 4px;
            transition: transform 0.2s;
        }

        .tree-node.expanded .tree-node-toggle {
            transform: rotate(90deg);
        }

        .tree-children {
            padding-left: 24px;
            display: none;
        }

        .tree-node.expanded .tree-children {
            display: block;
        }

        .tree-leaf {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .tree-leaf:hover {
            background: #f3f4f6;
        }

        .tree-leaf.active {
            background: #dbeafe;
            color: #1d4ed8;
        }

        .tree-leaf-icon {
            font-size: 16px;
            margin-right: 8px;
        }

        .tree-leaf-status {
            margin-left: auto;
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-active {
            background: #10b981;
        }

        .status-paused {
            background: #f59e0b;
        }

        .status-error {
            background: #dc2626;
        }

        /* 右侧内容区域 */
        .content {
            flex: 1;
            background: #f9fafb;
            overflow-y: auto;
            padding: 24px;
        }

        /* 企业战略库样式 */
        .upload-area {
            background: white;
            border: 2px dashed #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            margin-bottom: 24px;
            transition: border-color 0.3s;
        }

        .upload-area:hover {
            border-color: #2563eb;
        }

        .upload-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.3;
        }

        .upload-btn {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(37, 99, 235, 0.3);
        }

        .filter-bar {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            display: flex;
            gap: 16px;
            align-items: end;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .filter-group {
            flex: 1;
        }

        .filter-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .filter-select,
        .search-input {
            width: 100%;
            padding: 10px 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .filter-select:focus,
        .search-input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .search-group {
            flex: 2;
            position: relative;
        }

        .search-input {
            padding-left: 40px;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
        }

        /* 报告列表表格 */
        .report-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .table-header {
            display: grid;
            grid-template-columns: 2.2fr 1fr 1fr 0.8fr 0.8fr 1fr 1.2fr 0.8fr;
            padding: 16px 24px;
            background: #f3f4f6;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table-row {
            display: grid;
            grid-template-columns: 2.2fr 1fr 1fr 0.8fr 0.8fr 1fr 1.2fr 0.8fr;
            padding: 16px 24px;
            border-bottom: 1px solid #f3f4f6;
            font-size: 14px;
            transition: background-color 0.2s;
            cursor: pointer;
            align-items: center;
        }

        .table-row:hover {
            background: #f9fafb;
        }

        .report-name {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #1f2937;
        }

        .decision-badge {
            background: #ef4444;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        /* 目标市场库样式 */
        .world-map-container {
            background: white;
            border-radius: 12px;
            padding: 32px;
            height: calc(100vh - 240px);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .map-placeholder {
            text-align: center;
        }

        .map-svg {
            max-width: 100%;
            height: auto;
        }

        .region {
            transition: all 0.3s;
            cursor: pointer;
        }

        .region:hover {
            filter: brightness(1.1);
            transform: scale(1.02);
        }

        .region.disabled {
            cursor: not-allowed;
            opacity: 0.5;
        }

        /* 市场概览弹窗 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .modal-overlay.active {
            display: flex;
        }

        .modal {
            background: white;
            border-radius: 16px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
            padding: 24px 32px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 700;
            color: #1f2937;
        }

        .close-btn {
            background: #f3f4f6;
            border: none;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }

        .close-btn:hover {
            background: #e5e7eb;
        }

        .modal-body {
            flex: 1;
            overflow-y: auto;
            padding: 32px;
        }

        .market-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            margin-bottom: 32px;
        }

        .metric-card {
            background: #f3f4f6;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }

        .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .metric-label {
            font-size: 12px;
            color: #6b7280;
        }

        .section {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .alert-box {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            color: #92400e;
            margin-bottom: 16px;
        }

        .product-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .product-tag {
            background: #dbeafe;
            color: #1e40af;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        /* 公司/产品库样式 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 24px;
        }

        .entity-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s;
            cursor: pointer;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .entity-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .entity-icon {
            width: 64px;
            height: 64px;
            background: #f3f4f6;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-bottom: 16px;
        }

        .entity-name {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .entity-tags {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
        }

        .entity-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .tag-competitor {
            background: #fee2e2;
            color: #dc2626;
        }

        .tag-partner {
            background: #d1fae5;
            color: #059669;
        }

        .tag-growth {
            background: #fed7aa;
            color: #ea580c;
        }

        .entity-description {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
            margin-bottom: 12px;
        }

        .entity-meta {
            font-size: 12px;
            color: #2563eb;
            font-weight: 500;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 80px 40px;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.3;
        }

        .empty-title {
            font-size: 20px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .empty-description {
            font-size: 14px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 1199px) {
            .sidebar {
                width: 35%;
                min-width: 280px;
            }
            
            .card-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }
        }

        @media (max-width: 767px) {
            .navbar-menu {
                display: none;
            }

            .main-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                border-right: none;
                border-bottom: 1px solid #e5e7eb;
            }

            .content {
                padding: 16px;
            }

            .filter-bar {
                flex-direction: column;
            }

            .table-header,
            .table-row {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .table-header > div:not(:first-child),
            .table-row > div:not(:first-child) {
                display: none;
            }
        }
    </style>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-geo@4"></script>
</head>
<body>
    <!-- 背景动画 -->
    <div class="background-animation"></div>

    <!-- 全局导航栏 -->
    <nav class="navbar">
        <div class="navbar-brand">Foxu.AI</div>
        <div class="navbar-menu">
            <a href="index.html" class="navbar-item">首页</a>
            <a href="task_delegation.html" class="navbar-item">任务委托中心</a>
            <a href="#" class="navbar-item active">知识库</a>
            <a href="cadre_competency_learning.html" class="navbar-item">干部能力与学习系统</a>
            <a href="cadre_competency_admin.html" class="navbar-item">干部能力配置</a>
            <a href="team_management.html" class="navbar-item">企业管理</a>
        </div>
        <div class="navbar-actions">
            <div class="notification-badge">
                <span>🔔</span>
            </div>
            <div class="user-avatar">JD</div>
        </div>
    </nav>

    <!-- 页面头部 -->
    <!-- <div class="page-header">
        <div class="breadcrumb">
            <a href="index.html">首页</a>
            <span>></span>
            <span>知识库浏览器</span>
        </div>
        <div class="page-title">探索和管理企业智慧知识库</div>
        <p class="page-description"></p>
    </div> -->

    <!-- 主内容区域 -->
    <div class="main-container">
        <!-- 左侧树状导航 -->
        <aside class="sidebar">
            <div class="sidebar-search">
                <input type="text" placeholder="搜索知识库...">
            </div>
            
            <div class="tree-nav">
                <!-- 知识库浏览器 - 单层结构 -->
                <div class="tree-leaf active" onclick="showContent('strategy')">
                    <span class="tree-leaf-icon">📋</span>
                    <span>企业战略库</span>
                </div>
                <div class="tree-leaf" onclick="showContent('market')">
                    <span class="tree-leaf-icon">🌍</span>
                    <span>目标市场库</span>
                </div>
                <div class="tree-leaf" onclick="showContent('company')">
                    <span class="tree-leaf-icon">🏢</span>
                    <span>公司库</span>
                </div>
                <div class="tree-leaf" onclick="showContent('product')">
                    <span class="tree-leaf-icon">📦</span>
                    <span>产品库</span>
                </div>
            </div>
        </aside>

        <!-- 右侧内容区域 -->
        <main class="content" id="contentArea">
            <!-- 默认显示企业战略库 -->
            <div id="strategyContent">
                <div class="filter-bar">
                    <div class="filter-group">
                        <label class="filter-label">核心产品线</label>
                        <select class="filter-select">
                            <option>全部产品线</option>
                            <option>新能源汽车</option>
                            <option>智能制造</option>
                            <option>医疗器械</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">年度</label>
                        <select class="filter-select">
                            <option>2024</option>
                            <option>2023</option>
                            <option>2022</option>
                        </select>
                    </div>
                    <div class="search-group">
                        <label class="filter-label">搜索</label>
                        <div style="position: relative;">
                            <span class="search-icon">🔍</span>
                            <input type="text" class="search-input" placeholder="搜索报告名称或关键词...">
                        </div>
                    </div>
                </div>
                
                <div class="report-table">
                    <div class="table-header">
                        <div>报告名称</div>
                        <div>创建者</div>
                        <div>创建日期</div>
                        <div>调研数量</div>
                        <div>报告数量</div>
                        <div>决策状态</div>
                        <div>所属产品线</div>
                        <div>所属年度</div>
                    </div>
                    <div class="table-row" onclick="window.location.href='aip_space.html'">
                        <div class="report-name">
                            全球商机扫描报告
                            <span class="decision-badge">决策简报</span>
                        </div>
                        <div>全球战略官</div>
                        <div>2024-01-15</div>
                        <div><span style="font-weight: 600; color: #2563eb;">23</span></div>
                        <div><span style="font-weight: 600; color: #10b981;">8</span></div>
                        <div><span style="background: #dcfce7; color: #166534; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">已决策</span></div>
                        <div>新能源汽车</div>
                        <div>2024</div>
                    </div>
                    <div class="table-row">
                        <div class="report-name">竞争格局分析报告</div>
                        <div>市场拓展专家</div>
                        <div>2024-01-10</div>
                        <div><span style="font-weight: 600; color: #2563eb;">15</span></div>
                        <div><span style="font-weight: 600; color: #10b981;">5</span></div>
                        <div><span style="background: #fef3c7; color: #92400e; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">评估中</span></div>
                        <div>智能制造</div>
                        <div>2024</div>
                    </div>
                    <div class="table-row">
                        <div class="report-name">
                            行业趋势预测报告
                            <span class="decision-badge">决策简报</span>
                        </div>
                        <div>全球战略官</div>
                        <div>2023-12-20</div>
                        <div><span style="font-weight: 600; color: #2563eb;">31</span></div>
                        <div><span style="font-weight: 600; color: #10b981;">12</span></div>
                        <div><span style="background: #dcfce7; color: #166534; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">已决策</span></div>
                        <div>全产品线</div>
                        <div>2023</div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- 市场概览弹窗 -->
    <div class="modal-overlay" id="marketModal">
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title" id="marketName">德国</h2>
                <button class="close-btn" onclick="closeModal()">✕</button>
            </div>
            <div class="modal-body">
                <div class="market-metrics">
                    <div class="metric-card">
                        <div class="metric-value">€3.8T</div>
                        <div class="metric-label">市场规模</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">2.3%</div>
                        <div class="metric-label">增长率</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">高</div>
                        <div class="metric-label">竞争激烈度</div>
                    </div>
                </div>
                
                <div class="section">
                    <h3 class="section-title">
                        <span>⚠️</span>
                        <span>最新情报</span>
                    </h3>
                    <div class="alert-box">
                        市场情报官提醒：德国新能源汽车补贴政策将于下月调整，可能影响市场需求
                    </div>
                </div>
                
                <div class="section">
                    <h3 class="section-title">
                        <span>📦</span>
                        <span>活跃产品线</span>
                    </h3>
                    <div class="product-tags">
                        <span class="product-tag">新能源汽车</span>
                        <span class="product-tag">智能制造</span>
                        <span class="product-tag">医疗器械</span>
                    </div>
                </div>
                
                <div class="section">
                    <h3 class="section-title">
                        <span>📄</span>
                        <span>相关报告</span>
                    </h3>
                    <div class="report-table">
                        <div class="table-row" style="margin: 0 -32px; padding-left: 32px; padding-right: 32px;">
                            <div class="report-name">
                                德国市场进入策略分析 (2024)
                                <span class="decision-badge">决策简报</span>
                            </div>
                        </div>
                        <div class="table-row" style="margin: 0 -32px; padding-left: 32px; padding-right: 32px;">
                            <div class="report-name">
                                德国竞争格局分析 (2024)
                            </div>
                        </div>
                    </div>
                </div>
                
                <button class="upload-btn" style="margin-top: 24px;">上传文件</button>
            </div>
        </div>
    </div>

    <script>
        // 移除树状导航交互，因为现在是单层结构

        // 显示不同内容
        function showContent(type) {
            // 更新导航激活状态
            document.querySelectorAll('.tree-leaf').forEach(leaf => {
                leaf.classList.remove('active');
            });
            event.currentTarget.classList.add('active');
            
            const contentArea = document.getElementById('contentArea');
            
            switch(type) {
                case 'strategy':
                    contentArea.innerHTML = `
                        <div class="filter-bar">
                            <div class="filter-group">
                                <label class="filter-label">核心产品线</label>
                                <select class="filter-select">
                                    <option>全部产品线</option>
                                    <option>新能源汽车</option>
                                    <option>智能制造</option>
                                    <option>医疗器械</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label class="filter-label">年度</label>
                                <select class="filter-select">
                                    <option>2024</option>
                                    <option>2023</option>
                                    <option>2022</option>
                                </select>
                            </div>
                            <div class="search-group">
                                <label class="filter-label">搜索</label>
                                <div style="position: relative;">
                                    <span class="search-icon">🔍</span>
                                    <input type="text" class="search-input" placeholder="搜索报告名称或关键词...">
                                </div>
                            </div>
                        </div>
                        
                        <div class="report-table">
                            <div class="table-header">
                                <div>报告名称</div>
                                <div>创建者</div>
                                <div>创建日期</div>
                                <div>调研数量</div>
                                <div>报告数量</div>
                                <div>决策状态</div>
                                <div>所属产品线</div>
                                <div>所属年度</div>
                            </div>
                            <div class="table-row" onclick="window.location.href='aip_space.html'">
                                <div class="report-name">
                                    全球商机扫描报告
                                    <span class="decision-badge">决策简报</span>
                                </div>
                                <div>全球战略官</div>
                                <div>2024-01-15</div>
                                <div><span style="font-weight: 600; color: #2563eb;">23</span></div>
                                <div><span style="font-weight: 600; color: #10b981;">8</span></div>
                                <div><span style="background: #dcfce7; color: #166534; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">已决策</span></div>
                                <div>新能源汽车</div>
                                <div>2024</div>
                            </div>
                            <div class="table-row">
                                <div class="report-name">竞争格局分析报告</div>
                                <div>市场拓展专家</div>
                                <div>2024-01-10</div>
                                <div><span style="font-weight: 600; color: #2563eb;">15</span></div>
                                <div><span style="font-weight: 600; color: #10b981;">5</span></div>
                                <div><span style="background: #fef3c7; color: #92400e; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">评估中</span></div>
                                <div>智能制造</div>
                                <div>2024</div>
                            </div>
                            <div class="table-row">
                                <div class="report-name">
                                    行业趋势预测报告
                                    <span class="decision-badge">决策简报</span>
                                </div>
                                <div>全球战略官</div>
                                <div>2023-12-20</div>
                                <div><span style="font-weight: 600; color: #2563eb;">31</span></div>
                                <div><span style="font-weight: 600; color: #10b981;">12</span></div>
                                <div><span style="background: #dcfce7; color: #166534; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">已决策</span></div>
                                <div>全产品线</div>
                                <div>2023</div>
                            </div>
                        </div>
                    `;
                    break;
                    
                case 'market':
                    contentArea.innerHTML = `
                        <div class="filter-bar">
                            <div class="filter-group">
                                <label class="filter-label">国家/地区</label>
                                <select class="filter-select">
                                    <option>全部国家</option>
                                    <option>德国</option>
                                    <option>法国</option>
                                    <option>英国</option>
                                    <option>意大利</option>
                                    <option>美国</option>
                                    <option>日本</option>
                                    <option>中国</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label class="filter-label">核心产品线</label>
                                <select class="filter-select">
                                    <option>全部产品线</option>
                                    <option>新能源汽车</option>
                                    <option>智能制造</option>
                                    <option>医疗器械</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label class="filter-label">年度</label>
                                <select class="filter-select">
                                    <option>2024</option>
                                    <option>2023</option>
                                    <option>2022</option>
                                </select>
                            </div>
                            <div class="search-group">
                                <label class="filter-label">搜索</label>
                                <div style="position: relative;">
                                    <span class="search-icon">🔍</span>
                                    <input type="text" class="search-input" placeholder="搜索报告名称或关键词...">
                                </div>
                            </div>
                        </div>
                        
                        <div class="report-table">
                            <div class="table-header">
                                <div>报告名称</div>
                                <div>创建者</div>
                                <div>创建日期</div>
                                <div>调研数量</div>
                                <div>报告数量</div>
                                <div>决策状态</div>
                                <div>所属产品线</div>
                                <div>国家/地区</div>
                            </div>
                            <div class="table-row">
                                <div class="report-name">
                                    德国市场分析报告
                                    <span class="decision-badge">决策简报</span>
                                </div>
                                <div>市场拓展专家</div>
                                <div>2024-01-20</div>
                                <div><span style="font-weight: 600; color: #2563eb;">18</span></div>
                                <div><span style="font-weight: 600; color: #10b981;">6</span></div>
                                <div><span style="background: #dcfce7; color: #166534; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">已决策</span></div>
                                <div>新能源汽车</div>
                                <div>德国</div>
                            </div>
                            <div class="table-row">
                                <div class="report-name">法国消费者行为研究</div>
                                <div>全球战略官</div>
                                <div>2024-01-18</div>
                                <div><span style="font-weight: 600; color: #2563eb;">12</span></div>
                                <div><span style="font-weight: 600; color: #10b981;">4</span></div>
                                <div><span style="background: #e0e7ff; color: #3730a3; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">进行中</span></div>
                                <div>智能制造</div>
                                <div>法国</div>
                            </div>
                            <div class="table-row">
                                <div class="report-name">
                                    英国市场准入策略
                                    <span class="decision-badge">决策简报</span>
                                </div>
                                <div>市场拓展专家</div>
                                <div>2024-01-15</div>
                                <div><span style="font-weight: 600; color: #2563eb;">25</span></div>
                                <div><span style="font-weight: 600; color: #10b981;">9</span></div>
                                <div><span style="background: #dcfce7; color: #166534; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">已决策</span></div>
                                <div>医疗器械</div>
                                <div>英国</div>
                            </div>
                        </div>
                    `;
                    break;
                    
                case 'company':
                    contentArea.innerHTML = `
                        <div class="filter-bar">
                            <div class="filter-group">
                                <label class="filter-label">核心产品线</label>
                                <select class="filter-select">
                                    <option>全部产品线</option>
                                    <option>新能源汽车</option>
                                    <option>智能制造</option>
                                    <option>医疗器械</option>
                                </select>
                            </div>
                            <div class="search-group" style="flex: 3;">
                                <label class="filter-label">搜索</label>
                                <div style="position: relative;">
                                    <span class="search-icon">🔍</span>
                                    <input type="text" class="search-input" placeholder="搜索公司名称...">
                                </div>
                            </div>
                        </div>
                        
                        <div class="card-grid">
                            <div class="entity-card">
                                <div class="entity-icon">🏢</div>
                                <h3 class="entity-name">特斯拉汽车</h3>
                                <div class="entity-tags">
                                    <span class="entity-tag tag-competitor">竞争对手</span>
                                    <span class="entity-tag tag-growth">高增长</span>
                                </div>
                                <p class="entity-description">全球领先的电动汽车制造商，近期在德国市场扩张迅速</p>
                                <div class="entity-meta">产品线：新能源汽车</div>
                            </div>
                            
                            <div class="entity-card">
                                <div class="entity-icon">🏭</div>
                                <h3 class="entity-name">西门子集团</h3>
                                <div class="entity-tags">
                                    <span class="entity-tag tag-partner">合作伙伴</span>
                                </div>
                                <p class="entity-description">德国工业巨头，在智能制造领域有深度合作</p>
                                <div class="entity-meta">产品线：智能制造</div>
                            </div>
                            
                            <div class="entity-card">
                                <div class="entity-icon">🏥</div>
                                <h3 class="entity-name">美敦力公司</h3>
                                <div class="entity-tags">
                                    <span class="entity-tag tag-competitor">竞争对手</span>
                                </div>
                                <p class="entity-description">全球医疗器械领导者，在心血管产品领域竞争激烈</p>
                                <div class="entity-meta">产品线：医疗器械</div>
                            </div>
                            
                            <div class="entity-card">
                                <div class="entity-icon">🚗</div>
                                <h3 class="entity-name">比亚迪汽车</h3>
                                <div class="entity-tags">
                                    <span class="entity-tag tag-competitor">竞争对手</span>
                                    <span class="entity-tag tag-growth">高增长</span>
                                </div>
                                <p class="entity-description">中国新能源汽车领军企业，欧洲市场扩张积极</p>
                                <div class="entity-meta">产品线：新能源汽车</div>
                            </div>
                        </div>
                    `;
                    break;
                    
                case 'product':
                    contentArea.innerHTML = `
                        <div class="card-grid">
                            <div class="entity-card">
                                <div class="entity-icon">🚙</div>
                                <h3 class="entity-name">Model Y</h3>
                                <div class="entity-tags">
                                    <span class="entity-tag tag-competitor">竞品</span>
                                    <span class="entity-tag tag-growth">热销</span>
                                </div>
                                <p class="entity-description">特斯拉旗下SUV车型，2024年欧洲市场销量第一</p>
                                <div class="entity-meta">所属公司：特斯拉</div>
                            </div>
                            
                            <div class="entity-card">
                                <div class="entity-icon">🏭</div>
                                <h3 class="entity-name">SIMATIC S7-1500</h3>
                                <div class="entity-tags">
                                    <span class="entity-tag tag-partner">合作产品</span>
                                </div>
                                <p class="entity-description">西门子高端PLC控制器，工业4.0核心产品</p>
                                <div class="entity-meta">所属公司：西门子</div>
                            </div>
                        </div>
                    `;
                    break;
                    
                case 'learning':
                    contentArea.innerHTML = `
                        <div class="empty-state">
                            <div class="empty-icon">🎓</div>
                            <div class="empty-title">干部学习系统</div>
                            <div class="empty-description">此功能正在开发中</div>
                        </div>
                    `;
                    break;
                    
                case 'analysis':
                    contentArea.innerHTML = `
                        <div class="empty-state">
                            <div class="empty-icon">📊</div>
                            <div class="empty-title">发起分析</div>
                            <div class="empty-description">此功能正在开发中</div>
                        </div>
                    `;
                    break;
                    
                case 'settings':
                    contentArea.innerHTML = `
                        <div class="empty-state">
                            <div class="empty-icon">⚙️</div>
                            <div class="empty-title">系统设置</div>
                            <div class="empty-description">此功能正在开发中</div>
                        </div>
                    `;
                    break;
            }
        }
        
        // 处理地图点击
        function handleMapClick(event) {
            const region = event.target.closest('.region');
            if (region && !region.classList.contains('disabled')) {
                const regionName = region.dataset.region;
                const regionNames = {
                    'europe': '欧洲',
                    'north-america': '北美',
                    'asia': '亚洲',
                    'oceania': '大洋洲'
                };
                
                document.getElementById('marketName').textContent = regionNames[regionName];
                document.getElementById('marketModal').classList.add('active');
            }
        }
        
        // 关闭弹窗
        function closeModal() {
            document.getElementById('marketModal').classList.remove('active');
        }
        
        // 点击遮罩关闭弹窗
        document.getElementById('marketModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
        
        // 初始化世界地图
        function initWorldMap() {
            const ctx = document.getElementById('worldMapChart');
            if (!ctx) return;
            
            // 市场数据
            const marketData = [
                { country: '美国', value: 95, growth: 12, region: 'north-america' },
                { country: '德国', value: 88, growth: 8, region: 'europe' },
                { country: '法国', value: 75, growth: -5, region: 'europe' },
                { country: '英国', value: 70, growth: 6, region: 'europe' },
                { country: '日本', value: 82, growth: 0, region: 'asia' },
                { country: '韩国', value: 65, growth: 15, region: 'asia' },
                { country: '中国', value: 78, growth: 20, region: 'asia' },
                { country: '澳大利亚', value: 60, growth: 10, region: 'oceania' },
                { country: '巴西', value: 45, growth: 5, region: 'south-america', disabled: true },
                { country: '南非', value: 40, growth: 3, region: 'africa', disabled: true }
            ];
            
            // 创建水平条形图模拟地图效果
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: marketData.map(d => d.country),
                    datasets: [{
                        label: '市场潜力指数',
                        data: marketData.map(d => d.value),
                        backgroundColor: marketData.map(d => d.disabled ? '#9ca3af' : '#2563eb'),
                        borderColor: marketData.map(d => d.disabled ? '#9ca3af' : '#1d4ed8'),
                        borderWidth: 1
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                afterLabel: function(context) {
                                    const index = context.dataIndex;
                                    const growth = marketData[index].growth;
                                    const growthText = growth > 0 ? `↑ ${growth}%` : growth < 0 ? `↓ ${Math.abs(growth)}%` : `→ 0%`;
                                    return `增长率: ${growthText}`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value;
                                }
                            },
                            grid: {
                                display: true
                            }
                        },
                        y: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    onClick: function(event, elements) {
                        if (elements.length > 0) {
                            const index = elements[0].index;
                            const market = marketData[index];
                            if (!market.disabled) {
                                // 显示市场详情
                                document.getElementById('marketName').textContent = market.country;
                                document.getElementById('marketModal').classList.add('active');
                            }
                        }
                    }
                }
            });
        }
        
        // 显示市场详情
        function showMarketDetail(market) {
            const marketNames = {
                'usa': '美国',
                'germany': '德国',
                'japan': '日本',
                'france': '法国'
            };
            
            document.getElementById('marketName').textContent = marketNames[market] || '未知市场';
            document.getElementById('marketModal').classList.add('active');
        }
    </script>
</body>
</html>
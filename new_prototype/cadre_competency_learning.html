<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Foxu.AI - 我的能力发展中心</title>
    <style>
        /* 全局重置和基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f9fafb;
            color: #1f2937;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 背景动画效果 */
        .background-animation {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
            opacity: 0.03;
            overflow: hidden;
        }

        .background-animation::before,
        .background-animation::after {
            content: "";
            position: absolute;
            width: 600px;
            height: 600px;
            border-radius: 50%;
            animation: atmosphericFloat 20s ease-in-out infinite;
        }

        .background-animation::before {
            background: radial-gradient(circle, #667eea 0%, transparent 70%);
            top: -300px;
            left: -300px;
        }

        .background-animation::after {
            background: radial-gradient(circle, #764ba2 0%, transparent 70%);
            bottom: -300px;
            right: -300px;
            animation-delay: -10s;
        }

        @keyframes atmosphericFloat {
            0%, 100% { transform: translate(0, 0) scale(1); }
            33% { transform: translate(30px, -30px) scale(1.1); }
            66% { transform: translate(-20px, 20px) scale(0.9); }
        }

        /* 全局导航栏 */
        .navbar {
            position: sticky;
            top: 0;
            height: 60px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(12px);
            border-bottom: 1px solid #e5e7eb;
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: 700;
            background: linear-gradient(135deg, #2563eb, #9333ea);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-right: 48px;
        }

        .navbar-menu {
            display: flex;
            gap: 32px;
            flex: 1;
        }

        .navbar-item {
            color: #374151;
            text-decoration: none;
            font-size: 15px;
            font-weight: 500;
            transition: color 0.2s;
            position: relative;
        }

        .navbar-item:hover {
            color: #2563eb;
        }

        .navbar-item.active {
            color: #2563eb;
        }

        .navbar-item.active::after {
            content: '';
            position: absolute;
            bottom: -18px;
            left: 0;
            right: 0;
            height: 3px;
            background: #2563eb;
            border-radius: 3px 3px 0 0;
        }

        .navbar-actions {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s;
        }

        /* 主容器 */
        .main-container {
            display: grid;
            grid-template-columns: 300px 1fr 380px;
            height: calc(100vh - 60px);
            background: white;
        }

        /* 左侧边栏 - 能力进度概览 */
        .sidebar {
            background: #f9fafb;
            border-right: 1px solid #e5e7eb;
            padding: 24px;
            overflow-y: auto;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .progress-overview {
            margin-bottom: 32px;
        }

        .progress-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            transition: all 0.2s;
            cursor: pointer;
        }

        .progress-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .progress-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .progress-card-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
        }

        .progress-status {
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 500;
        }

        .status-not-started {
            background: #f3f4f6;
            color: #6b7280;
        }

        .status-in-progress {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-certified {
            background: #d1fae5;
            color: #065f46;
        }

        .progress-bar {
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
            margin-top: 8px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2563eb, #3b82f6);
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
            font-size: 12px;
            color: #6b7280;
        }

        /* 中央内容区 - 能力地图可视化 */
        .content {
            padding: 32px;
            overflow-y: auto;
            background: white;
        }

        .content-header {
            margin-bottom: 32px;
        }

        .content-title {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .content-subtitle {
            font-size: 16px;
            color: #6b7280;
        }

        .view-toggle {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
        }

        .view-btn {
            padding: 8px 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            color: #374151;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .view-btn:hover {
            background: #f3f4f6;
        }

        .view-btn.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        /* 能力地图容器 */
        .competency-map-container {
            background: #f9fafb;
            border-radius: 16px;
            padding: 32px;
            min-height: 600px;
            position: relative;
        }

        /* 树形视图 */
        .tree-view {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .domain-group {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .domain-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            cursor: pointer;
        }

        .domain-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #eff6ff, #e0e7ff);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .domain-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            flex: 1;
        }

        .domain-progress {
            font-size: 14px;
            color: #6b7280;
        }

        .competencies-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 16px;
        }

        .competency-card {
            background: #f9fafb;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 16px;
            position: relative;
            cursor: pointer;
            transition: all 0.2s;
        }

        .competency-card:hover {
            border-color: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.1);
        }

        .competency-card.not-started {
            border-style: dashed;
            background: #fafafa;
        }

        .competency-card.in-progress {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #eff6ff, #f9fafb);
        }

        .competency-card.certified {
            border-color: #10b981;
            background: linear-gradient(135deg, #f0fdf4, #f9fafb);
        }

        .competency-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 12px;
        }

        .competency-title {
            font-size: 15px;
            font-weight: 600;
            color: #1f2937;
        }

        .competency-badge {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            font-size: 14px;
        }

        .badge-certified {
            background: #10b981;
            color: white;
        }

        .competency-meta {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-bottom: 12px;
        }

        .meta-tag {
            padding: 2px 8px;
            background: #e5e7eb;
            border-radius: 10px;
            font-size: 11px;
            color: #4b5563;
        }

        .competency-stats {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #6b7280;
        }

        /* 右侧边栏 - 学习资源 */
        .resource-panel {
            background: #f9fafb;
            border-left: 1px solid #e5e7eb;
            padding: 24px;
            overflow-y: auto;
        }

        .resource-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .resource-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        /* 能力详情部分 */
        .competency-details {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .detail-section {
            margin-bottom: 20px;
        }

        .detail-section:last-child {
            margin-bottom: 0;
        }

        .detail-label {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
        }

        .detail-content {
            font-size: 14px;
            color: #1f2937;
            line-height: 1.6;
        }

        .progress-detail {
            margin-top: 12px;
        }

        .progress-stats {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #6b7280;
            margin-top: 8px;
        }

        .kb-links {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-top: 8px;
        }

        .kb-link {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: #eff6ff;
            border: 1px solid #dbeafe;
            border-radius: 8px;
            font-size: 13px;
            color: #1e40af;
            cursor: pointer;
            transition: all 0.2s;
        }

        .kb-link:hover {
            background: #dbeafe;
            border-color: #bfdbfe;
        }

        .resource-filter {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
        }

        .filter-chip {
            padding: 6px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 16px;
            background: white;
            font-size: 12px;
            color: #4b5563;
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-chip:hover {
            background: #f3f4f6;
        }

        .filter-chip.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        .resource-section {
            margin-bottom: 28px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
        }

        .section-count {
            font-size: 12px;
            color: #6b7280;
        }

        .resource-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .resource-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 10px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }

        .resource-item:hover {
            border-color: #3b82f6;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        .resource-item.required {
            border-left: 4px solid #f59e0b;
        }

        .resource-item.completed {
            opacity: 0.7;
        }

        .resource-icon {
            position: absolute;
            top: 12px;
            right: 12px;
            width: 20px;
            height: 20px;
            background: #d1fae5;
            color: #065f46;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .resource-name {
            font-size: 13px;
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 4px;
            padding-right: 30px;
        }

        .resource-type {
            font-size: 11px;
            color: #6b7280;
        }

        /* 浮动操作按钮 */
        .fab {
            position: fixed;
            bottom: 24px;
            right: 24px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #2563eb, #3b82f6);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
            transition: all 0.3s;
            z-index: 100;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
        }

        /* 能力详情模态框 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            align-items: center;
            justify-content: center;
        }

        .modal.active {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            width: 90%;
            max-width: 700px;
            max-height: 90vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
            padding: 24px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
        }

        .modal-close {
            width: 32px;
            height: 32px;
            border: none;
            background: #f3f4f6;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #6b7280;
            transition: all 0.2s;
        }

        .modal-close:hover {
            background: #e5e7eb;
            color: #374151;
        }

        .modal-body {
            padding: 24px;
            overflow-y: auto;
            flex: 1;
        }

        .competency-detail-section {
            margin-bottom: 24px;
        }

        .detail-label {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
        }

        .detail-content {
            font-size: 14px;
            color: #1f2937;
            line-height: 1.6;
        }

        .learning-resources {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-container {
                grid-template-columns: 1fr;
            }
            
            .sidebar,
            .resource-panel {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- 背景动画 -->
    <div class="background-animation"></div>

    <!-- 全局导航栏 -->
    <nav class="navbar">
        <div class="navbar-brand">Foxu.AI</div>
        <div class="navbar-menu">
            <a href="index.html" class="navbar-item">首页</a>
            <a href="task_delegation.html" class="navbar-item">任务委托中心</a>
            <a href="knowledge_base_explorer.html" class="navbar-item">知识库</a>
            <a href="#" class="navbar-item active">干部能力与学习系统</a>
            <a href="cadre_competency_admin.html" class="navbar-item">干部能力配置</a>
            <a href="team_management.html" class="navbar-item">企业管理</a>
        </div>
        <div class="navbar-actions">
            <div class="user-avatar">张三</div>
        </div>
    </nav>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 左侧边栏 - 能力进度概览 -->
        <aside class="sidebar">
            <h2 class="sidebar-title">
                <span>📊</span>
                能力进度概览
            </h2>
            
            <div class="progress-overview">
                <div class="progress-card" onclick="focusCompetency('german-market')">
                    <div class="progress-card-header">
                        <span class="progress-card-title">德国市场洞察</span>
                        <span class="progress-status status-in-progress">学习中</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 65%;"></div>
                    </div>
                    <div class="progress-info">
                        <span>产品线A</span>
                        <span>13/20 已完成</span>
                    </div>
                </div>

                <div class="progress-card" onclick="focusCompetency('france-market')">
                    <div class="progress-card-header">
                        <span class="progress-card-title">法国市场洞察</span>
                        <span class="progress-status status-certified">已认证</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 100%;"></div>
                    </div>
                    <div class="progress-info">
                        <span>产品线A</span>
                        <span>15/15 已完成</span>
                    </div>
                </div>

                <div class="progress-card" onclick="focusCompetency('competition-analysis')">
                    <div class="progress-card-header">
                        <span class="progress-card-title">竞争分析能力</span>
                        <span class="progress-status status-in-progress">学习中</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 30%;"></div>
                    </div>
                    <div class="progress-info">
                        <span>通用</span>
                        <span>6/20 已完成</span>
                    </div>
                </div>

                <div class="progress-card" onclick="focusCompetency('pricing-strategy')">
                    <div class="progress-card-header">
                        <span class="progress-card-title">产品定价策略</span>
                        <span class="progress-status status-not-started">未开始</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%;"></div>
                    </div>
                    <div class="progress-info">
                        <span>产品线B</span>
                        <span>0/18 已完成</span>
                    </div>
                </div>
            </div>

            <div style="margin-top: 32px; padding-top: 32px; border-top: 1px solid #e5e7eb;">
                <h3 style="font-size: 14px; font-weight: 600; color: #6b7280; margin-bottom: 16px;">学习统计</h3>
                <div style="display: flex; flex-direction: column; gap: 12px;">
                    <div style="display: flex; justify-content: space-between; font-size: 14px;">
                        <span style="color: #6b7280;">总能力数</span>
                        <span style="font-weight: 600; color: #1f2937;">5个</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-size: 14px;">
                        <span style="color: #6b7280;">已认证</span>
                        <span style="font-weight: 600; color: #10b981;">1个</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-size: 14px;">
                        <span style="color: #6b7280;">学习中</span>
                        <span style="font-weight: 600; color: #3b82f6;">2个</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-size: 14px;">
                        <span style="color: #6b7280;">完成率</span>
                        <span style="font-weight: 600; color: #1f2937;">48%</span>
                    </div>
                </div>
            </div>
        </aside>

        <!-- 中央内容区 - 能力地图可视化 -->
        <main class="content">
            <div class="content-header">
                <h1 class="content-title">我的能力地图</h1>
                <p class="content-subtitle">可视化展示您的能力发展路径和当前进度</p>
            </div>

            <div class="view-toggle">
                <button class="view-btn active" onclick="switchView('tree')">树形视图</button>
                <button class="view-btn" onclick="switchView('radial')">辐射视图</button>
                <button class="view-btn" onclick="switchView('list')">列表视图</button>
            </div>

            <div class="competency-map-container">
                <div id="tree-view" class="tree-view">
                    <!-- 市场洞察力域 -->
                    <div class="domain-group">
                        <div class="domain-header" onclick="toggleDomain(this)">
                            <div class="domain-icon">🎯</div>
                            <h3 class="domain-title">市场洞察力</h3>
                            <span class="domain-progress">2/3 能力项</span>
                        </div>
                        <div class="competencies-grid">
                            <div class="competency-card in-progress" onclick="showCompetencyDetail('german-market')">
                                <div class="competency-header">
                                    <span class="competency-title">德国市场洞察</span>
                                    <div class="competency-badge">65%</div>
                                </div>
                                <div class="competency-meta">
                                    <span class="meta-tag">产品线A</span>
                                    <span class="meta-tag">必修</span>
                                </div>
                                <div class="competency-stats">
                                    <span>13/20 资源</span>
                                    <span>预计 5 小时</span>
                                </div>
                            </div>
                            <div class="competency-card certified" onclick="showCompetencyDetail('france-market')">
                                <div class="competency-header">
                                    <span class="competency-title">法国市场洞察</span>
                                    <div class="competency-badge badge-certified">✓</div>
                                </div>
                                <div class="competency-meta">
                                    <span class="meta-tag">产品线A</span>
                                    <span class="meta-tag">已认证</span>
                                </div>
                                <div class="competency-stats">
                                    <span>15/15 资源</span>
                                    <span>2024.03.15 认证</span>
                                </div>
                            </div>
                            <div class="competency-card not-started" onclick="showCompetencyDetail('uk-market')">
                                <div class="competency-header">
                                    <span class="competency-title">英国市场洞察</span>
                                </div>
                                <div class="competency-meta">
                                    <span class="meta-tag">产品线B</span>
                                    <span class="meta-tag">选修</span>
                                </div>
                                <div class="competency-stats">
                                    <span>0/12 资源</span>
                                    <span>预计 4 小时</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 战略规划力域 -->
                    <div class="domain-group">
                        <div class="domain-header" onclick="toggleDomain(this)">
                            <div class="domain-icon">📈</div>
                            <h3 class="domain-title">战略规划力</h3>
                            <span class="domain-progress">0/2 能力项</span>
                        </div>
                        <div class="competencies-grid">
                            <div class="competency-card not-started" onclick="showCompetencyDetail('pricing-strategy')">
                                <div class="competency-header">
                                    <span class="competency-title">产品定价策略</span>
                                </div>
                                <div class="competency-meta">
                                    <span class="meta-tag">产品线B</span>
                                    <span class="meta-tag">必修</span>
                                </div>
                                <div class="competency-stats">
                                    <span>0/18 资源</span>
                                    <span>预计 6 小时</span>
                                </div>
                            </div>
                            <div class="competency-card not-started">
                                <div class="competency-header">
                                    <span class="competency-title">市场进入策略</span>
                                </div>
                                <div class="competency-meta">
                                    <span class="meta-tag">通用</span>
                                    <span class="meta-tag">选修</span>
                                </div>
                                <div class="competency-stats">
                                    <span>0/10 资源</span>
                                    <span>预计 3 小时</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 产品竞争力域 -->
                    <div class="domain-group">
                        <div class="domain-header" onclick="toggleDomain(this)">
                            <div class="domain-icon">💡</div>
                            <h3 class="domain-title">产品竞争力</h3>
                            <span class="domain-progress">1/1 能力项</span>
                        </div>
                        <div class="competencies-grid">
                            <div class="competency-card in-progress" onclick="showCompetencyDetail('competition-analysis')">
                                <div class="competency-header">
                                    <span class="competency-title">竞争分析能力</span>
                                    <div class="competency-badge">30%</div>
                                </div>
                                <div class="competency-meta">
                                    <span class="meta-tag">通用</span>
                                    <span class="meta-tag">必修</span>
                                </div>
                                <div class="competency-stats">
                                    <span>6/20 资源</span>
                                    <span>预计 8 小时</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 右侧边栏 - 学习资源 -->
        <aside class="resource-panel">
            <div class="resource-header">
                <h2 class="resource-title" id="selected-competency-title">请选择一个能力项</h2>
            </div>

            <div id="competency-detail-container" style="display: none;">
                <!-- 能力详情 -->
                <div class="competency-details">
                    <div class="detail-section">
                        <div class="detail-label">能力描述</div>
                        <div class="detail-content" id="competency-description">
                            请从左侧选择一个能力项查看详情
                        </div>
                    </div>

                    <div class="detail-section">
                        <div class="detail-label">关联产品线</div>
                        <div class="detail-content">
                            <div class="competency-meta" id="competency-product-lines">
                                <!-- 动态加载产品线标签 -->
                            </div>
                        </div>
                    </div>

                    <div class="detail-section">
                        <div class="detail-label">关联知识库</div>
                        <div class="detail-content">
                            <div class="kb-links" id="competency-knowledge-bases">
                                <!-- 动态加载知识库链接 -->
                            </div>
                        </div>
                    </div>

                    <div class="detail-section">
                        <div class="detail-label">学习进度</div>
                        <div class="detail-content">
                            <div class="progress-detail">
                                <div class="progress-bar" style="height: 8px;">
                                    <div class="progress-fill" id="competency-progress-bar" style="width: 0%;"></div>
                                </div>
                                <div class="progress-stats">
                                    <span id="progress-completed">已完成 0/0 个学习资源</span>
                                    <span id="progress-time">预计剩余 0 小时</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 学习资源筛选 -->
                <div style="margin-bottom: 16px;">
                    <div class="resource-filter">
                        <div class="filter-chip active" onclick="filterResources('all')">全部</div>
                        <div class="filter-chip" onclick="filterResources('required')">必读</div>
                        <div class="filter-chip" onclick="filterResources('unread')">未读</div>
                    </div>
                </div>

                <!-- 学习资源列表 -->
                <div id="resource-container">
                    <div class="resource-section">
                        <div class="section-header">
                            <h3 class="section-title" id="resource-section-title">学习资源</h3>
                            <span class="section-count" id="resource-count">0个</span>
                        </div>
                        <div class="resource-list" id="resource-list">
                            <!-- 动态加载资源列表 -->
                        </div>
                    </div>
                </div>

                <!-- 知识库集成入口 -->
                <div style="margin-top: 24px; padding: 16px; background: #eff6ff; border-radius: 12px; text-align: center;">
                    <p style="font-size: 14px; color: #1e40af; margin-bottom: 12px;">探索更多学习资源</p>
                    <button class="btn btn-primary" onclick="openKnowledgeBase()">进入知识库浏览器</button>
                </div>
            </div>

            <!-- 默认提示 -->
            <div id="no-selection-hint" style="text-align: center; padding: 60px 20px; color: #6b7280;">
                <div style="font-size: 48px; margin-bottom: 16px;">📚</div>
                <p style="font-size: 16px; margin-bottom: 8px;">请选择一个能力项</p>
                <p style="font-size: 14px;">点击左侧能力卡片查看详细信息和学习资源</p>
            </div>
        </aside>
    </div>

    <!-- 浮动操作按钮 -->
    <div class="fab" onclick="showLearningPath()">
        <span>🗺️</span>
    </div>

    <script>
        // 切换视图
        function switchView(viewType) {
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 这里可以实现不同视图的切换逻辑
            console.log('切换到视图:', viewType);
        }

        // 切换域的展开/收起
        function toggleDomain(domainHeader) {
            const competenciesGrid = domainHeader.nextElementSibling;
            if (competenciesGrid.style.display === 'none') {
                competenciesGrid.style.display = 'grid';
            } else {
                competenciesGrid.style.display = 'none';
            }
        }

        // 聚焦到特定能力
        function focusCompetency(competencyId) {
            // 滚动到对应的能力卡片
            const card = document.querySelector(`[onclick*="${competencyId}"]`);
            if (card) {
                card.scrollIntoView({ behavior: 'smooth', block: 'center' });
                // 添加高亮效果
                card.style.boxShadow = '0 0 0 3px rgba(37, 99, 235, 0.3)';
                setTimeout(() => {
                    card.style.boxShadow = '';
                }, 2000);
            }
        }

        // 能力数据模拟
        const competencyData = {
            'german-market': {
                title: '德国市场洞察',
                description: '深入了解德国市场的特点、消费者行为、竞争格局和监管环境。掌握在德国市场成功运营所需的关键洞察和策略制定能力。',
                productLines: ['新能源汽车', '产品线A'],
                knowledgeBases: [
                    { id: 'market-germany', title: '德国市场数据库', icon: '🌍' },
                    { id: 'policy-eu', title: '欧盟法规库', icon: '📋' }
                ],
                progress: 65,
                completedResources: 13,
                totalResources: 20,
                estimatedHours: 5,
                resources: [
                    { id: 'aip-1', name: '德国汽车市场深度分析报告', type: 'AIP空间 • 决策简报', required: true, completed: false },
                    { id: 'alert-1', name: '德国新能源政策变化预警', type: '情报预警', required: true, completed: true },
                    { id: 'doc-1', name: '德国市场进入策略指南.pdf', type: '上传文档', required: true, completed: false },
                    { id: 'aip-2', name: 'Bosch 竞品分析报告', type: 'AIP空间', required: false, completed: false },
                    { id: 'link-1', name: '欧洲市场营销策略课程', type: '外部链接', required: false, completed: false }
                ]
            },
            'france-market': {
                title: '法国市场洞察',
                description: '了解法国市场的独特特征、消费者偏好、行业生态和商业文化。建立在法国市场成功运营的战略思维和执行能力。',
                productLines: ['产品线A'],
                knowledgeBases: [
                    { id: 'market-france', title: '法国市场数据库', icon: '🌍' },
                    { id: 'competitor-valeo', title: 'Valeo 竞品分析', icon: '🎯' }
                ],
                progress: 100,
                completedResources: 15,
                totalResources: 15,
                estimatedHours: 0,
                resources: [
                    { id: 'fr-1', name: '法国汽车产业分析', type: 'AIP空间', required: true, completed: true },
                    { id: 'fr-2', name: '法国消费者行为研究', type: '上传文档', required: true, completed: true }
                ]
            },
            'competition-analysis': {
                title: '竞争分析能力',
                description: '掌握系统的竞争分析方法论，能够深入分析竞争对手的战略、产品、技术和市场表现，为企业战略决策提供支撑。',
                productLines: ['通用'],
                knowledgeBases: [
                    { id: 'competitor-bosch', title: 'Bosch 竞品分析', icon: '🎯' },
                    { id: 'competitor-continental', title: 'Continental 竞品分析', icon: '🎯' }
                ],
                progress: 30,
                completedResources: 6,
                totalResources: 20,
                estimatedHours: 8,
                resources: [
                    { id: 'comp-1', name: '竞争分析方法论', type: '上传文档', required: true, completed: true },
                    { id: 'comp-2', name: '主要竞争对手画像', type: 'AIP空间', required: true, completed: false }
                ]
            },
            'pricing-strategy': {
                title: '产品定价策略',
                description: '学习和掌握产品定价的核心理论和实践方法，包括成本分析、价值定价、竞争定价等策略的制定和执行。',
                productLines: ['产品线B'],
                knowledgeBases: [],
                progress: 0,
                completedResources: 0,
                totalResources: 18,
                estimatedHours: 6,
                resources: [
                    { id: 'price-1', name: '定价策略基础理论', type: '上传文档', required: true, completed: false },
                    { id: 'price-2', name: '行业定价案例分析', type: 'AIP空间', required: false, completed: false }
                ]
            }
        };

        let currentCompetencyId = null;
        let currentFilter = 'all';

        // 显示能力详情
        function showCompetencyDetail(competencyId) {
            currentCompetencyId = competencyId;
            const data = competencyData[competencyId];
            
            if (!data) return;

            // 显示详情容器，隐藏提示
            document.getElementById('competency-detail-container').style.display = 'block';
            document.getElementById('no-selection-hint').style.display = 'none';

            // 更新标题
            document.getElementById('selected-competency-title').textContent = data.title;

            // 更新能力描述
            document.getElementById('competency-description').textContent = data.description;

            // 更新产品线
            const productLinesContainer = document.getElementById('competency-product-lines');
            productLinesContainer.innerHTML = data.productLines.map(line => 
                `<span class="meta-tag">${line}</span>`
            ).join('');

            // 更新知识库
            const kbContainer = document.getElementById('competency-knowledge-bases');
            if (data.knowledgeBases.length > 0) {
                kbContainer.innerHTML = data.knowledgeBases.map(kb => 
                    `<div class="kb-link" onclick="openKnowledgeBase('${kb.id}')">
                        <span>${kb.icon}</span>
                        <span>${kb.title}</span>
                    </div>`
                ).join('');
            } else {
                kbContainer.innerHTML = '<span style="color: #6b7280; font-size: 14px;">暂无关联知识库</span>';
            }

            // 更新进度
            document.getElementById('competency-progress-bar').style.width = data.progress + '%';
            document.getElementById('progress-completed').textContent = 
                `已完成 ${data.completedResources}/${data.totalResources} 个学习资源`;
            document.getElementById('progress-time').textContent = 
                data.estimatedHours > 0 ? `预计剩余 ${data.estimatedHours} 小时` : '已完成';

            // 更新资源列表
            updateResourceList(data.resources);

            // 高亮选中的能力卡片
            document.querySelectorAll('.competency-card').forEach(card => {
                card.style.borderColor = '';
                card.style.boxShadow = '';
            });
            const selectedCard = document.querySelector(`[onclick*="${competencyId}"]`);
            if (selectedCard) {
                selectedCard.style.borderColor = '#2563eb';
                selectedCard.style.boxShadow = '0 0 0 3px rgba(37, 99, 235, 0.1)';
            }
        }

        // 更新资源列表
        function updateResourceList(resources) {
            const filteredResources = filterResourcesByType(resources, currentFilter);
            const resourceList = document.getElementById('resource-list');
            
            resourceList.innerHTML = filteredResources.map(resource => {
                const completedClass = resource.completed ? 'completed' : '';
                const requiredClass = resource.required ? 'required' : '';
                const icon = resource.completed ? '<div class="resource-icon">✓</div>' : '';
                
                return `
                    <div class="resource-item ${requiredClass} ${completedClass}" onclick="openResource('${resource.id}')">
                        ${icon}
                        <div class="resource-name">${resource.name}</div>
                        <div class="resource-type">${resource.type}</div>
                    </div>
                `;
            }).join('');

            // 更新计数
            document.getElementById('resource-count').textContent = filteredResources.length + '个';
            
            // 更新标题
            const titleMap = {
                'all': '全部资源',
                'required': '必读资源',
                'unread': '未读资源'
            };
            document.getElementById('resource-section-title').textContent = titleMap[currentFilter];
        }

        // 筛选资源
        function filterResourcesByType(resources, filterType) {
            switch (filterType) {
                case 'required':
                    return resources.filter(r => r.required);
                case 'unread':
                    return resources.filter(r => !r.completed);
                default:
                    return resources;
            }
        }

        // 筛选资源
        function filterResources(filterType) {
            currentFilter = filterType;
            
            document.querySelectorAll('.filter-chip').forEach(chip => {
                chip.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 更新资源列表
            if (currentCompetencyId && competencyData[currentCompetencyId]) {
                updateResourceList(competencyData[currentCompetencyId].resources);
            }
        }

        // 打开资源
        function openResource(resourceId) {
            // 记录学习进度
            event.currentTarget.classList.add('completed');
            const icon = document.createElement('div');
            icon.className = 'resource-icon';
            icon.textContent = '✓';
            if (!event.currentTarget.querySelector('.resource-icon')) {
                event.currentTarget.appendChild(icon);
            }
            
            // 打开资源（这里可以集成知识库浏览器）
            console.log('打开资源:', resourceId);
        }

        // 打开知识库浏览器
        function openKnowledgeBase(kbId) {
            // 如果提供了特定的知识库ID，可以传递参数
            if (kbId) {
                window.location.href = `knowledge_base_explorer.html?kb=${kbId}`;
            } else {
                window.location.href = 'knowledge_base_explorer.html';
            }
        }

        // 显示学习路径
        function showLearningPath() {
            alert('学习路径功能开发中...');
        }

        // 加载用户数据
        function loadUserData() {
            // 从 localStorage 加载用户的学习进度数据
            const userData = localStorage.getItem('userCompetencyData');
            if (userData) {
                // 恢复用户的学习进度
                console.log('加载用户数据');
            }
        }

        // 保存用户进度
        function saveUserProgress() {
            // 保存当前的学习进度到 localStorage
            const progressData = {
                competencies: [],
                completedResources: [],
                lastAccessed: new Date().toISOString()
            };
            
            localStorage.setItem('userCompetencyData', JSON.stringify(progressData));
        }

        // 初始化
        window.addEventListener('load', function() {
            loadUserData();
            
            // 定期保存进度
            setInterval(saveUserProgress, 30000); // 每30秒保存一次
        });

        // 页面卸载时保存进度
        window.addEventListener('beforeunload', saveUserProgress);
    </script>
</body>
</html>
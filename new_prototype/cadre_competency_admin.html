<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Foxu.AI - 干部能力管理系统</title>
    <style>
        /* 全局重置和基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f9fafb;
            color: #1f2937;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 背景动画效果 */
        .background-animation {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
            opacity: 0.03;
            overflow: hidden;
        }

        .background-animation::before,
        .background-animation::after {
            content: "";
            position: absolute;
            width: 600px;
            height: 600px;
            border-radius: 50%;
            animation: atmosphericFloat 20s ease-in-out infinite;
        }

        .background-animation::before {
            background: radial-gradient(circle, #667eea 0%, transparent 70%);
            top: -300px;
            left: -300px;
        }

        .background-animation::after {
            background: radial-gradient(circle, #764ba2 0%, transparent 70%);
            bottom: -300px;
            right: -300px;
            animation-delay: -10s;
        }

        @keyframes atmosphericFloat {
            0%, 100% { transform: translate(0, 0) scale(1); }
            33% { transform: translate(30px, -30px) scale(1.1); }
            66% { transform: translate(-20px, 20px) scale(0.9); }
        }

        /* 全局导航栏 */
        .navbar {
            position: sticky;
            top: 0;
            height: 60px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(12px);
            border-bottom: 1px solid #e5e7eb;
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: 700;
            background: linear-gradient(135deg, #2563eb, #9333ea);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-right: 48px;
        }

        .navbar-menu {
            display: flex;
            gap: 32px;
            flex: 1;
        }

        .navbar-item {
            color: #374151;
            text-decoration: none;
            font-size: 15px;
            font-weight: 500;
            transition: color 0.2s;
            position: relative;
        }

        .navbar-item:hover {
            color: #2563eb;
        }

        .navbar-item.active {
            color: #2563eb;
        }

        .navbar-item.active::after {
            content: '';
            position: absolute;
            bottom: -18px;
            left: 0;
            right: 0;
            height: 3px;
            background: #2563eb;
            border-radius: 3px 3px 0 0;
        }

        .navbar-actions {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s;
        }

        /* 页面头部 */
        /* .page-header {
            background: white;
            padding: 16px 24px;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .page-description {
            font-size: 14px;
            color: #6b7280;
        } */

        /* 标签页 */
        .tabs {
            background: white;
            padding: 0 24px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            gap: 32px;
        }

        .tab {
            padding: 16px 0;
            font-size: 14px;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.2s;
        }

        .tab:hover {
            color: #374151;
        }

        .tab.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
        }

        /* 主内容区域 */
        .main-content {
            padding: 24px;
            height: calc(100vh - 60px - 90px );
            overflow-y: auto;
        }

        /* 能力地图编辑器 */
        .competency-editor {
            display: grid;
            grid-template-columns: 250px 1fr 300px;
            gap: 24px;
            height: 100%;
        }

        .editor-toolbar {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .toolbar-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 16px;
        }

        .tool-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            margin-bottom: 8px;
            background: #f3f4f6;
            border-radius: 8px;
            cursor: move;
            transition: all 0.2s;
        }

        .tool-item:hover {
            background: #e5e7eb;
            transform: translateX(4px);
        }

        .tool-icon {
            width: 32px;
            height: 32px;
            background: white;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .editor-canvas {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: auto;
        }

        .canvas-container {
            min-height: 600px;
            position: relative;
        }

        .competency-node {
            position: absolute;
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 16px 20px;
            min-width: 200px;
            cursor: move;
            transition: all 0.2s;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .competency-node:hover {
            border-color: #2563eb;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
        }

        .competency-node.domain {
            background: linear-gradient(135deg, #eff6ff, #e0e7ff);
            border-color: #2563eb;
        }

        .node-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .node-tags {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .node-tag {
            font-size: 11px;
            padding: 2px 8px;
            background: #e5e7eb;
            border-radius: 10px;
            color: #4b5563;
        }

        .editor-properties {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .property-section {
            margin-bottom: 24px;
        }

        .property-label {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .property-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s;
        }

        .property-input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .property-textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            min-height: 80px;
            resize: vertical;
            transition: all 0.2s;
        }

        .property-textarea:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        /* 产品线选择器 */
        .product-lines {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 8px;
        }

        .product-line-chip {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 12px;
            background: #dbeafe;
            border: 1px solid #93c5fd;
            border-radius: 16px;
            font-size: 12px;
            color: #1e40af;
            cursor: pointer;
            transition: all 0.2s;
        }

        .product-line-chip:hover {
            background: #bfdbfe;
        }

        .product-line-chip.selected {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        /* 学习资料库管理 */
        .resource-management {
            display: flex;
            flex-direction: column;
            gap: 24px;
            height: 100%;
        }

        .resource-filters {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            display: flex;
            gap: 16px;
            align-items: flex-end;
        }

        .filter-group {
            flex: 1;
        }

        .filter-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
        }

        .filter-select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }

        .filter-select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
        }

        .resource-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            flex: 1;
        }

        .table-header {
            display: grid;
            grid-template-columns: 2fr 1fr 1.5fr 1.5fr 120px;
            padding: 16px 20px;
            background: #f9fafb;
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #e5e7eb;
        }

        .table-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1.5fr 1.5fr 120px;
            padding: 16px 20px;
            border-bottom: 1px solid #f3f4f6;
            font-size: 14px;
            align-items: center;
            transition: background 0.2s;
        }

        .table-row:hover {
            background: #f9fafb;
        }

        .resource-title {
            font-weight: 500;
            color: #1f2937;
        }

        .resource-type {
            display: inline-block;
            padding: 4px 10px;
            background: #f3f4f6;
            border-radius: 12px;
            font-size: 12px;
            color: #4b5563;
        }

        .resource-type.aip {
            background: #fef3c7;
            color: #92400e;
        }

        .resource-type.alert {
            background: #fee2e2;
            color: #991b1b;
        }

        .resource-tags {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .resource-tag {
            padding: 2px 8px;
            background: #e0e7ff;
            border-radius: 10px;
            font-size: 11px;
            color: #3730a3;
        }

        .resource-actions {
            display: flex;
            gap: 8px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            background: white;
            color: #374151;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-small:hover {
            background: #f3f4f6;
            border-color: #d1d5db;
        }

        /* 能力指派矩阵 */
        .assignment-matrix {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            overflow: auto;
        }

        .matrix-filters {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
        }

        .matrix-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .matrix-table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border: 1px solid #e5e7eb;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .matrix-table td {
            padding: 8px;
            border: 1px solid #e5e7eb;
            text-align: center;
        }

        .matrix-table tr:hover {
            background: #f9fafb;
        }

        .matrix-checkbox {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .user-name {
            font-weight: 500;
            color: #1f2937;
            text-align: left;
            padding-left: 16px;
        }

        /* 模态框 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            align-items: center;
            justify-content: center;
        }

        .modal.active {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
            padding: 24px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
        }

        .modal-close {
            width: 32px;
            height: 32px;
            border: none;
            background: #f3f4f6;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #6b7280;
            transition: all 0.2s;
        }

        .modal-close:hover {
            background: #e5e7eb;
            color: #374151;
        }

        .modal-body {
            padding: 24px;
            overflow-y: auto;
            flex: 1;
        }

        .modal-footer {
            padding: 20px 24px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #e5e7eb;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        /* 知识库选择器样式 */
        .selected-items {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .selected-item {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 10px;
            background: #e0e7ff;
            border: 1px solid #c7d2fe;
            border-radius: 16px;
            font-size: 12px;
            color: #4338ca;
        }

        .selected-item .remove-btn {
            cursor: pointer;
            color: #6366f1;
            font-weight: bold;
            margin-left: 4px;
            transition: color 0.2s;
        }

        .selected-item .remove-btn:hover {
            color: #4338ca;
        }

        .knowledge-base-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            align-items: center;
            justify-content: center;
        }

        .knowledge-base-modal.active {
            display: flex;
        }

        .kb-modal-content {
            background: white;
            border-radius: 16px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .kb-list {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
        }

        .kb-category {
            margin-bottom: 24px;
        }

        .kb-category-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .kb-items {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 12px;
        }

        .kb-item {
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
        }

        .kb-item:hover {
            border-color: #3b82f6;
            background: #f0f9ff;
        }

        .kb-item.selected {
            border-color: #2563eb;
            background: #eff6ff;
        }

        .kb-item-title {
            font-size: 14px;
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .kb-item-desc {
            font-size: 12px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .competency-editor {
                grid-template-columns: 1fr;
            }
            
            .editor-toolbar,
            .editor-properties {
                display: none;
            }
        }
    </style>
    <!-- 引入Sortable.js用于拖拽 -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
</head>
<body>
    <!-- 背景动画 -->
    <div class="background-animation"></div>

    <!-- 全局导航栏 -->
    <nav class="navbar">
        <div class="navbar-brand">Foxu.AI</div>
        <div class="navbar-menu">
            <a href="index.html" class="navbar-item">首页</a>
            <a href="task_delegation.html" class="navbar-item">任务委托中心</a>
            <a href="knowledge_base_explorer.html" class="navbar-item">知识库</a>
            <a href="cadre_competency_learning.html" class="navbar-item">干部能力与学习系统</a>
            <a href="#" class="navbar-item active">干部能力配置</a>
            <a href="team_management.html" class="navbar-item">企业管理</a>
        </div>
        <div class="navbar-actions">
            <div class="user-avatar">管理</div>
        </div>
    </nav>

    <!-- 页面头部 -->
    <!-- <div class="page-header">
        <div class="page-title">干部能力管理系统</div>
        <div class="page-description">定义企业能力模型，管理学习资源，指派能力发展任务</div>
    </div> -->

    <!-- 标签页 -->
    <div class="tabs">
        <div class="tab active" onclick="switchTab('competency-map')">能力地图编辑</div>
        <div class="tab" onclick="switchTab('resource-library')">学习资料库管理</div>
        <div class="tab" onclick="switchTab('assignment-matrix')">能力指派矩阵</div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 能力地图编辑器 -->
        <div id="competency-map" class="tab-content competency-editor">
            <!-- 左侧工具栏 -->
            <div class="editor-toolbar">
                <h3 class="toolbar-title">拖拽添加节点</h3>
                <div id="tool-palette">
                    <div class="tool-item" draggable="true" data-type="domain">
                        <div class="tool-icon">🎯</div>
                        <div>
                            <div style="font-weight: 500;">能力域</div>
                            <div style="font-size: 12px; color: #6b7280;">顶层能力分类</div>
                        </div>
                    </div>
                    <div class="tool-item" draggable="true" data-type="competency">
                        <div class="tool-icon">💡</div>
                        <div>
                            <div style="font-weight: 500;">能力项</div>
                            <div style="font-size: 12px; color: #6b7280;">具体能力要求</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中央画布 -->
            <div class="editor-canvas">
                <div id="canvas-container" class="canvas-container">
                    <!-- 示例节点 -->
                    <div class="competency-node domain" style="left: 50px; top: 50px;" data-id="domain-1">
                        <div class="node-title">市场洞察力</div>
                        <div class="node-tags">
                            <span class="node-tag">3个能力项</span>
                        </div>
                    </div>
                    <div class="competency-node" style="left: 300px; top: 80px;" data-id="comp-1">
                        <div class="node-title">德国市场洞察</div>
                        <div class="node-tags">
                            <span class="node-tag">产品线A</span>
                            <span class="node-tag">产品线B</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧属性面板 -->
            <div class="editor-properties">
                <h3 class="toolbar-title">节点属性</h3>
                <div id="property-panel">
                    <div class="property-section">
                        <label class="property-label">名称</label>
                        <input type="text" class="property-input" id="node-name" placeholder="输入节点名称">
                    </div>
                    <div class="property-section">
                        <label class="property-label">描述</label>
                        <textarea class="property-textarea" id="node-description" placeholder="输入节点描述"></textarea>
                    </div>
                    <div class="property-section">
                        <label class="property-label">关联产品线</label>
                        <div class="product-lines">
                            <div class="product-line-chip" data-line="新能源汽车">新能源汽车</div>
                            <div class="product-line-chip" data-line="智能制造">智能制造</div>
                            <div class="product-line-chip" data-line="医疗器械">医疗器械</div>
                            <div class="product-line-chip" data-line="通用">通用</div>
                        </div>
                    </div>
                    <div class="property-section">
                        <label class="property-label">关联知识库</label>
                        <div class="knowledge-base-selector">
                            <button class="btn btn-secondary" style="width: 100%; margin-bottom: 8px;" onclick="showKnowledgeBaseSelector()">选择知识库</button>
                            <div id="selected-knowledge-bases" class="selected-items"></div>
                        </div>
                    </div>
                    <div class="property-section">
                        <button class="btn btn-primary" style="width: 100%;">保存更改</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学习资料库管理 -->
        <div id="resource-library" class="tab-content resource-management" style="display: none;">
            <div class="resource-filters">
                <div class="filter-group">
                    <label class="filter-label">资源类型</label>
                    <select class="filter-select">
                        <option>全部类型</option>
                        <option>AIP空间</option>
                        <option>情报预警</option>
                        <option>上传文档</option>
                        <option>外部链接</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">能力标签</label>
                    <select class="filter-select">
                        <option>全部能力</option>
                        <option>市场洞察力</option>
                        <option>战略规划力</option>
                        <option>产品竞争力</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">产品线</label>
                    <select class="filter-select">
                        <option>全部产品线</option>
                        <option>新能源汽车</option>
                        <option>智能制造</option>
                        <option>医疗器械</option>
                    </select>
                </div>
                <button class="btn btn-primary" onclick="showUploadModal()">上传资源</button>
            </div>

            <div class="resource-table">
                <div class="table-header">
                    <div>资源名称</div>
                    <div>类型</div>
                    <div>能力标签</div>
                    <div>产品线</div>
                    <div>操作</div>
                </div>
                <div class="table-row">
                    <div class="resource-title">2024年德国市场分析报告</div>
                    <div><span class="resource-type aip">AIP空间</span></div>
                    <div class="resource-tags">
                        <span class="resource-tag">德国市场洞察</span>
                        <span class="resource-tag">竞争分析</span>
                    </div>
                    <div class="resource-tags">
                        <span class="resource-tag">产品线A</span>
                    </div>
                    <div class="resource-actions">
                        <button class="btn-small">编辑标签</button>
                        <button class="btn-small">必读标记</button>
                    </div>
                </div>
                <div class="table-row">
                    <div class="resource-title">新能源汽车政策变化预警</div>
                    <div><span class="resource-type alert">情报预警</span></div>
                    <div class="resource-tags">
                        <span class="resource-tag">政策洞察</span>
                    </div>
                    <div class="resource-tags">
                        <span class="resource-tag">新能源汽车</span>
                    </div>
                    <div class="resource-actions">
                        <button class="btn-small">编辑标签</button>
                        <button class="btn-small">必读标记</button>
                    </div>
                </div>
                <div class="table-row">
                    <div class="resource-title">营销4P理论基础.pdf</div>
                    <div><span class="resource-type">上传文档</span></div>
                    <div class="resource-tags">
                        <span class="resource-tag">战略规划力</span>
                    </div>
                    <div class="resource-tags">
                        <span class="resource-tag">通用</span>
                    </div>
                    <div class="resource-actions">
                        <button class="btn-small">编辑标签</button>
                        <button class="btn-small">必读标记</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 能力指派矩阵 -->
        <div id="assignment-matrix" class="tab-content" style="display: none;">
            <div class="assignment-matrix">
                <div class="matrix-filters">
                    <div class="filter-group" style="flex: 1;">
                        <label class="filter-label">按产品线筛选</label>
                        <select class="filter-select">
                            <option>全部产品线</option>
                            <option>新能源汽车</option>
                            <option>智能制造</option>
                            <option>医疗器械</option>
                        </select>
                    </div>
                    <div class="filter-group" style="flex: 1;">
                        <label class="filter-label">按部门筛选</label>
                        <select class="filter-select">
                            <option>全部部门</option>
                            <option>市场部</option>
                            <option>产品部</option>
                            <option>战略部</option>
                        </select>
                    </div>
                    <button class="btn btn-primary">批量指派</button>
                </div>

                <table class="matrix-table">
                    <thead>
                        <tr>
                            <th style="width: 200px;">干部姓名</th>
                            <th>德国市场洞察</th>
                            <th>法国市场洞察</th>
                            <th>产品定价策略</th>
                            <th>竞争分析能力</th>
                            <th>战略规划能力</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="user-name">张三（欧洲区总监）</td>
                            <td><input type="checkbox" class="matrix-checkbox" checked></td>
                            <td><input type="checkbox" class="matrix-checkbox" checked></td>
                            <td><input type="checkbox" class="matrix-checkbox"></td>
                            <td><input type="checkbox" class="matrix-checkbox" checked></td>
                            <td><input type="checkbox" class="matrix-checkbox"></td>
                        </tr>
                        <tr>
                            <td class="user-name">李四（产品经理）</td>
                            <td><input type="checkbox" class="matrix-checkbox"></td>
                            <td><input type="checkbox" class="matrix-checkbox"></td>
                            <td><input type="checkbox" class="matrix-checkbox" checked></td>
                            <td><input type="checkbox" class="matrix-checkbox" checked></td>
                            <td><input type="checkbox" class="matrix-checkbox"></td>
                        </tr>
                        <tr>
                            <td class="user-name">王五（市场分析师）</td>
                            <td><input type="checkbox" class="matrix-checkbox" checked></td>
                            <td><input type="checkbox" class="matrix-checkbox"></td>
                            <td><input type="checkbox" class="matrix-checkbox"></td>
                            <td><input type="checkbox" class="matrix-checkbox" checked></td>
                            <td><input type="checkbox" class="matrix-checkbox"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 上传资源模态框 -->
    <div id="uploadModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">上传学习资源</h2>
                <button class="modal-close" onclick="closeUploadModal()">×</button>
            </div>
            <div class="modal-body">
                <div class="property-section">
                    <label class="property-label">资源类型</label>
                    <select class="filter-select">
                        <option>上传文档</option>
                        <option>外部链接</option>
                    </select>
                </div>
                <div class="property-section">
                    <label class="property-label">选择文件</label>
                    <input type="file" class="property-input" accept=".pdf,.docx,.pptx">
                </div>
                <div class="property-section">
                    <label class="property-label">资源标题</label>
                    <input type="text" class="property-input" placeholder="输入资源标题">
                </div>
                <div class="property-section">
                    <label class="property-label">能力标签（必选）</label>
                    <select class="filter-select" multiple style="height: 120px;">
                        <option>市场洞察力 - 德国市场洞察</option>
                        <option>市场洞察力 - 法国市场洞察</option>
                        <option>战略规划力 - 产品定价策略</option>
                        <option>产品竞争力 - 竞争分析能力</option>
                    </select>
                </div>
                <div class="property-section">
                    <label class="property-label">产品线（必选）</label>
                    <div class="product-lines">
                        <div class="product-line-chip" data-line="新能源汽车">新能源汽车</div>
                        <div class="product-line-chip" data-line="智能制造">智能制造</div>
                        <div class="product-line-chip" data-line="医疗器械">医疗器械</div>
                        <div class="product-line-chip" data-line="通用">通用</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeUploadModal()">取消</button>
                <button class="btn btn-primary">确认上传</button>
            </div>
        </div>
    </div>

    <!-- 知识库选择模态框 -->
    <div id="knowledgeBaseModal" class="knowledge-base-modal">
        <div class="kb-modal-content">
            <div class="modal-header">
                <h2 class="modal-title">选择关联知识库</h2>
                <button class="modal-close" onclick="closeKnowledgeBaseModal()">×</button>
            </div>
            <div class="kb-list">
                <!-- 目标市场数据库 -->
                <div class="kb-category">
                    <h3 class="kb-category-title">
                        <span>🌍</span>
                        目标市场数据库
                    </h3>
                    <div class="kb-items">
                        <div class="kb-item" data-kb-id="market-germany" onclick="toggleKBSelection(this)">
                            <div class="kb-item-title">德国市场</div>
                            <div class="kb-item-desc">德国汽车市场分析、政策法规、竞争格局</div>
                        </div>
                        <div class="kb-item" data-kb-id="market-france" onclick="toggleKBSelection(this)">
                            <div class="kb-item-title">法国市场</div>
                            <div class="kb-item-desc">法国市场趋势、消费者行为、产业链分析</div>
                        </div>
                        <div class="kb-item" data-kb-id="market-uk" onclick="toggleKBSelection(this)">
                            <div class="kb-item-title">英国市场</div>
                            <div class="kb-item-desc">英国市场准入、技术标准、合作伙伴</div>
                        </div>
                        <div class="kb-item" data-kb-id="market-italy" onclick="toggleKBSelection(this)">
                            <div class="kb-item-title">意大利市场</div>
                            <div class="kb-item-desc">意大利制造业特点、供应链、市场机会</div>
                        </div>
                    </div>
                </div>

                <!-- 竞品策略数据库 -->
                <div class="kb-category">
                    <h3 class="kb-category-title">
                        <span>🎯</span>
                        竞品策略数据库
                    </h3>
                    <div class="kb-items">
                        <div class="kb-item" data-kb-id="competitor-bosch" onclick="toggleKBSelection(this)">
                            <div class="kb-item-title">Bosch 竞品分析</div>
                            <div class="kb-item-desc">博世产品线、技术路线、市场策略</div>
                        </div>
                        <div class="kb-item" data-kb-id="competitor-continental" onclick="toggleKBSelection(this)">
                            <div class="kb-item-title">Continental 竞品分析</div>
                            <div class="kb-item-desc">大陆集团创新动态、专利布局、合作网络</div>
                        </div>
                        <div class="kb-item" data-kb-id="competitor-valeo" onclick="toggleKBSelection(this)">
                            <div class="kb-item-title">Valeo 竞品分析</div>
                            <div class="kb-item-desc">法雷奥智能化转型、新能源布局、全球战略</div>
                        </div>
                    </div>
                </div>

                <!-- 政策法规库 -->
                <div class="kb-category">
                    <h3 class="kb-category-title">
                        <span>📋</span>
                        政策法规库
                    </h3>
                    <div class="kb-items">
                        <div class="kb-item" data-kb-id="policy-eu" onclick="toggleKBSelection(this)">
                            <div class="kb-item-title">欧盟法规</div>
                            <div class="kb-item-desc">GDPR、碳排放标准、产品认证要求</div>
                        </div>
                        <div class="kb-item" data-kb-id="policy-energy" onclick="toggleKBSelection(this)">
                            <div class="kb-item-title">新能源政策</div>
                            <div class="kb-item-desc">各国补贴政策、充电基础设施、技术路线</div>
                        </div>
                        <div class="kb-item" data-kb-id="policy-safety" onclick="toggleKBSelection(this)">
                            <div class="kb-item-title">安全标准</div>
                            <div class="kb-item-desc">Euro NCAP、功能安全、网络安全要求</div>
                        </div>
                    </div>
                </div>

                <!-- 技术趋势库 -->
                <div class="kb-category">
                    <h3 class="kb-category-title">
                        <span>💡</span>
                        技术趋势库
                    </h3>
                    <div class="kb-items">
                        <div class="kb-item" data-kb-id="tech-autonomous" onclick="toggleKBSelection(this)">
                            <div class="kb-item-title">自动驾驶技术</div>
                            <div class="kb-item-desc">L3/L4技术进展、传感器融合、AI算法</div>
                        </div>
                        <div class="kb-item" data-kb-id="tech-battery" onclick="toggleKBSelection(this)">
                            <div class="kb-item-title">电池技术</div>
                            <div class="kb-item-desc">固态电池、快充技术、电池管理系统</div>
                        </div>
                        <div class="kb-item" data-kb-id="tech-connectivity" onclick="toggleKBSelection(this)">
                            <div class="kb-item-title">车联网技术</div>
                            <div class="kb-item-desc">5G-V2X、OTA更新、信息安全架构</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeKnowledgeBaseModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmKBSelection()">确认选择</button>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换
        function switchTab(tabId) {
            // 更新标签状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 切换内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.display = 'none';
            });
            document.getElementById(tabId).style.display = tabId === 'competency-map' ? 'grid' : 'block';
        }

        // 产品线选择
        document.querySelectorAll('.product-line-chip').forEach(chip => {
            chip.addEventListener('click', function() {
                this.classList.toggle('selected');
            });
        });

        // 节点拖拽
        let draggedElement = null;
        let currentNode = null;

        // 工具箱拖拽
        document.querySelectorAll('.tool-item').forEach(tool => {
            tool.addEventListener('dragstart', function(e) {
                draggedElement = {
                    type: this.dataset.type,
                    isNew: true
                };
                e.dataTransfer.effectAllowed = 'copy';
            });
        });

        // 画布接收拖拽
        const canvas = document.getElementById('canvas-container');
        canvas.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'copy';
        });

        canvas.addEventListener('drop', function(e) {
            e.preventDefault();
            if (draggedElement && draggedElement.isNew) {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                createNode(draggedElement.type, x, y);
            }
        });

        // 创建新节点
        function createNode(type, x, y) {
            const node = document.createElement('div');
            node.className = `competency-node ${type === 'domain' ? 'domain' : ''}`;
            node.style.left = x + 'px';
            node.style.top = y + 'px';
            node.dataset.id = `${type}-${Date.now()}`;
            node.innerHTML = `
                <div class="node-title">${type === 'domain' ? '新能力域' : '新能力项'}</div>
                <div class="node-tags">
                    ${type === 'domain' ? '<span class="node-tag">0个能力项</span>' : ''}
                </div>
            `;
            
            // 添加节点拖拽
            makeNodeDraggable(node);
            
            // 添加点击事件
            node.addEventListener('click', function() {
                selectNode(this);
            });
            
            canvas.appendChild(node);
            selectNode(node);
            return node;
        }

        // 使节点可拖拽
        function makeNodeDraggable(node) {
            let isDragging = false;
            let startX, startY, initialX, initialY;
            
            node.addEventListener('mousedown', function(e) {
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;
                initialX = node.offsetLeft;
                initialY = node.offsetTop;
                node.style.cursor = 'grabbing';
                node.style.zIndex = 1000;
            });
            
            document.addEventListener('mousemove', function(e) {
                if (!isDragging) return;
                
                e.preventDefault();
                const dx = e.clientX - startX;
                const dy = e.clientY - startY;
                
                node.style.left = (initialX + dx) + 'px';
                node.style.top = (initialY + dy) + 'px';
            });
            
            document.addEventListener('mouseup', function() {
                if (isDragging) {
                    isDragging = false;
                    node.style.cursor = 'move';
                    node.style.zIndex = '';
                }
            });
        }

        // 选中节点
        function selectNode(node) {
            // 移除其他节点的选中状态
            document.querySelectorAll('.competency-node').forEach(n => {
                n.style.borderColor = '#e5e7eb';
            });
            
            // 选中当前节点
            node.style.borderColor = '#2563eb';
            currentNode = node;
            
            // 更新属性面板
            document.getElementById('node-name').value = node.querySelector('.node-title').textContent;
            
            // 清空产品线选择
            document.querySelectorAll('#property-panel .product-line-chip').forEach(chip => {
                chip.classList.remove('selected');
            });
            
            // 加载已选择的产品线
            if (!node.classList.contains('domain')) {
                node.querySelectorAll('.node-tag').forEach(tag => {
                    const tagText = tag.textContent;
                    document.querySelectorAll('#property-panel .product-line-chip').forEach(chip => {
                        if (chip.dataset.line === tagText) {
                            chip.classList.add('selected');
                        }
                    });
                });
            }
            
            // 加载已关联的知识库
            const kbContainer = document.getElementById('selected-knowledge-bases');
            kbContainer.innerHTML = '';
            
            if (node.dataset.knowledgeBases) {
                try {
                    const kbs = JSON.parse(node.dataset.knowledgeBases);
                    selectedKnowledgeBases.clear();
                    
                    kbs.forEach(kb => {
                        selectedKnowledgeBases.add(kb.id);
                        const selectedItem = document.createElement('div');
                        selectedItem.className = 'selected-item';
                        selectedItem.innerHTML = `
                            ${kb.title}
                            <span class="remove-btn" onclick="removeKnowledgeBase('${kb.id}')">×</span>
                        `;
                        kbContainer.appendChild(selectedItem);
                    });
                } catch (e) {
                    console.error('Error loading knowledge bases:', e);
                }
            }
        }

        // 初始化已有节点
        document.querySelectorAll('.competency-node').forEach(node => {
            makeNodeDraggable(node);
            node.addEventListener('click', function() {
                selectNode(this);
            });
        });

        // 模态框控制
        function showUploadModal() {
            document.getElementById('uploadModal').classList.add('active');
        }

        function closeUploadModal() {
            document.getElementById('uploadModal').classList.remove('active');
        }

        // 保存节点属性
        document.querySelector('#property-panel .btn-primary').addEventListener('click', function() {
            if (!currentNode) return;
            
            const name = document.getElementById('node-name').value;
            const description = document.getElementById('node-description').value;
            const selectedLines = Array.from(document.querySelectorAll('#property-panel .product-line-chip.selected'))
                .map(chip => chip.dataset.line);
            
            // 更新节点显示
            currentNode.querySelector('.node-title').textContent = name;
            
            // 更新产品线标签
            const tagsContainer = currentNode.querySelector('.node-tags');
            if (currentNode.classList.contains('domain')) {
                // 域节点显示子节点数量
                const childCount = document.querySelectorAll(`[data-parent="${currentNode.dataset.id}"]`).length;
                tagsContainer.innerHTML = `<span class="node-tag">${childCount}个能力项</span>`;
            } else {
                // 能力项节点显示产品线
                tagsContainer.innerHTML = selectedLines.map(line => 
                    `<span class="node-tag">${line}</span>`
                ).join('');
            }
            
            // 保存到本地存储
            saveCompetencyMap();
            showNotification('保存成功', 'success');
        });

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.style.cssText = `
                position: fixed;
                top: 80px;
                right: 24px;
                padding: 16px 24px;
                background: ${type === 'success' ? '#10b981' : '#2563eb'};
                color: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                font-size: 14px;
                z-index: 3000;
                animation: slideIn 0.3s ease-out;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // 保存完整的系统数据
        function saveSystemData() {
            const systemData = {
                competencyMap: getCompetencyMapData(),
                resources: getResourcesData(),
                assignments: getAssignmentsData()
            };
            
            localStorage.setItem('cadreCompetencySystem', JSON.stringify(systemData));
        }

        function getCompetencyMapData() {
            const nodes = [];
            const connections = [];
            
            document.querySelectorAll('.competency-node').forEach(node => {
                const nodeData = {
                    id: node.dataset.id,
                    type: node.classList.contains('domain') ? 'domain' : 'competency',
                    title: node.querySelector('.node-title').textContent,
                    position: {
                        x: node.offsetLeft,
                        y: node.offsetTop
                    },
                    parentId: node.dataset.parent || null,
                    productLines: [],
                    knowledgeBases: []
                };
                
                // 获取产品线信息
                if (!node.classList.contains('domain')) {
                    node.querySelectorAll('.node-tag').forEach(tag => {
                        if (!tag.textContent.includes('个能力项')) {
                            nodeData.productLines.push(tag.textContent);
                        }
                    });
                }
                
                // 获取知识库信息
                if (node.dataset.knowledgeBases) {
                    try {
                        nodeData.knowledgeBases = JSON.parse(node.dataset.knowledgeBases);
                    } catch (e) {
                        console.error('Error parsing knowledge bases:', e);
                    }
                }
                
                nodes.push(nodeData);
            });
            
            return { nodes, connections };
        }

        function getResourcesData() {
            // 这里可以从表格中收集资源数据
            return [];
        }

        function getAssignmentsData() {
            // 这里可以从矩阵中收集指派数据
            return [];
        }

        // 保存数据到本地存储
        function saveCompetencyMap() {
            saveSystemData();
        }

        // 加载数据
        function loadCompetencyMap() {
            const saved = localStorage.getItem('cadreCompetencySystem');
            if (saved) {
                const systemData = JSON.parse(saved);
                // 恢复能力地图
                if (systemData.competencyMap && systemData.competencyMap.nodes) {
                    systemData.competencyMap.nodes.forEach(nodeData => {
                        // 这里可以根据保存的数据重建节点
                    });
                }
            }
        }

        // 双击编辑节点名称
        canvas.addEventListener('dblclick', function(e) {
            const node = e.target.closest('.competency-node');
            if (!node) return;
            
            const titleElement = node.querySelector('.node-title');
            const currentTitle = titleElement.textContent;
            
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentTitle;
            input.style.cssText = `
                width: 100%;
                padding: 4px 8px;
                border: 1px solid #2563eb;
                border-radius: 4px;
                font-size: 14px;
                font-weight: 600;
            `;
            
            titleElement.textContent = '';
            titleElement.appendChild(input);
            input.focus();
            input.select();
            
            function saveTitle() {
                const newTitle = input.value || currentTitle;
                titleElement.textContent = newTitle;
                if (currentNode === node) {
                    document.getElementById('node-name').value = newTitle;
                }
                saveCompetencyMap();
            }
            
            input.addEventListener('blur', saveTitle);
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    saveTitle();
                }
            });
        });

        // 右键菜单
        canvas.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            const node = e.target.closest('.competency-node');
            if (!node) return;
            
            // 创建右键菜单
            const menu = document.createElement('div');
            menu.className = 'context-menu';
            menu.style.cssText = `
                position: absolute;
                left: ${e.pageX}px;
                top: ${e.pageY}px;
                background: white;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                padding: 8px 0;
                z-index: 2000;
                min-width: 160px;
            `;
            
            const menuItems = [
                { text: '添加子能力项', action: () => addChildNode(node) },
                { text: '关联知识库', action: () => linkKnowledgeBase(node) },
                { text: '删除节点', action: () => deleteNode(node) }
            ];
            
            menuItems.forEach(item => {
                const menuItem = document.createElement('div');
                menuItem.style.cssText = `
                    padding: 8px 16px;
                    cursor: pointer;
                    font-size: 14px;
                    color: #374151;
                    transition: background 0.2s;
                `;
                menuItem.textContent = item.text;
                menuItem.addEventListener('click', () => {
                    item.action();
                    menu.remove();
                });
                menuItem.addEventListener('mouseenter', () => {
                    menuItem.style.background = '#f3f4f6';
                });
                menuItem.addEventListener('mouseleave', () => {
                    menuItem.style.background = 'transparent';
                });
                menu.appendChild(menuItem);
            });
            
            document.body.appendChild(menu);
            
            // 点击其他地方关闭菜单
            document.addEventListener('click', function closeMenu(e) {
                if (!menu.contains(e.target)) {
                    menu.remove();
                    document.removeEventListener('click', closeMenu);
                }
            });
        });

        function addChildNode(parentNode) {
            const rect = parentNode.getBoundingClientRect();
            const canvasRect = canvas.getBoundingClientRect();
            const x = rect.left - canvasRect.left + 50;
            const y = rect.bottom - canvasRect.top + 30;
            
            const childNode = createNode('competency', x, y);
            childNode.dataset.parent = parentNode.dataset.id;
            
            // 更新父节点的子节点计数
            if (parentNode.classList.contains('domain')) {
                const childCount = document.querySelectorAll(`[data-parent="${parentNode.dataset.id}"]`).length;
                parentNode.querySelector('.node-tags').innerHTML = 
                    `<span class="node-tag">${childCount}个能力项</span>`;
            }
        }

        function linkKnowledgeBase(node) {
            currentNode = node;
            selectNode(node);
            showKnowledgeBaseSelector();
        }

        function deleteNode(node) {
            if (confirm('确定要删除这个节点吗？')) {
                node.remove();
                saveCompetencyMap();
                showNotification('节点已删除', 'success');
            }
        }

        // 页面加载时初始化
        window.addEventListener('load', function() {
            loadCompetencyMap();
        });

        // 知识库选择功能
        let selectedKnowledgeBases = new Set();
        let tempSelectedKBs = new Set();

        function showKnowledgeBaseSelector() {
            // 清空临时选择
            tempSelectedKBs.clear();
            
            // 加载当前节点已关联的知识库
            if (currentNode && currentNode.dataset.knowledgeBases) {
                const kbs = JSON.parse(currentNode.dataset.knowledgeBases || '[]');
                kbs.forEach(kb => tempSelectedKBs.add(kb.id));
            }
            
            // 更新选中状态
            document.querySelectorAll('.kb-item').forEach(item => {
                if (tempSelectedKBs.has(item.dataset.kbId)) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }
            });
            
            document.getElementById('knowledgeBaseModal').classList.add('active');
        }

        function closeKnowledgeBaseModal() {
            document.getElementById('knowledgeBaseModal').classList.remove('active');
        }

        function toggleKBSelection(item) {
            const kbId = item.dataset.kbId;
            
            if (tempSelectedKBs.has(kbId)) {
                tempSelectedKBs.delete(kbId);
                item.classList.remove('selected');
            } else {
                tempSelectedKBs.add(kbId);
                item.classList.add('selected');
            }
        }

        function confirmKBSelection() {
            // 更新当前节点的知识库关联
            selectedKnowledgeBases = new Set(tempSelectedKBs);
            
            // 显示选中的知识库
            const container = document.getElementById('selected-knowledge-bases');
            container.innerHTML = '';
            
            const kbData = [];
            document.querySelectorAll('.kb-item').forEach(item => {
                if (selectedKnowledgeBases.has(item.dataset.kbId)) {
                    const title = item.querySelector('.kb-item-title').textContent;
                    
                    // 创建显示元素
                    const selectedItem = document.createElement('div');
                    selectedItem.className = 'selected-item';
                    selectedItem.innerHTML = `
                        ${title}
                        <span class="remove-btn" onclick="removeKnowledgeBase('${item.dataset.kbId}')">×</span>
                    `;
                    container.appendChild(selectedItem);
                    
                    // 保存数据
                    kbData.push({
                        id: item.dataset.kbId,
                        title: title
                    });
                }
            });
            
            // 保存到节点数据
            if (currentNode) {
                currentNode.dataset.knowledgeBases = JSON.stringify(kbData);
            }
            
            closeKnowledgeBaseModal();
            saveCompetencyMap();
        }

        function removeKnowledgeBase(kbId) {
            selectedKnowledgeBases.delete(kbId);
            
            // 更新显示
            const container = document.getElementById('selected-knowledge-bases');
            const kbData = [];
            
            document.querySelectorAll('.kb-item').forEach(item => {
                if (selectedKnowledgeBases.has(item.dataset.kbId)) {
                    const title = item.querySelector('.kb-item-title').textContent;
                    kbData.push({
                        id: item.dataset.kbId,
                        title: title
                    });
                }
            });
            
            // 更新节点数据
            if (currentNode) {
                currentNode.dataset.knowledgeBases = JSON.stringify(kbData);
                
                // 重新渲染
                showKnowledgeBaseSelector();
                closeKnowledgeBaseModal();
                
                // 重新显示选中的知识库
                container.innerHTML = '';
                kbData.forEach(kb => {
                    const selectedItem = document.createElement('div');
                    selectedItem.className = 'selected-item';
                    selectedItem.innerHTML = `
                        ${kb.title}
                        <span class="remove-btn" onclick="removeKnowledgeBase('${kb.id}')">×</span>
                    `;
                    container.appendChild(selectedItem);
                });
            }
            
            saveCompetencyMap();
        }
    </script>
</body>
</html>
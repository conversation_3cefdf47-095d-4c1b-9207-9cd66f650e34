### **原型迭代计划：从AI团队到AI引擎**

**核心目标：** 将产品原型从“管理几个AI员工”的界面，全面升级为“消费和驱动一个强大AI情报引擎”的平台。

---

#### **第一阶段：核心概念重塑与后端逻辑对齐 (1-2天)**

这个阶段主要是思想和架构上的统一，为后续UI/UX迭代打下基础。

1.  **确立新核心：三AI代理流水线**
    *   **动作**: 在设计上，将所有功能的源头都归于“市场情报官”、“全球战略官”、“目标市场顾问”这三大代理。后续所有界面都要体现出“这是哪个AI的产出”。
    *   **影响**: 废弃旧的、泛化的“AI员工”概念。用户不再是管理“员工”，而是驱动“代理”并消费其产出。

2.  **定义核心产物：AIP空间**
    *   **动作**: 明确我们已经设计好的`AIP空间`是“全球战略官”和“目标市场顾问”分析任务的**唯一、标准化交付物**。
    - **影响**: `prototype/prototype_plan_aip_space.md` 的设计成果被完全保留和提升，成为新架构的核心组件。

---

#### **第二阶段：主应用界面全面革新 (3-5天)**

这是最大刀阔斧的改动，将直接重塑产品的核心用户体验。

1.  **将首页 (`index.html`) 改造为“团队情报中心”**
    *   **动作**: 彻底抛弃旧首页展示“团队成员列表+绩效图表”的布局。
    *   **新设计**:
        *   **主视图**: 设计一个无限滚动的信息流 (Feed)，作为`团队情报中心`。
        *   **卡片类型**: 设计至少三种核心卡片：
            *   **预警卡片**: 由“市场情报官”发布，带有醒目的“黄/绿灯”标识。
            *   **AIP空间卡片**: 由“战略官”或“顾问”发布，应包含我们设计的`总览仪表盘`的核心元素（播客播放器、流水线概览、核心指标）。
            *   **用户活动卡片**: 同事点赞/收藏了某个情报等。
        *   **筛选功能**: 在页面顶部设计清晰的筛选器，允许用户按`AI代理`和`知识库`进行信息流筛选。
    *   **影响**: `index.html` 需要完全重写。

2.  **将雇佣流程 (`hire-new.html`) 改造为“任务委托中心”**
    *   **动作**: 废弃“选择形象 -> 命名”的雇佣流程。
    *   **新设计**: 创建一个新的页面或模块，用于向AI代理下达分析指令。
        *   **入口**: 可以在“团队情报中心”设置一个全局的“发起分析”按钮。
        *   **界面**: 用户选择需要启动的分析任务（如A0-A9），然后可以对任务进行参数配置（例如，指定目标市场、设定分析深度等）。
        *   **产出**: 任务完成后，将在“团队情报中心”生成一个`AIP空间`卡片。
    *   **影响**: `hire-new.html` 和 `hire.js` 文件被废弃，需要创建全新的“任务委托”页面。

---

#### **第三阶段：深度整合与完善AIP空间 (2-3天)**

将我们精心设计的`AIP空间`完美融入新流程。

1.  **完成`AIP空间`高保真原型**
    *   **动作**: 基于`prototype/prototype_plan_aip_space.md`的最终版本，完成高保真UI设计。
    *   **影响**: 这是对现有工作成果的直接转化，风险低，效率高。

2.  **打通“情报中心”到“AIP空间”的流程**
    *   **动作**: 用户在“团队情报中心”点击任何一个`AIP空间`卡片，都应该能无缝跳转到我们设计的`AIP空间`独立页面。
    *   **新设计**:
        *   确保`AIP空间`页面的全局顶部导航栏 (`Global Header`) 设计得以实现。
        *   确保页面能清晰展示其归属的`AI代理`和`知识库`。
    *   **影响**: `workspace-director.html` 和 `workspace-new.html` 被统一的`AIP空间`页面取代。

---

#### **第四阶段：构建P2优先级新功能 (3-4天)**

在稳固的核心体验之上，扩展新的应用场景。

1.  **设计与原型化“干部学习系统”**
    *   **动作**: 根据`feature_specs_cadre_learning_system.md`，设计全新的功能模块。
    *   **新设计**:
        *   **管理者视图**: 设计一个后台界面，核心是“内容选择器”，允许管理者从`团队情报中心`筛选多个`AIP空间`或`预警`，打包成“必读简报”。
        *   **干部视图**: 设计一个“专注学习界面”，屏蔽所有干扰，让干部能沉浸式地学习简报包内的所有内容，并最终提交学习心得。
    *   **影响**: 需要创建全新的`cadre-learning.html`等相关文件。

### **迭代计划总结**

| 阶段 | 核心任务 | 关键动作 | 涉及/影响的文件 | 产出物 |
| :--- | :--- | :--- | :--- | :--- |
| **1** | **概念重塑** | 统一思想，明确三代理引擎和AIP空间的核心地位。 | 所有`@prd`文档 | 清晰的产品架构蓝图 |
| **2** | **主体验革新** | 改造首页为“团队情报中心”，改造雇佣为“任务委托”。 | `index.html`, `hire-new.html` | 全新的“团队情报中心”和“任务委托”原型 |
| **3** | **深度整合** | 完成AIP空间高保真原型，并与情报中心打通。 | `prototype_plan_aip_space.md`, `workspace-*.html` | 一个可交互、信息完整的AIP空间原型 |
| **4** | **功能扩展** | 设计“干部能力与学习系统”，学习流程通过“可视化知识库浏览器”的权限控制和阅读记录追踪实现。 | `prd/prototype_plan_cadre_competency_system.md`, `prd/prototype_plan_knowledge_base.md` | “干部能力系统”的核心流程原型，以及作为学习环境的“知识库浏览器”原型 |

# 功能规格说明: AIP空间 (Artifact Workspace)

## 1. 文档信息

| 项目 | 内容 |
| :--- | :--- |
| **功能名称** | AIP空间 (Artifact Workspace) |
| **关联PRD** | `prd_v3.md` (核心引擎的产出格式) |
| **版本** | 1.1 |
| **创建日期** | 2025年7月21日 |
| **状态** | **草稿** |

## 2. 功能概述

“AIP空间”是Foxu.AI系统所有AI代理（市场情报官、全球战略官、市场拓展专家）产出的标准化、结构化的信息集合。它不仅仅是一份报告，而是一个完整的“决策流水线”，旨在透明化从“调研”到“分析”再到“决策”的全过程，为用户提供即用型、可追溯、可信赖的战略情报。

## 3. 核心结构与组件

每一个“AIP空间”都将包含以下四个核心部分，以支持用户进行高效的战略决策：

### 3.1. 总览仪表盘 (Workspace Dashboard)
- **定位**: AIP空间的入口和核心摘要，提供快速概览和关键操作。
- **组成元素**:
    1.  **AI播客简报 (AI Podcast Briefing)**:
        -   **形式**: 一个约10分钟的AI生成音频文件（MP3），内嵌播放器。
        -   **内容**: AI将本次分析的核心洞察、关键发现和最值得关注的机遇/风险，用生动、口语化的方式进行播报。
        -   **目的**: 勾起用户兴趣，便于在碎片化时间快速掌握要点。
    2.  **决策流水线概览**: 一个可视化的看板，清晰展示“调研”、“分析”、“决策”三个阶段的关键成果摘要。
        -   **调研阶段卡片**: 简要说明调研范围和成果，如“扫描了30个信息源，筛选出8个高相关度来源。”
        -   **分析阶段卡片**: 简要说明分析深度和产出，如“产出1份深度报告，5张关键图表，核心发现是...”
        -   **决策阶段卡片**: 简要说明决策工具和建议，如“已生成12页的会议演示文稿，核心建议是...”

### 3.2. 第一站：调研成果 (Research Findings)
- **定位**: 所有分析的基石，展示原始信息来源及其评估。
- **组成元素**:
    1.  **来源评估报告 (sources.md)**:
        -   **形式**: 一个Markdown文件，详细列出所有被引用的信息来源。
        -   **内容**: 对于每一个信息来源，包含：
            -   **来源名称与链接**。
            -   **可信度评估 (Credibility)**: AI对来源权威性的评分或评级（如：高、中、低）。
            -   **相关性评估 (Relevance)**: AI对该来源与本次分析主题相关度的评分或评级（如：高、中、低）。
            -   **内容摘要 (Summary)**: AI提炼的该来源与本次分析最相关的内容摘要。

### 3.3. 第二站：分析产出 (Analysis Artifacts)
- **定位**: 将原始数据转化为洞察的核心环节，展示AI的分析过程和结果。
- **组成元素**:
    1.  **深度研究报告 (PDF/Markdown)**:
        -   **形式**: 完整的分析过程、数据和详细文字说明。
        -   **内容**: AI根据分析任务生成的详细报告。
    2.  **关键图表集 (Image Library)**:
        -   **形式**: 所有在报告和PPT中使用的图表、图片的高清文件。
        -   **内容**: AI根据数据生成的各类可视化图表。
    3.  **数据模型 (Excel)**:
        -   **形式**: 包含原始数据和计算逻辑的Excel文件。
        -   **内容**: 如果分析涉及数据建模或量化分析，提供可供用户验证和二次分析的数据源。

### 3.4. 第三站：决策工具 (Decision Tools)
- **定位**: 推动行动的临门一脚，提供可直接用于沟通和决策的工具。
- **组成元素**:
    1.  **会议演示 (Meeting-Ready Presentation)**:
        -   **形式**: 一个可以直接在浏览器中打开和演示的HTML格式PPT。
        -   **内容**: AI自动从研究报告中提取关键图表、核心结论和要点，编排成一个逻辑清晰、视觉化的演示文稿。
        -   **目的**: 省去用户制作PPT的时间，确保团队能基于同样的信息和框架进行高效讨论。

### 3.5. 第四站：决策落地 (Decision Implementation)
- **定位**: 将分析和讨论转化为具体决策和行动的引导工具。
- **组成元素**:
    1.  **决策清单 (Decision Checklist)**:
        -   **形式**: AI生成的可填写问卷（Web表单形式），支持多用户填写和结果汇总。
        -   **内容**: AI根据本次分析（调研成果、分析产出）和建议（会议演示PPT）自动生成一系列关键决策点和相关问题。
            -   **问题类型**: 仅支持**选择题**（单选/多选）和**打分题**（如1-5分，1-10分）。
            -   **示例问题**: “您认为本次市场进入策略中，最大的风险是（多选）？”，“您对推荐的渠道伙伴A的合作意愿度是（1-5分）？”，“本次会议后，您对该战略的信心指数是（1-10分）？”
        -   **目的**: 
            -   **会前准备**: 引导参会人员提前思考，带着问题和初步观点参会，提升会议效率。
            -   **会中讨论**: 问卷结果可作为会议讨论的起点，直接聚焦分歧点和共识点。
            -   **会后落地**: 帮助企业将抽象的分析转化为具体的决策和行动清单，明确责任人。
        -   **交互**: 
            -   **会前填写**: 用户收到问卷链接，在会前完成填写。
            -   **会中展示**: 系统实时汇总所有会前填写结果，以图表形式（如饼图、柱状图、平均分）在会议界面展示，供讨论使用。
            -   **会后再次填写**: 会议讨论结束后，由**企业管理员**来填写同一份问卷，记录会议的最终决议。此时，每个题目下方会备注显示会前所有人的选择比例或平均分，帮助管理员回顾讨论前的共识，并记录讨论后的最终决策或共识变化。
            -   支持问卷分发、填写、结果实时汇总和导出。

## 4. 交互与扩展

- **原子化交互**: AIP空间内的每一个组件（如来源、报告、图表、数据模型、PPT，**决策清单**）都支持独立的“点赞”和“收藏”功能，用户的这些活动将同步到“团队情报中心”的信息流中。
- **AI代理归属**: 每个“AIP空间”都将明确标明其是由哪个AI代理（全球战略官或市场拓展专家）产出的，并归档至对应的知识库。
- **可扩展性**: 未来可根据需求增加更多类型的组件，如视频摘要、交互式数据仪表盘等。

# 功能规格说明: 企业管理 (Team & Permission Management)

## 1. 文档信息

| 项目 | 内容 |
| :--- | :--- |
| **功能名称** | 企业管理 (Team & Permission Management) |
| **关联PRD** | `prd_v3.md` (P1核心功能) |
| **版本** | 1.0 |
| **创建日期** | 2025年7月22日 |
| **状态** | **草稿** |

## 2. 功能概述

企业管理模块是Foxu.AI平台的核心管理功能，为企业创始人/CEO提供完整的团队成员管理、权限分配、计费管理和系统设置功能。该模块确保企业能够安全、高效地管理AI代理的访问权限，并为不同角色的用户提供个性化的功能体验。

## 3. 核心功能模块

### 3.1. 成员与权限管理

#### 3.1.1. 成员邀请与管理
- **邀请机制**:
    - **邮件邀请**: 管理员通过邮箱地址邀请新成员
    - **邀请链接**: 生成带有过期时间的邀请链接
    - **批量邀请**: 支持CSV文件批量导入成员信息
- **成员信息管理**:
    - **基本信息**: 姓名、邮箱、部门、职位
    - **状态管理**: 待激活、活跃、暂停、已离职
    - **加入时间**: 记录成员加入和最后活跃时间

#### 3.1.2. 角色权限体系
- **角色定义**:
    - **超级管理员 (Super Admin)**: 企业创始人/CEO，拥有所有权限
    - **管理员 (Admin)**: 高级管理者，可管理成员和权限，但不能修改计费
    - **成员 (Member)**: 普通用户，只能访问被授权的功能和数据
- **权限矩阵**:
    ```
    功能权限                    超级管理员  管理员  成员
    ─────────────────────────────────────────────
    邀请/删除成员                    ✓      ✓     ✗
    分配AI代理权限                   ✓      ✓     ✗
    查看计费信息                     ✓      ✗     ✗
    修改系统设置                     ✓      ✗     ✗
    访问首页信息流                   ✓      ✓     ✓
    使用AI交互中心                   ✓      ✓     ✓
    发起任务委托                     ✓      ✓     ✓*
    访问知识库                       ✓      ✓     ✓*
    参与eLearning                   ✓      ✓     ✓*
    
    * 需要相应的AI代理权限
    ```

#### 3.1.3. AI代理权限分配
- **权限分配机制**:
    - **代理级权限**: 成员可以被授权访问特定的AI代理（全球战略官、市场拓展专家、市场情报官）
    - **产品线权限**: 在代理权限基础上，进一步限制可访问的产品线
    - **功能权限**: 控制成员可以执行的操作（查看、对话、委托任务）
- **权限继承**:
    - **信息流权限**: 有AI代理权限的成员可以在首页看到对应代理的产出
    - **学习任务权限**: 自动在eLearning系统中创建相关的学习任务
    - **知识库权限**: 可以访问对应AI代理关联的知识库内容

### 3.2. 计费与点数管理

#### 3.2.1. 套餐与订阅管理
- **套餐信息展示**:
    - **当前套餐**: 套餐名称、包含的AI代理、月度点数配额
    - **使用统计**: 本月已用点数、剩余点数、使用趋势
    - **到期提醒**: 套餐到期时间和续费提醒
- **订阅管理**:
    - **升级/降级**: 套餐变更申请和审批流程
    - **续费管理**: 自动续费设置和手动续费
    - **发票管理**: 发票申请、下载和历史记录

#### 3.2.2. 点数使用监控
- **实时监控**:
    - **点数消耗**: 按AI代理、任务类型、成员维度的点数使用统计
    - **使用预警**: 点数不足预警和使用异常提醒
    - **成本分析**: 各部门/成员的成本分摊和ROI分析
- **历史分析**:
    - **月度报告**: 点数使用月度报告和趋势分析
    - **成本优化**: 基于使用数据的成本优化建议
    - **预算规划**: 下月点数需求预测和预算规划

### 3.3. 系统设置与配置

#### 3.3.1. 企业信息设置
- **基本信息**:
    - **企业名称**: 企业全称和简称
    - **行业分类**: 企业所属行业和细分领域
    - **企业规模**: 员工数量、年营收等基本信息
- **联系信息**:
    - **主要联系人**: 企业主要联系人信息
    - **技术联系人**: 系统技术对接联系人
    - **财务联系人**: 计费和发票相关联系人

#### 3.3.2. 系统偏好设置
- **通知设置**:
    - **邮件通知**: 各类系统通知的邮件推送设置
    - **站内通知**: AI交互中心的通知偏好设置
    - **预警设置**: 市场情报官预警的接收设置
- **界面设置**:
    - **时区设置**: 企业所在时区和显示偏好
    - **语言设置**: 系统界面语言偏好
    - **主题设置**: 界面主题和品牌定制

## 4. 用户界面设计要求

### 4.1. 导航结构
- **全局导航**: 在主导航栏中的"企业管理"入口
- **二级导航**: 成员与权限、计费与点数、系统设置三个子模块
- **面包屑**: 清晰的页面层级导航

### 4.2. 成员管理界面
- **成员列表**: 表格形式展示所有成员信息
- **权限矩阵**: 可视化的权限分配界面
- **批量操作**: 支持批量邀请、权限分配、状态变更

### 4.3. 计费管理界面
- **仪表盘**: 直观的点数使用和成本统计
- **使用详情**: 详细的点数消耗记录和分析
- **账单管理**: 发票和付款记录管理

## 5. 技术实现要求

### 5.1. 权限控制
- **基于角色的访问控制 (RBAC)**: 实现细粒度的权限控制
- **API权限验证**: 所有API调用都需要进行权限验证
- **前端权限控制**: 根据用户权限动态显示界面元素

### 5.2. 数据安全
- **数据隔离**: 确保不同企业的数据完全隔离
- **访问日志**: 记录所有敏感操作的访问日志
- **数据备份**: 定期备份企业管理相关数据

### 5.3. 集成要求
- **单点登录 (SSO)**: 支持企业现有的SSO系统集成
- **API接口**: 提供企业管理相关的API接口
- **Webhook**: 支持成员变更、权限变更等事件的Webhook通知

## 6. 业务流程

### 6.1. 成员入职流程
1. **管理员发送邀请** → 2. **成员接受邀请并注册** → 3. **管理员分配权限** → 4. **系统自动创建学习任务** → 5. **成员开始使用系统**

### 6.2. 权限变更流程
1. **权限变更申请** → 2. **管理员审批** → 3. **系统更新权限** → 4. **相关功能访问权限同步更新** → 5. **通知相关成员**

### 6.3. 计费异常处理
1. **系统检测异常** → 2. **自动预警通知** → 3. **管理员确认处理** → 4. **调整使用策略** → 5. **记录处理结果**

## 7. 成功指标

### 7.1. 功能指标
- **成员管理效率**: 新成员从邀请到开始使用的平均时间 < 24小时
- **权限准确性**: 权限分配错误率 < 1%
- **系统稳定性**: 企业管理功能可用性 > 99.9%

### 7.2. 用户体验指标
- **管理员满意度**: 企业管理功能满意度 > 4.5/5.0
- **操作便捷性**: 常用管理操作完成时间 < 3分钟
- **学习成本**: 新管理员上手时间 < 30分钟

### 7.3. 业务指标
- **成员活跃度**: 被邀请成员的激活率 > 90%
- **权限利用率**: 被分配权限的实际使用率 > 80%
- **成本控制**: 点数使用预算偏差 < 10%

## 8. 风险与缓解措施

### 8.1. 安全风险
- **风险**: 权限泄露或滥用
- **缓解**: 定期权限审计、访问日志监控、最小权限原则

### 8.2. 操作风险
- **风险**: 误删成员或错误权限分配
- **缓解**: 操作确认机制、操作日志、数据恢复机制

### 8.3. 成本风险
- **风险**: 点数使用超预算
- **缓解**: 实时监控、预警机制、使用限制设置

这个企业管理模块将为Foxu.AI平台提供完整的企业级管理能力，确保平台能够安全、高效地服务于企业客户的复杂管理需求。

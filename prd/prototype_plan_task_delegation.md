# 原型设计规划: 任务委托中心 (Task Delegation Center) - 改进版

## 1. 文档信息

| 项目 | 内容 |
| :--- | :--- |
| **功能名称** | 任务委托中心 (Task Delegation Center) - 改进版 |
| **关联规格** | `prd/feature_specs_ai_agents.md` |
| **版本** | 2.0 |
| **创建日期** | 2025年7月22日 |
| **状态** | **草稿** |

## 2. 设计目标

本原型旨在设计一个清晰、直观、引导性强的界面，让用户可以轻松地向AI代理（全球战略官、市场拓展专家、市场情报官）下达分析指令，并配置相关参数。

**核心改进点**：
-   **默认配置**: 用户开通企业账户后，默认拥有一条产品线，包含一个全球战略官和一个市场情报官。
-   **产品线可见性**: 用户可以清晰地看到自己已开通的产品线及其对应的AI代理。
-   **层级展示**: 用户可以看到不同产品线下面不同目标市场的市场拓展专家。
-   **实例化选择**: 从抽象的代理类型选择转变为具体的代理实例选择。
-   **扩展机制**: 当用户需要开通第二条产品线或新增全球战略官/市场情报官时，需联系专属顾问在后台进行开通。

## 3. 原型设计范围

-   **任务委托主界面**: 一个多步骤的向导式（Wizard）界面
-   **AI代理类型与实例选择**: 分层展示用户已购买的具体AI代理实例
-   **分析任务选择**: 根据所选代理实例，展示其能执行的任务列表
-   **参数配置**: 为特定任务提供输入参数的表单
-   **回顾与确认**: 最终的任务摘要和确认页面

## 4. 关键设计点与思考

### 4.1. 入口与整体流程

-   **多重入口设计**: 任务委托中心提供多个便捷的访问入口
    1. **全局导航栏**: 顶部导航栏的 `[任务委托中心]` 主入口
    2. **首页快速入口**: 团队绩效概览中的 `[🎯 任务委托中心]` 快速按钮
    3. **首页筛选器**: 筛选器右侧的 `[+ 任务委托中心]` 按钮
    4. **首页管理入口**: 绩效概览中的 `[📊 管理AI团队]` 按钮
-   **页面模式**: 任务委托中心作为独立页面，而非弹窗或向导模式
-   **主要功能**:
    1. **AI代理管理**: 查看和管理用户购买的所有AI代理实例
    2. **任务发起**: 向特定AI代理发起新的分析任务
    3. **新增代理雇佣**: 允许用户根据业务需求雇佣新的AI代理实例
    4. **状态监控**: 实时查看各AI代理的工作状态和绩效
-   **任务发起流程**: 当用户选择发起新任务时，采用向导式（Wizard）的步骤条界面
    -   `步骤一：选择AI代理实例` -> `步骤二：选择分析任务` -> `步骤三：配置任务参数` -> `步骤四：回顾与确认`

### 4.2. 任务委托中心主界面设计

任务委托中心作为独立页面，主要功能是展示和管理用户的AI代理团队，并提供任务发起入口。

#### 4.2.1. 页面入口与导航
-   **页面标题**: "任务委托中心"
-   **面包屑导航**: `首页 > 任务委托中心`
-   **页面描述**: "管理您的AI代理团队，发起新的分析任务"

### 4.3. AI代理管理界面（主要功能）

#### 4.3.1. 整体布局设计
-   **布局**: 采用**左右分栏**的高效布局模式
-   **左侧区域**: 树状导航，展示用户的AI代理组织架构
-   **右侧区域**: 代理详情卡片，显示选中代理的具体信息和配置

#### 4.3.2. 左侧：树状导航结构 (已更新)

**默认配置说明**: 用户开通企业账户后，默认拥有一条产品线（如“产品线A”），其中包含一个全球战略官和一个市场情报官。当用户需要开通第二条产品线或新增全球战略官/市场情报官时，需联系专属顾问在后台进行开通。

```
📁 我的AI代理团队
├── 📦 产品线A (当前产品线)
│   ├── 👤 🌍 全球战略官A (负责人: 李经理)
│   ├── 👤 📊 市场情报官A (负责人: 张总)
│   └── 🎯 市场拓展专家 (可雇佣多个)
│       ├── 💺 🇩🇪 德国市场顾问 (席位空缺)
│       └── ➕ 雇佣新的市场拓展专家
└── ➕ 联系专属顾问开通新产品线/核心AI代理
```

**树状导航特点**：
-   **层级清晰**: 以**产品线**为顶级节点，其下分组展示不同类型的AI代理实例。
-   **席位状态指示 (新增)**: 在每个AI代理实例节点前，增加一个图标明确其席位状态：
    - `👤 已指派`: 表示该AI已绑定高管。
    - `💺 席位空缺`: 表示该AI尚未绑定高管。
-   **状态指示**: 用颜色或图标表示代理的工作状态（活跃、暂停等）。
-   **搜索与扩展**: 保留搜索和新增代理入口。

#### 4.3.3. 右侧：代理详情卡片 (已更新)
当用户在左侧树状导航中选择具体的代理实例时，右侧显示该代理的详细信息，并**突出显示“协作席位管理”模块**。

**全球战略官详情卡片 (示例)**：
```
┌─────────────────────────────────────────────────┐
│ 🌍 全球战略官A                                  │
│ 产品线A | 状态: ● 活跃中                        │
├─────────────────────────────────────────────────┤
│ 👤 协作席位管理                                 │
│    负责人: [头像] 李经理                         │
│    任职资格: ✅ 已认证 (2025-07-20)              │
│                                    [更换负责人] │
├─────────────────────────────────────────────────┤
│ 📊 工作概览                                     │
│ • 最近分析: 2025-01-15 "欧洲市场进入策略"       │
│ • 本月完成: 3个分析任务                         │
│ • 平均耗时: 4.2天 (vs 传统21天, ↑80%效率)      │
│ ─────────────────────────────────────────────── │
│ 🎯 可执行任务                                   │
│ • A0: 全球商业环境分析                          │
│ • ... (其他任务)                                │
│ ─────────────────────────────────────────────── │
│                                    [选择此代理] │
└─────────────────────────────────────────────────┘
```

**市场拓展专家详情卡片 (席位空缺/待学习示例)**：
```
┌─────────────────────────────────────────────────┐
│ 🇩🇪 德国市场顾问                                │
│ 产品线A | 状态: ● 活跃中                        │
├─────────────────────────────────────────────────┤
│ 👤 协作席位管理                                 │
│    负责人: [头像] 王总监                         │
│    任职资格: ⚠️ 待学习 (尚未完成认证)            │
│                                    [指派学习任务] │
├─────────────────────────────────────────────────┤
│ 📊 工作概览                                     │
│ • 最近分析: 2025-01-10 "德国竞品分析报告"       │
│ • ... (其他信息)                                │
│ ─────────────────────────────────────────────── │
│ 🎯 可执行任务 (部分任务可能因未认证而置灰)      │
│ • A4: 竞品分析 • A5: 客户画像分析               │
│ • ... (其他任务)                                │
│ ─────────────────────────────────────────────── │
│                                    [选择此代理] │
└─────────────────────────────────────────────────┘
```

- **协作席位管理模块**:
    - **显示已绑定高管**: 展示高管姓名和头像。
    - **显示任职资格状态**: `✅ 已认证` 或 `⚠️ 待学习`。
    - **提供管理操作**:
        - 若席位空缺，显示 **`[指派负责人]`** 按钮。
        - 若负责人“待学习”，显示 **`[指派学习任务]`** 按钮，点击后可快速为其在干部学习系统中创建学习任务。
        - 提供 **`[更换负责人]`** 的操作入口。

#### 4.3.4. 任务发起入口
在右侧代理详情卡片中，用户可以通过 **[选择此代理]** 按钮发起新任务。

### 4.4. 任务发起流程（向导模式） (已更新)

在向导流程中，将“人”的因素前置。

#### 4.4.1. 流程概览
-   **步骤流程**: `选择分析任务` -> `配置任务参数` -> `回顾与确认`
-   **上下文保持**: 已选择的 **人机团队 (AI代理 + 负责人)** 信息在整个流程中保持显示。

#### 4.4.2. 步骤一：选择分析任务
-   **前置信息**: 在选择任务前，系统已明确用户选择的是哪个 **AI代理** 及其 **负责人**。
-   **布局**: 页面顶部明确显示“**执行团队: 德国市场顾问 + 王总监 (⚠️ 待学习)**”。
-   **任务列表**: 根据所选代理实例，展示其能执行的任务列表。
    -   如果负责人**未认证**，部分高级或关键任务可能会被**置灰**并提示“负责人需完成认证后方可委托此任务”。
-   **交互**: 用户选择一个可用的分析任务后，进入下一步

#### 4.4.3. 步骤二：配置任务参数

这是最具交互性的步骤，界面根据所选任务动态生成。

-   **布局**: 一个结构化的表单
-   **通用参数**:
    -   **任务名称**: 用户可自定义本次任务的名称，方便后续追踪
    -   **执行代理**: 显示选定的具体代理实例（如"全球战略官A"或"德国市场顾问(产品线A)"），不可修改
    -   **所属产品线**: 自动显示该代理实例所属的产品线，不可修改
-   **动态参数 (示例)**:
    -   **如果选择A3 (目标市场研究)**:
        -   `目标国家`: (下拉框或搜索框)
        -   `核心问题`: (文本域) "请列出您最关心的3-5个问题"
        -   `指定竞品`: (标签输入框) "请输入需要重点分析的竞争对手名称"
    -   **如果选择A4 (竞品分析)**:
        -   `目标国家`: (下拉框)
        -   `主要竞品`: (标签输入框，必填)
        -   `分析维度`: (多选框) `[定价策略]`, `[产品特性]`, `[营销活动]`, `[用户评价]`
-   **交互**: 提供清晰的输入提示和校验

#### 4.4.4. 步骤三：回顾与确认

-   **布局**: 一个只读的摘要页面
-   **内容**:
    -   **委托代理**: `全球战略官A` (具体实例名称)
    -   **所属产品线**: `产品线A`
    -   **分析任务**: `A3 - 目标市场研究`
    -   **任务名称**: `2025年Q1德国市场进入策略研究`
    -   **核心参数**:
        -   `目标国家: 德国`
        -   `核心问题: ...`
    -   **预期产出**: `一个包含完整分析的AIP空间`
    -   **预计耗时**: `3-5个工作日`
-   **交互**:
    -   `[<上一步]` 按钮，允许用户返回修改
    -   `[确认委托]` 按钮，点击后系统开始执行任务

#### 4.4.5. 任务启动后的反馈

-   点击"确认委托"后，用户可以选择：
    1. **返回任务委托中心**: 继续管理其他AI代理或发起更多任务
    2. **跳转到首页**: 在首页信息流中查看任务进度
-   **任务状态反馈**:
    - 在首页信息流的顶部，出现一张新的"任务进行中"卡片
    - 卡片显示：任务名称、执行代理实例、所属产品线、进度条
    - 在任务委托中心的代理详情卡片中，也会更新该代理的工作状态
-   **任务完成通知**:
    - 任务完成后，"任务进行中"卡片自动转变为完整的"AIP空间卡片"
    - 用户会收到AI交互中心的通知提醒
    - 代理详情卡片中的统计数据会相应更新

### 4.5. 新增代理雇佣流程

当用户需要扩展AI代理团队时，可以通过此向导流程雇佣新的代理实例。

#### 4.5.1. 流程概览
-   **触发方式**: 在AI代理管理界面的左侧树状导航中，在各代理类型（如“全球战略官”）或产品线层级提供“雇佣新代理”按钮。点击时，系统将根据点击位置自动预选代理类型。
-   **显示模式**: 采用向导式（Wizard）的步骤条界面，可以是弹窗或页面跳转。
-   **步骤流程**: `步骤一：选择代理类型` -> `步骤二：配置代理信息` -> `步骤三：确认雇佣`

#### 4.5.2. 步骤一：选择代理类型
-   **布局**: 展示可雇佣的AI代理类型列表，如"全球战略官"、"市场拓展专家"。
-   **交互**: 
    -   如果用户从特定代理类型（如“全球战略官”）的节点点击“雇佣新代理”，则该类型将被自动预选并高亮显示，用户可直接进入下一步。
    -   如果用户从“我的AI代理团队”根节点点击“雇佣新代理”，则需要手动选择代理类型。
    -   用户选择或确认代理类型后，进入下一步。

#### 4.5.3. 步骤二：配置代理信息
-   **布局**: 根据所选代理类型，动态生成配置表单。
-   **通用配置项**:
    -   **代理名称**: 用户自定义代理实例的名称（如"美国市场专家"）。
    -   **所属产品线**: 选择该代理将归属的产品线。
-   **特定配置项**:
    -   **市场拓展专家**: 需要选择"目标国家/地区"、"专业方向"（如B2B制造业、消费品零售）、"语言支持"等。
    -   **全球战略官**: 可能需要选择"关注区域"（如全球、亚太地区）等。
-   **交互**: 提供清晰的输入提示和校验。

#### 4.5.4. 步骤三：确认雇佣
-   **布局**: 一个只读的摘要页面，显示即将雇佣的代理实例的详细信息。
-   **内容**: 代理类型、名称、所属产品线、配置参数等。
-   **交互**:
    -   `[<上一步]` 按钮，允许用户返回修改。
    -   `[确认雇佣]` 按钮，点击后系统完成代理实例的创建。

#### 4.5.5. 雇佣成功后的反馈
-   点击"确认雇佣"后，用户可以选择：
    1. **返回任务委托中心**: 查看新雇佣的代理实例。
    2. **发起新任务**: 直接为新雇佣的代理发起分析任务。
-   **代理列表更新**: 新雇佣的代理实例将出现在左侧树状导航中，并显示其初始状态（如"空闲中"）。

### 4.6. 任务状态跟踪与历史管理 (已更新)

本功能旨在为用户提供对其AI代理所执行任务的全面可见性，包括实时进度和历史记录，并**突出“人”在其中的角色**。

#### 4.6.1. 实时任务状态展示
-   **在右侧代理详情卡片中**:
    -   新增“当前任务”区域，显示代理正在执行的任务名称、进度条，以及**明确标出该任务的负责人(高管)**。
    -   例如: `进行中: A3-德国市场研究 (负责人: 李经理) [████... 60%]`

#### 4.6.2. 历史任务管理
-   **在右侧代理详情卡片中**:
    -   新增“历史任务”区域，以列表形式展示该代理已完成的任务。
    -   每条历史任务包含：任务名称、完成日期、交付物链接，以及**明确标出该任务的负责人(高管)**。

#### 4.6.3. 任务进度通知
-   **AI交互中心通知**: 任务状态发生关键变化时（如：任务开始、任务进度达到50%、任务完成、任务异常），通过AI交互中心向用户推送通知。
-   **邮件/短信提醒**: 用户可配置是否接收关键任务状态变化的邮件或短信提醒。

## 5. 决策效率与质量量化模型

### 5.1. 模型概述

基于AIP空间的完整决策流水线（调研→分析→决策工具→决策落地），我们建立了一套量化的决策效率和质量评估模型。该模型通过对比传统决策方式与AI驱动的决策流程，为企业提供可量化的价值证明。

### 5.2. 核心指标体系

#### 5.2.1. 决策效率指标 (Decision Efficiency Metrics)

**传统决策流程 vs AI驱动决策流程对比**：

| 决策环节 | 传统方式 | AI驱动方式 | 效率提升 |
|---------|---------|-----------|---------|
| **信息收集** | 7-14天 | 1-2天 | 75-85% |
| **分析报告** | 10-20天 | 3-5天 | 70-75% |
| **会议准备** | 2-3天 | 0.5天 | 80-85% |
| **决策讨论** | 2-4小时 | 1-1.5小时 | 50-65% |
| **决议记录** | 1-2天 | 实时 | 95-100% |

**量化指标**：
- **决策周期缩短**: 平均从21-41天缩短至4.5-8.5天，提升**79-83%**
- **会议效率提升**: 通过会前问卷和数据可视化，会议时间缩短**50-65%**
- **决策参与度**: 会前填写率通常达到**85-95%**，确保充分准备

#### 5.2.2. 决策质量指标 (Decision Quality Metrics)

**证据基础强度**：
- **信息来源数量**: 平均每个AIP空间包含15-30个验证信息源
- **可信度评估**: 所有信息源都经过AI可信度评估（高/中/低）
- **数据支撑**: 100%的决策建议都有量化数据支撑

**决策一致性**：
- **会前共识度**: 通过问卷量化团队初始共识水平
- **决策收敛度**: 会中讨论后的观点收敛程度
- **执行对齐度**: 会后决议与会前预期的匹配度

**决策可追溯性**：
- **完整记录**: 从调研到决议的全流程透明化记录
- **版本管理**: 决策过程中的所有变更都有版本记录
- **责任明确**: 每个决策点都有明确的参与者和时间戳

### 5.3. 价值量化模型

#### 5.3.1. 时间成本节约

**高管时间价值计算**：
```
假设参与决策的高管平均时薪 = ¥500/小时
传统决策流程高管投入时间 = 40-60小时
AI驱动决策流程高管投入时间 = 8-12小时
单次决策节约时间成本 = (40-60) - (8-12) = 28-52小时
单次决策节约成本 = ¥14,000 - ¥26,000
```

#### 5.3.2. 决策质量提升价值

**错误决策成本避免**：
- **传统决策错误率**: 约15-25%（基于信息不充分）
- **AI驱动决策错误率**: 约5-10%（基于充分信息和数据）
- **错误决策平均损失**: ¥50万-200万
- **年度避免损失**: (15%-5%) × 决策次数 × 平均损失

#### 5.3.3. 组织效率提升

**团队协作效率**：
- **决策参与度提升**: 85-95% vs 传统的60-70%
- **执行对齐度提升**: 90-95% vs 传统的70-80%
- **重复讨论减少**: 减少60-80%的重复性会议

### 5.4. ROI计算示例

**年度价值计算（以中型企业为例）**：

**投入成本**：
- Foxu.AI平台年费: ¥50万
- 团队培训成本: ¥5万
- 总投入: ¥55万

**产出价值**：
- 时间成本节约: 20次决策 × ¥20万 = ¥400万
- 错误决策避免: 10% × 20次 × ¥100万 = ¥200万
- 效率提升价值: ¥150万
- 总产出: ¥750万

**ROI计算**：
```
ROI = (产出价值 - 投入成本) / 投入成本 × 100%
ROI = (750万 - 55万) / 55万 × 100% = 1,264%
```

**关键成功指标 (KSI)**：
- 决策周期缩短 > 75%
- 会议效率提升 > 50%
- 决策参与度 > 85%
- 执行对齐度 > 90%
- 年度ROI > 1000%

## 6. 设计优势与用户价值

### 5.1. 高效的左右分栏布局
-   **一屏全览**: 用户可以在一个界面内同时看到所有代理和详细信息
-   **快速切换**: 左侧点击即可在右侧查看不同代理的详情，无需页面跳转
-   **空间利用**: 充分利用宽屏显示器的横向空间，信息密度更高

### 5.2. 清晰的层级结构
-   **树状导航**: 代理类型→产品线→具体实例的三级层级一目了然
-   **数量统计**: 每个节点显示包含的代理数量，便于用户了解资源分布
-   **状态可视**: 通过颜色和图标直观显示每个代理的工作状态

### 5.3. 丰富的代理信息展示
-   **工作概览**: 显示代理的最近活动、完成任务数、平均耗时等关键指标
-   **能力展示**: 清晰列出代理可执行的所有任务类型，包括增值服务
-   **专业信息**: 对于市场拓展专家，显示专业领域、语言支持等特色信息

### 5.4. 智能的交互体验
-   **即时反馈**: 左侧选择立即在右侧显示详情，响应迅速
-   **上下文保持**: 选择代理后，后续步骤会保持代理信息的上下文
-   **搜索支持**: 左侧提供搜索功能，快速定位特定代理

### 5.5. 可扩展的架构设计
-   **灵活布局**: 左右分栏比例可调，适应不同屏幕尺寸
-   **模块化**: 右侧详情卡片采用模块化设计，便于添加新的信息区块
-   **响应式**: 支持桌面、平板、移动端的不同布局模式

## 6. 具体界面设计细节

### 6.1. 左右分栏布局规范

#### 6.1.1. 整体布局尺寸
```
┌─────────────────────────────────────────────────────────────┐
│                    任务委托中心 - 选择AI代理                 │
├─────────────────┬───────────────────────────────────────────┤
│   左侧导航区     │              右侧详情区                   │
│   (宽度: 30%)   │            (宽度: 70%)                   │
│                │                                         │
│ 📁 我的AI代理团队 │  🌍 全球战略官A                          │
│ ├── 🌍 全球战略官 │  产品线A | 状态: ● 活跃中                │
│ │   ├── 产品线A  │  ─────────────────────────────────────  │
│ │   └── 产品线B  │  📊 工作概览                            │
│ ├── 🎯 市场拓展专家│  • 最近分析: 2025-01-15                │
│ │   ├── 产品线A  │  • 本月完成: 3个分析任务                │
│ │   └── 产品线B  │  • 平均耗时: 4.2天                      │
│ └── 📊 市场情报官 │  ─────────────────────────────────────  │
│     ├── 产品线A  │  🎯 可执行任务                          │
│     └── 产品线B  │  • A0-A3: 常规分析任务                  │
│                │  • GGP, SNT: 增值服务                   │
│                │  ─────────────────────────────────────  │
│                │                        [选择此代理]    │
└─────────────────┴───────────────────────────────────────────┘
```

#### 6.1.2. 左侧树状导航设计
**节点类型与样式**：
```
� 根节点 (我的AI代理团队)
├── � 代理类型节点 (全球战略官) + 数量徽章 (2)
│   ├── 📦 产品线节点 (产品线A) + 状态指示器
│   │   └── �� 代理实例节点 (德国市场顾问) + 状态点
│   └── 📦 产品线节点 (产品线B)
├── 🎯 代理类型节点 (市场拓展专家) + 数量徽章 (5)
└── 📊 代理类型节点 (市场情报官) + 数量徽章 (2)
```

**交互状态**：
-   **默认状态**: 节点显示基本信息和图标
-   **悬停状态**: 节点背景色变化，显示更多操作选项
-   **选中状态**: 节点高亮显示，右侧显示对应详情
-   **展开/折叠**: 点击节点前的展开图标控制子节点显示

**状态指示器**：
-   ● 绿色圆点: 活跃中
-   ⏸ 黄色暂停图标: 暂停服务
-   � 灰色工具图标: 维护中
-   ❌ 红色叉号: 服务异常

#### 6.1.3. 右侧详情卡片设计
**卡片结构**：
```
┌─────────────────────────────────────────────────┐
│ [图标] [代理名称]                                │
│ [产品线] | [状态指示器] [状态文字]               │
│ ─────────────────────────────────────────────── │
│ 📊 [信息区块标题]                               │
│ • [关键信息项1]                                 │
│ • [关键信息项2]                                 │
│ • [关键信息项3]                                 │
│ ─────────────────────────────────────────────── │
│ 🎯 [功能区块标题]                               │
│ • [功能项1] • [功能项2]                         │
│ • [功能项3] • [功能项4]                         │
│ ─────────────────────────────────────────────── │
│                                    [操作按钮]  │
└─────────────────────────────────────────────────┘
```

**信息层级**：
1. **头部区域**: 代理身份信息（名称、产品线、状态）
2. **工作概览**: 最近活动、统计数据、性能指标
3. **能力展示**: 可执行任务、专业领域、服务范围
4. **操作区域**: 主要操作按钮

### 6.2. 交互状态设计

#### 6.2.1. 代理类型选择状态
-   **未选择状态**: 卡片呈现默认样式，底部区域隐藏
-   **选择状态**: 卡片高亮显示，底部区域展开显示实例列表
-   **其他卡片**: 变为半透明状态，表示未选择

#### 6.2.2. 代理实例选择状态
-   **可选择状态**: 卡片边框为蓝色虚线，鼠标悬停时高亮
-   **已选择状态**: 卡片边框为蓝色实线，背景色变化
-   **不可用状态**: 卡片变为灰色，显示"暂停服务"等提示

### 6.3. 响应式布局考虑

#### 6.3.1. 桌面端布局 (≥1200px)
-   **左侧导航区**: 固定宽度30%，最小宽度300px
-   **右侧详情区**: 自适应宽度70%，最小宽度600px
-   **树状导航**: 支持多层级展开，节点间距适中
-   **详情卡片**: 完整显示所有信息区块

#### 6.3.2. 平板端布局 (768px-1199px)
-   **左侧导航区**: 固定宽度35%，最小宽度280px
-   **右侧详情区**: 自适应宽度65%
-   **树状导航**: 适当缩小节点间距，保持层级清晰
-   **详情卡片**: 信息区块可能需要垂直滚动

#### 6.3.3. 移动端布局 (<768px)
-   **布局模式**: 切换为单栏布局，支持左右滑动切换
-   **导航模式**:
    -   默认显示树状导航
    -   选择代理后滑动到详情页面
    -   提供"返回选择"按钮
-   **树状导航**:
    -   节点文字可能缩略显示
    -   支持手势展开/折叠
-   **详情卡片**:
    -   信息区块垂直堆叠
    -   操作按钮固定在底部

## 7. 用户体验优化建议

### 7.1. 智能推荐
-   基于用户历史使用记录，在代理选择界面突出显示常用的代理实例
-   在任务选择界面，优先展示该代理实例最近执行过的任务类型

### 7.2. 快捷操作
-   提供"重复上次任务"功能，一键复制上次的任务配置
-   支持"收藏常用配置"，保存常用的任务参数组合

### 7.3. 状态提示
-   实时显示代理实例的工作负载状态（空闲/忙碌/排队中）
-   预估任务完成时间，帮助用户合理安排工作计划

## 8. 原型阶段与交付物

-   **阶段1: 流程图与线框图**: 绘制完整的用户任务委托流程，并为每个步骤创建低保真线框图
-   **阶段2: 交互原型**: 创建一个可点击的中保真原型，完整模拟从发起分析到确认委托的全过程
-   **阶段3: 高保真设计**: 完善视觉设计，特别是代理实例卡片的展示效果和层级结构的可视化
-   **阶段4: 用户测试**: 进行可用性测试，验证新的分层选择模式是否提升了用户体验

## 9. 技术实现考虑

### 9.1. 数据结构设计
-   用户-产品线-代理实例的关联关系
-   代理实例的状态管理和实时更新
-   任务历史和配置的存储结构

### 9.2. 性能优化
-   代理实例列表的懒加载
-   实时状态更新的WebSocket连接
-   任务配置的本地缓存机制


设计简述：
1. 左侧树状导航，右侧内容
2. 右侧三种类型：全球战略官详情卡片、市场拓展专家详情卡片、市场情报官详情卡片
3. 任务发起：4.4. 任务发起流程，采用弹窗形式，在弹窗上部分写一个 steps，标明当前步骤状态。一个全局的按钮切换步骤（上一步、下一步）
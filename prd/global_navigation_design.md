# 全局导航设计规范

## 1. 文档信息

| 项目 | 内容 |
| :--- | :--- |
| **功能名称** | 全局导航设计规范 |
| **版本** | 1.0 |
| **创建日期** | 2025年7月22日 |
| **状态** | **草稿** |

## 2. 设计目标

统一Foxu.AI平台的全局导航体验，确保用户能够快速、直观地访问所有核心功能模块。

## 3. 全局导航栏设计

### 3.1. 主导航结构

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Foxu.AI Logo  [首页] [知识库] [eLearning] [任务委托中心] [企业管理] ... [🔔] [👤] │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 3.2. 导航项详细说明

| 导航项 | 原名称 | 功能描述 | 对应页面/功能 |
|--------|--------|----------|---------------|
| **首页** | 团队情报中心 | 信息流、绩效概览、AI交互 | `prototype_plan_team_hub.md` |
| **知识库** | 可视化知识库浏览器 | 四大知识库的统一入口 | `prototype_plan_knowledge_base.md` |
| **eLearning** | 干部学习系统 | 干部学习、认证、简报管理 | `feature_specs_cadre_learning_system.md` |
| **任务委托中心** | 发起分析 | AI代理管理、任务发起 | `prototype_plan_task_delegation.md` |
| **企业管理** | 企业成员与权限管理 | 成员管理、权限分配、计费 | 待创建 |
| **🔔** | AI交互中心 | 通知、AI对话、消息管理 | 集成在全局导航中 |
| **👤** | 用户菜单 | 个人设置、账户管理 | 标准用户菜单 |

### 3.3. 二级导航设计

#### 3.3.1. 知识库二级导航

```
[知识库 ▼]
├── 📚 企业战略库
├── 🌍 目标市场数据库  
├── 🏢 公司数据库
└── 📱 产品数据库
```

#### 3.3.2. 企业管理二级导航

```
[企业管理 ▼]
├── 👥 成员与权限
├── 💰 计费与点数
└── ⚙️ 系统设置
```

## 4. 首页快速入口设计

### 4.1. 团队绩效概览快速入口

在首页的团队绩效概览部分，提供以下快速入口：

```
[📊 管理AI团队] [💬 与AI对话] [🎯 任务委托中心] [📚 知识库] [👨‍🎓 eLearning]
```

### 4.2. 快速入口功能映射

| 快速入口 | 跳转目标 | 功能描述 |
|----------|----------|----------|
| 📊 管理AI团队 | 任务委托中心 | 直接跳转到AI代理管理界面 |
| 💬 与AI对话 | AI交互中心 | 打开AI对话弹窗 |
| 🎯 任务委托中心 | 任务委托中心 | 跳转到任务发起流程 |
| 📚 知识库 | 知识库浏览器 | 跳转到知识库首页 |
| 👨‍🎓 eLearning | eLearning系统 | 跳转到学习管理界面 |

## 5. 左侧边栏导航设计

### 5.1. 统一侧边栏结构

在知识库、eLearning等页面使用统一的左侧边栏：

```
┌───────────────────────┐
│ 📁 导航                │
├───────────────────────┤
│ 🏠 首页                │
│ 📚 知识库              │
│   ├── 企业战略库       │
│   ├── 目标市场数据库   │
│   ├── 公司数据库       │
│   └── 产品数据库       │
│ 👨‍🎓 eLearning          │
│ 🎯 任务委托中心        │
│ ⚙️ 企业管理            │
└───────────────────────┘
```

### 5.2. 侧边栏交互规则

- **当前页面高亮**: 用户当前所在页面的导航项应高亮显示
- **展开/折叠**: 有子项的导航项支持展开/折叠
- **快速跳转**: 点击任意导航项可快速跳转到对应页面

## 6. 响应式设计

### 6.1. 桌面端 (≥1200px)
- 完整显示所有导航项
- 二级导航以下拉菜单形式展示
- 左侧边栏完全展开

### 6.2. 平板端 (768-1199px)
- 主导航项可能需要适当缩短文字
- 二级导航保持下拉菜单形式
- 左侧边栏可折叠

### 6.3. 移动端 (<768px)
- 主导航收缩为汉堡菜单
- 侧边栏改为全屏抽屉式导航
- 快速入口改为垂直堆叠

## 7. 设计原则

### 7.1. 一致性原则
- 所有页面使用相同的导航结构
- 统一的图标和命名规范
- 一致的交互行为

### 7.2. 可发现性原则
- 所有核心功能都有明确的入口
- 重要功能提供多个访问路径
- 清晰的视觉层级

### 7.3. 效率原则
- 常用功能提供快速入口
- 减少用户的点击路径
- 支持键盘快捷键

## 8. 实施优先级

### P0 (必须实现)
- 全局导航栏基础结构
- 主要导航项的跳转功能
- AI交互中心集成

### P1 (重要功能)
- 二级导航下拉菜单
- 首页快速入口
- 左侧边栏导航

### P2 (增强功能)
- 响应式适配
- 键盘快捷键支持
- 导航状态记忆

## 9. 技术实现考虑

### 9.1. 路由管理
- 使用前端路由管理页面跳转
- 支持浏览器前进/后退
- URL友好，支持直接访问

### 9.2. 状态管理
- 记录用户当前位置
- 保持导航状态
- 支持页面刷新后状态恢复

### 9.3. 性能优化
- 导航组件懒加载
- 图标资源优化
- 减少重复渲染

## 10. 用户体验指标

### 10.1. 导航效率
- 用户找到目标功能的平均时间
- 导航错误率（点错导航项的比例）
- 用户对导航结构的满意度

### 10.2. 功能发现
- 新功能的发现率
- 各导航项的使用频率
- 用户反馈的导航改进建议

这个全局导航设计确保了所有核心功能都有清晰的入口，同时保持了界面的简洁性和用户体验的一致性。

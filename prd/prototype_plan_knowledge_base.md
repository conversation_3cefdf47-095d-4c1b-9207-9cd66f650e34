# 原型设计规划: 可视化知识库浏览器 (Visualized Knowledge Base Explorer)

## **存在的问题**
1. 团队情报中心没定义原型
2. 企业上传文件空间对应四大库的话，在哪显示？
3. 目标市场在 vue 项目写地图
4. `干部学习`，`发起分析`，`设置`没有原型定义



## 1. 文档信息

| 项目 | 内容 |
| :--- | :--- |
| **功能名称** | 可视化知识库浏览器 (Visualized Knowledge Base Explorer) |
| **关联规格** | `prd/prd_v3.md` (P1 功能) |
| **版本** | 1.0 |
| **创建日期** | 2025年7月21日 |
| **状态** | **草稿** |

## 2. 设计目标

本原型旨在设计一个统一的、可视化的界面，让用户可以直观地探索、查询和管理四大动态知识库，使其成为产品的“企业智慧大脑”。

-   **信息可视化**: 将抽象的数据库转化为直观、易于理解的交互界面（如地图、卡片、文档）。
-   **无缝探索**: 用户可以在不同的知识库之间无缝跳转，追溯信息源头，发现隐藏的关联。
-   **学习环境**: 作为“干部学习系统”的核心载体，支持**基于产品线的**自主学习和认证追踪。
-   **人机协作**: 允许管理员对知识库内容进行手动补充和修正，**并支持用户上传、编辑自有知识，将其无缝融入知识库。**
-   **版本管理**: 追踪知识库内容的动态更新，提供版本回溯能力。
-   **增值服务入口**: 整合AI代理的增值服务入口，方便用户按需发起高级分析。

## 3. 原型设计范围

-   **主导航框架**: 用于在四大知识库之间切换。
-   **企业战略库界面**: 文档/报告型视图。
-   **目标市场数据库界面**: 交互式地图视图。
-   **公司数据库界面**: CRM式卡片视图。
-   **产品数据库界面**: 产品情报中心视图。
-   **用户内容上传与在线编辑**: 贯穿四大知识库的通用功能。

## 4. 关键设计点与思考

### 4.1. 主导航框架

-   **布局**: 在应用主界面的左侧设计一个常驻的、可折叠的侧边导航栏。位于左侧width20%。
-   **导航项**: 
    -   `团队情报中心` (首页)
    -   `知识库浏览器` (可展开)
        -   `企业战略库`
        -   `目标市场库`
        -   `公司库`
        -   `产品库`
    -   `干部学习`
    -   `发起分析`
    -   `设置`

### 4.2. 各知识库界面设计，内容位于右侧 flex1

#### 4.2.1. 企业战略库 (Corporate Wiki / Dashboard)
-   **布局**: 以文档和报告为中心的仪表盘视图。
-   **核心元素**: 
    -   一个可搜索、可筛选的列表，展示所有顶层战略分析报告（A0-A3），这些报告均以**AIP空间**的形式呈现，按年度更新。**对于干部学习系统，每个AIP空间内的“决策简报”是必读内容。**
    -   **T1情报集成**: T1情报流将作为**增量文档**持续补充到企业战略库中，丰富战略背景信息。
    -   **筛选器**: 顶部提供“核心产品线”筛选器和**“年度”筛选器**，允许用户按产品线和年度过滤报告。
    -   表格形式，列表项包含：`报告名称`, `创建者 (AI代理)`, `创建日期`, `核心洞察摘要`, `所属产品线`, `所属年度`。
    -   **视觉区分**: 在列表项中，对包含“决策简报”的AIP空间进行视觉高亮或添加特殊图标，提示其在学习系统中的重要性。（aip 空间有四个阶段，第三个阶段就是“决策简报”）
    -   点击列表项可直接跳转到对应的`AIP空间`。跳转到 aip_space.html
    -   **企业上传文件空间**: 
        -   **入口**: 在界面中提供“上传文件”按钮或拖拽区域。
        -   **功能**: 用户可上传`PDF`, `PPTX`, `Word`, `Excel`, `图片`等文件。
        -   **自动转换**: 系统自动将上传文件内容转换为Markdown格式，并提取关键信息。
        -   **在线编辑**: 用户可以在线编辑转换后的Markdown内容，进行补充、修正或格式调整。
        -   **内容属性**: 上传内容需强制标记“核心产品线”和“能力标签”，以便与干部学习系统集成。
        -   **视觉区分**: 上传文件将与AI产出的AIP空间进行视觉区分（如不同背景色、图标）。
    

#### 4.2.2. 目标市场数据库 (Interactive World Atlas)
-   **布局**: 以一个全屏的、可交互的世界地图为主体。
-   **核心交互**:
    -   地图上的每个国家/地区根据是否有数据进行高亮。
    -   **权限控制**: 用户只能看到其被分配权限的目标市场（国家/地区）。未授权的市场将显示为灰色或不可点击。
    -   **悬浮 (Hover)**: 显示该国家/地区名称和核心KPI（如：市场潜力评分）。
    -   **点击 (Click)**: 弹出一个该市场的“**概览卡片 (Overview Card)**”。
-   **市场概览卡片，考虑弹窗的形式，参考 task_delegation.html中的弹窗**:
    -   `国家名称`
    -   `核心数据`: 市场规模、增长率、竞争激烈度。
    -   `最新情报`: 显示由“市场情报官”推送的最新预警。
    -   `相关报告`: 列出所有与该市场相关的`AIP空间`链接（A4-A9），按年度更新，并可按产品线和年度筛选。**对于干部学习系统，每个AIP空间内的“决策简报”是必读内容。**
    -   **`活跃产品线`**: 显示该市场下当前活跃的核心产品线。
    -   **T2情报集成**: T2情报流将作为**增量文档**持续补充到目标市场数据库中，丰富市场细节信息。
    -   **视觉区分**: 在相关报告列表中，对包含“决策简报”的AIP空间进行视觉高亮或添加特殊图标。
    -   `[查看完整档案]` 按钮，点击后进入该市场的**专属详情页**。暂缓
    -   **企业上传文件空间**: 
        -   **入口**: 在市场详情页内提供“上传文件”按钮。
        -   **功能**: 用户可上传与该市场相关的研究报告、市场数据、本地化策略等文件。
        -   **自动转换与在线编辑**: 同企业战略库，支持自动转换为Markdown和在线编辑。
        -   **内容属性**: 上传内容需强制标记“核心产品线”和“能力标签”。
        -   **视觉区分**: 同企业战略库，与AI产出进行视觉区分。
    

#### 4.2.3. 公司/产品数据库 (CRM-style Hub)
-   **布局**: 采用可搜索、可筛选的卡片网格布局。
-   **筛选器**: 顶部提供“核心产品线”筛选器。
-   **卡片元素**:
    -   `公司/产品Logo`
    -   `公司/产品名称`
    -   `核心标签`: 如 `[竞争对手]`, `[合作伙伴]`, `[高增长]`。
    -   `一句话摘要`: “德国市场的领导者，近期有价格下调动作。”
    -   **`所属产品线`**。
    -   **T3/T4情报集成**: T3/T4情报流将作为**增量文档**持续补充到公司/产品档案中，丰富实体信息。
    -   **企业上传文件空间**: 
        -   **入口**: 在公司/产品详情页内提供“上传文件”按钮。
        -   **功能**: 用户可上传与该公司/产品相关的内部研究、销售数据、客户反馈等文件。
        -   **自动转换与在线编辑**: 同企业战略库，支持自动转换为Markdown和在线编辑。
        -   **内容属性**: 上传内容需强制标记“核心产品线”和“能力标签”。
        -   **视觉区分**: 同企业战略库，与AI产出进行视觉区分。
-   **交互**: 点击卡片进入该实体（公司/产品）的360度视图详情页，汇总所有相关信息。

### 4.3. 权限控制与内容可见性 (Permission Control & Content Visibility)

-   **核心机制**: 知识库内容的可见性与用户的AI代理分配权限以及**核心产品线**权限直接挂钩。
-   **实现方式**:
    -   当用户被分配给某个AI代理（如“全球战略官”或“市场拓展专家”）时，系统会自动赋予其查阅该代理所关联知识库的权限。
    -   **如果该权限与特定“核心产品线”关联，则用户将只能看到该知识库中与被授权产品线相关的内容。**
    -   在知识库浏览器中，用户将只能看到其拥有查阅权限的知识库（或知识库中的特定内容）。
    -   未授权的知识库或内容将显示为灰色、不可点击，或提示“无权限访问”。

### 4.4. 内容阅读状态与历史追踪 (Content Read Status & History Tracking)

-   **目的**: 记录用户的学习足迹，并作为“干部能力与学习系统”中能力认证的依据。
-   **实现方式**:
    -   **阅读状态**: 知识库中的每个可阅读内容（如AIP空间、情报预警、知识库条目、手动上传资料）都将有“未读”和“已读”两种状态。
        -   **视觉提示**: “未读”内容可以有醒目的标记（如蓝色圆点、加粗标题），“已读”内容则显示为普通状态。**对于AIP空间内的“决策简报”，将有更强的视觉提示（如特殊徽章、醒目边框），以强调其必读性。**
        -   **自动标记**: 用户打开并浏览内容达到一定时间或滚动到页面底部时，系统自动将其标记为“已读”。
    -   **阅读记录**: 系统将记录用户的阅读历史，包括：`内容名称`, `阅读时间`, `阅读时长`, `所属产品线`。**在阅读记录中，明确标识出“决策简报”的阅读完成情况。**

### 4.5. 与干部学习系统的集成 (Integration with Cadre Learning System)

-   **能力认证依据**: “干部能力与学习系统”将直接调用知识库的阅读状态和历史记录，来判断用户是否完成了某项能力的学习要求。
-   **学习路径**: 当用户被指派某项能力时，系统会根据该能力关联的知识库内容，在“我的学习任务”中生成一个待完成的阅读清单。**该清单将主要由相关AIP空间中的“决策简报”组成。** 用户在知识库浏览器中的阅读行为将直接更新该清单的完成状态。

## 5. 原型阶段与交付物

-   **阶段1: 线框图**: 为四大知识库的主界面，以及“市场概览卡片”、“公司卡片”等核心组件制作线框图。
-   **阶段2: 交互原型**: 创建一个可点击的中保真原型，重点模拟在不同知识库之间跳转、以及“引导式学习层”与主界面联动的交互过程。

### 4.6. 版本管理与动态更新 (Version Management & Dynamic Updates)

-   **核心机制**: 知识库内容的更新分为两种类型：年度报告更新和增量情报更新，版本管理策略也随之简化。
-   **年度报告版本管理 (AIP空间)**:
    -   **对象**: “企业战略库”中的A0-A3报告和“目标市场数据库”中的A4-A9报告（包括增值服务产出的AIP空间）。
    -   **更新频率**: 按年度更新，每次更新生成新的年度版本。
    -   **版本标识**: 主要通过“所属年度”进行区分，例如“2024年全球商机扫描报告”、“2025年德国市场进入策略”。
    -   **历史查看**: 用户可以通过“年度筛选器”查看不同年份的报告版本。
-   **增量情报更新 (T1-T4情报流)**:
    -   **对象**: T1-T4情报流直接补充到对应的知识库中，作为**增量文档或数据点**。
    -   **更新频率**: 持续、实时更新。
    -   **版本标识**: 不设独立版本号，而是作为知识库内容的最新状态体现。
    -   **回溯功能**: 用户可以通过时间线或日志查看历史增量情报。
-   **更新触发**: 
    -   **T1情报流**: 持续更新“企业战略库”中的增量情报。
    -   **T2情报流**: 持续更新“目标市场数据库”中的增量情报。
    -   **T3情报流**: 持续更新“公司数据库”中的公司档案增量情报。
    -   **T4情报流**: 持续更新“产品数据库”中的产品档案增量情报。
    -   年度报告的更新由AI代理在每年初根据最新数据生成。


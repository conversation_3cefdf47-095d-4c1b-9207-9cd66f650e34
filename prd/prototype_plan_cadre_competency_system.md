# 原型设计规划: 干部能力与学习系统 (Cadre Competency & Learning System)

## 1. 文档信息

| 项目 | 内容 |
| :--- | :--- |
| **功能名称** | 干部能力与学习系统 (Cadre Competency & Learning System) |
| **关联规格** | `prd/feature_specs_cadre_learning_system.md` (V2) |
| **版本** | 2.0 |
| **创建日期** | 2025年7月21日 |
| **状态** | **草稿** |

## 2. 设计愿景与目标

根据最新反馈，我们将“干部学习系统”从一个简单的“阅读指派工具”升级为一个战略性的“**能力发展平台**”。系统的核心不再是“指派文档”，而是“**培养和认证干部的核心能力**”。

-   **能力模型化**: 将模糊的干部能力要求，转化为一个清晰、可管理、可追踪的“能力地图”。
-   **知识体系化**: 打破AI产出和企业自有知识的壁垒，构建一个统一的、与能力挂钩的“学习资料库”。
-   **发展路径化**: 将被动学习转为主动发展，为干部提供清晰的能力提升路径和认证机制，激发成长动力。
-   **管理数据化**: 为管理者提供团队能力矩阵和学习分析，数据化地评估团队准备度和战略对齐水平。

## 3. 核心组件设计

### 3.1. 组件一：企业能力地图 (Corporate Competency Map)

这是整个系统的基石，是所有学习活动的“指挥中心”。

-   **形态**: 一个可视化的、可由管理员自定义的层级结构（树状或脑图状）。
-   **管理员能力**:
    1.  **定义能力域 (Domain)**: 管理员可以创建顶层能力域，例如 `市场洞察力`、`战略规划力`、`产品竞争力`、`组织领导力`。
    2.  **定义能力项 (Competency)**: 在每个域下，可以创建具体的能力项，并可将其与一个或多个**核心产品线**关联。例如，在`市场洞察力`下，可创建 `德国市场洞察`、`产品线A市场洞察` 等。
    3.  **关联知识库**: 每个能力项都可以（非必须）关联到一个或多个“四大动态知识库”的特定部分。例如，`德国市场洞察`能力项可以关联到“目标市场数据库 - 德国”。
-   **干部视图**: 每个干部都有一个个人能力地图，用不同的颜色或状态（如 `未开始`, `学习中`, `已认证`）标注自己每项能力的掌握情况。

### 3.2. 组件二：统一学习资料库 (Unified Learning Library)

这是与“能力地图”紧密关联的内容中心。

-   **形态**: 一个可筛选、可搜索的中央资料库。
-   **资料来源**:
    1.  **内部AI产出**: 系统内所有的 `AIP空间` 和 `情报预警` 都会自动进入资料库。
    2.  **外部手动上传**: 管理员可以手动上传 `PDF`, `PPTX`, `Word` 文档，或添加外部链接（文章、视频等）。
-   **核心交互：能力标签 (Competency Tagging)**
    -   **无论是AI产出还是手动上传的资料，都必须被管理员打上一个或多个来自“能力地图”的标签，并强制标记其所属的“核心产品线”**。**对于AIP空间，管理员还可以进一步标记其内部的“决策简报”为该能力项的“必读”内容。** 例如，一份关于“Bosch”的竞品分析报告（AIP空间）可以被打上 `德国市场洞察` 和 `产品线A` 两个标签。一份手动上传的《营销4P理论.pdf》可以被打上 `战略规划力` 标签，如果该理论适用于所有产品线，则可标记为 `通用` 或 `所有产品线`。

## 4. 核心用户流程

### 4.1. 管理者流程：从“指派文档”到“指派能力”

1.  **第一步：构建蓝图** - 管理员在“能力地图”编辑器中，定义公司的核心能力模型。
2.  **第二步：填充弹药** - 管理员在“学习资料库”中，为AI产出和手动上传的资料打上对应的能力标签。
3.  **第三步：指派任务** - 管理员的核心操作变为“**指派能力**”。例如，为新上任的欧洲总监指派 `德国市场洞察` 能力，如果该能力与特定产品线相关，则需同时指定产品线（如 `产品线A`）。系统将自动筛选出“统一学习资料库”中所有同时标记有该能力和产品线的资料，作为该干部的学习内容。
4.  **第四步：追踪评估** - 管理员在“学习统计”仪表盘中，查看团队整体的能力矩阵分布，以及每个干部的能力认证进度。

### 4.2. 干部流程：权限驱动的自主学习与认证 (Permission-Driven Self-Directed Learning & Certification)

1.  **接收任务**: 干部收到通知：“您已被指派学习 **[德国市场洞察]** 能力，请在X月X日前完成认证。”
2.  **获取权限**: 当干部被指派某项能力时，系统会自动赋予其访问该能力所需知识库内容的**查阅权限**。如果该能力与特定产品线关联，则权限将限定在该产品线范围内。例如，指派`德国市场洞察`能力（关联`产品线A`），则该干部将获得“目标市场数据库 - 德国”中`产品线A`相关内容的查阅权限。
3.  **自主探索学习**: 干部进入“可视化知识库浏览器”，在被授权的知识库范围内进行自主探索和学习。系统会通过**内容阅读状态追踪**（详见`prototype_plan_knowledge_base.md`）来记录干部的学习进度。
4.  **完成认证**: 当干部完成所有与该能力相关联的、被标记为“必读”的知识库内容（**特别是AIP空间中的“决策简报”**）的阅读，并提交“核心洞察”后，该干部个人“能力地图”上的对应能力项被点亮，状态变为“**已认证**”，获得一个可视化的徽章。

## 5. 界面元素 (UI/UX) 原型规划

-   **管理端**:
    -   **能力地图编辑器**: 提供拖拽、编辑、添加节点的交互，用于创建和维护能力地图，并支持为能力项关联“核心产品线”。（考虑使用https://github.com/Shopify/draggable或者其他的支持拖拽的，满足基本功能即可）
    -   **学习资料库管理界面**: 一个带强大筛选和标签管理功能的表格或卡片列表，**强制要求为每份资料标记所属的“核心产品线”**。
    -   **能力指派界面**: 一个以“用户-能力-产品线”矩阵为核心的界面，方便管理员为多个用户批量指派多项能力。
-   **干部端**:
    -   **我的能力地图**: 干部个人首页，可视化展示自己的能力掌握情况。
    -   **新版引导式学习界面**: 在“可视化知识库浏览器”之上运行的“学习路径”浮动层或侧边栏的设计。

## 6. 原型阶段与交付物

-   **阶段1: 概念与流程验证**: 绘制出“能力地图”和“学习资料库”的核心概念图，以及新的管理员和干部用户流程图。
-   **阶段2: 核心界面线框图**: 为“能力地图编辑器”、“资料库标签管理”、“我的能力地图”和“新版专注学习界面”制作低保真线框图。
-   **阶段3: 交互原型**: 制作一个中保真原型，完整模拟“管理员指派一项能力 -> 干部完成学习并获得认证”的核心闭环流程。


设计+流程简述：
## 管理员
### 组件一：企业能力地图 (Corporate Competency Map)
**能力地图编辑器**: 提供拖拽、编辑、添加节点的交互，用于创建和维护能力地图，并支持为能力项关联“核心产品线”。
1. 管理员
1.1 定义顶层能力域
1.2 定义能力项
1.3 关联知识库
### 组件二：统一学习资料库 (Unified Learning Library)
**学习资料库管理界面**: 一个带强大筛选和标签管理功能的表格或卡片列表，**强制要求为每份资料标记所属的“核心产品线”**。
1. 所有的 `AIP空间` 和 `情报预警`，手动上传 `PDF`, `PPTX`, `Word` 文档
2. 为每个资料打标签
3. 指派能力，为干部指派能力域+产品线，由此生成独立的`统一学习资料库`



## 学习干部
1. 显示出“管理员”配置好的能力地图，但每个干部独立学习状态
2. 获得指定的`统一学习资料库`
3. “可视化知识库浏览器”阅读并记录状态，每个能力项全部阅读更新干部“已认证”状态（需求暂缓）
# Foxu.AI 设计系统规范

## 1. 文档信息

| 项目 | 内容 |
| :--- | :--- |
| **文档名称** | Foxu.AI 设计系统规范 |
| **版本** | 1.0 |
| **创建日期** | 2025年7月22日 |
| **状态** | **草稿** |
| **负责人** | 设计团队 |

## 2. 设计理念

### 2.1 核心价值观
- **专业可信**: 体现企业级产品的专业性和可靠性
- **智能高效**: 通过设计传达AI驱动的高效决策能力
- **简洁易用**: 降低学习成本，提升用户体验
- **国际化**: 支持全球化企业的多元化需求

### 2.2 设计原则
- **一致性**: 统一的视觉语言和交互模式
- **可访问性**: 符合WCAG 2.1 AA标准
- **响应式**: 适配多种设备和屏幕尺寸
- **性能优先**: 优化加载速度和交互响应

## 3. 视觉设计规范

### 3.1 色彩系统

#### 主色调
```css
:root {
  /* 品牌主色 - 智能蓝 */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-500: #3b82f6;  /* 主要使用 */
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-900: #1e3a8a;
  
  /* 辅助色 - 专业紫 */
  --secondary-500: #8b5cf6;
  --secondary-600: #7c3aed;
  
  /* 成功色 - 增长绿 */
  --success-500: #10b981;
  --success-600: #059669;
  
  /* 警告色 */
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  
  /* 错误色 */
  --error-500: #ef4444;
  --error-600: #dc2626;
}
```

#### 中性色
```css
:root {
  /* 文字颜色 */
  --gray-900: #111827;  /* 主要文字 */
  --gray-700: #374151;  /* 次要文字 */
  --gray-500: #6b7280;  /* 辅助文字 */
  --gray-400: #9ca3af;  /* 占位符 */
  
  /* 背景颜色 */
  --gray-50: #f9fafb;   /* 页面背景 */
  --gray-100: #f3f4f6;  /* 卡片背景 */
  --gray-200: #e5e7eb;  /* 分割线 */
}
```

### 3.2 字体系统

#### 字体族
```css
:root {
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
}
```

#### 字体大小
```css
:root {
  --text-xs: 0.75rem;    /* 12px - 标签、辅助信息 */
  --text-sm: 0.875rem;   /* 14px - 正文、表单 */
  --text-base: 1rem;     /* 16px - 基础文字 */
  --text-lg: 1.125rem;   /* 18px - 小标题 */
  --text-xl: 1.25rem;    /* 20px - 卡片标题 */
  --text-2xl: 1.5rem;    /* 24px - 页面标题 */
  --text-3xl: 1.875rem;  /* 30px - 主标题 */
  --text-4xl: 2.25rem;   /* 36px - 大标题 */
}
```

#### 字重
```css
:root {
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
}
```

### 3.3 间距系统

#### 基础间距
```css
:root {
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
}
```

### 3.4 圆角系统
```css
:root {
  --radius-sm: 0.25rem;   /* 4px - 小元素 */
  --radius-md: 0.5rem;    /* 8px - 按钮、输入框 */
  --radius-lg: 0.75rem;   /* 12px - 卡片 */
  --radius-xl: 1rem;      /* 16px - 大卡片 */
  --radius-2xl: 1.5rem;   /* 24px - 容器 */
  --radius-full: 9999px;  /* 圆形 */
}
```

### 3.5 阴影系统
```css
:root {
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}
```

## 4. 组件设计规范

### 4.1 按钮组件

#### 主要按钮
```css
.btn-primary {
  background: var(--primary-500);
  color: white;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: var(--primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}
```

#### 次要按钮
```css
.btn-secondary {
  background: white;
  color: var(--primary-500);
  border: 1px solid var(--primary-500);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: var(--primary-50);
  transform: translateY(-1px);
}
```

### 4.2 卡片组件

#### 基础卡片
```css
.card {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}
```

#### AI代理卡片
```css
.agent-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
}

.agent-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,...') /* 纹理图案 */;
  opacity: 0.1;
  pointer-events: none;
}
```

### 4.3 表单组件

#### 输入框
```css
.input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  transition: all 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
```

#### 选择器
```css
.select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E");
  background-position: right var(--space-3) center;
  background-repeat: no-repeat;
  background-size: 1rem;
  padding-right: var(--space-10);
}
```

## 5. 动画与交互规范

### 5.1 过渡动画
```css
:root {
  --transition-fast: 0.15s ease;
  --transition-base: 0.2s ease;
  --transition-slow: 0.3s ease;
  --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}
```

### 5.2 关键帧动画
```css
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
```

### 5.3 状态指示器
```css
.status-online {
  position: relative;
  background: var(--success-500);
  border-radius: var(--radius-full);
}

.status-online::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: var(--radius-full);
  background: var(--success-500);
  animation: pulse-online 2s infinite;
}

@keyframes pulse-online {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.8);
    opacity: 0;
  }
}
```

## 6. 响应式设计规范

### 6.1 断点系统
```css
:root {
  --breakpoint-xs: 480px;   /* 手机 */
  --breakpoint-sm: 768px;   /* 平板 */
  --breakpoint-md: 1024px;  /* 小桌面 */
  --breakpoint-lg: 1200px;  /* 大桌面 */
  --breakpoint-xl: 1440px;  /* 超大屏 */
}
```

### 6.2 容器规范
```css
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--space-6);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--space-8);
  }
}
```

### 6.3 网格系统
```css
.grid {
  display: grid;
  gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }
}
```

## 7. 可访问性规范

### 7.1 颜色对比度
- 正文文字与背景对比度 ≥ 4.5:1
- 大文字（18px+）与背景对比度 ≥ 3:1
- 非文字元素与背景对比度 ≥ 3:1

### 7.2 焦点管理
```css
.focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
```

### 7.3 语义化标记
- 使用正确的HTML语义标签
- 为图片提供alt属性
- 为表单元素提供label
- 使用ARIA属性增强可访问性

## 8. 性能优化规范

### 8.1 CSS优化
- 使用CSS变量减少重复
- 避免深层嵌套选择器
- 使用transform和opacity进行动画
- 合理使用will-change属性

### 8.2 图片优化
- 使用WebP格式
- 提供多种尺寸的响应式图片
- 使用懒加载
- 优化SVG图标

### 8.3 字体优化
- 使用font-display: swap
- 预加载关键字体
- 使用系统字体作为fallback

## 9. 组件库结构

### 9.1 文件组织
```
components/
├── base/           # 基础组件
│   ├── Button/
│   ├── Input/
│   └── Card/
├── layout/         # 布局组件
│   ├── Header/
│   ├── Sidebar/
│   └── Container/
├── business/       # 业务组件
│   ├── AgentCard/
│   ├── TaskPanel/
│   └── ChatModal/
└── utils/          # 工具样式
    ├── animations.css
    ├── utilities.css
    └── responsive.css
```

### 9.2 命名规范
- 使用BEM命名方法论
- 组件名使用PascalCase
- CSS类名使用kebab-case
- 变量名使用camelCase

## 10. 品牌元素规范

### 10.1 Logo使用
- 最小尺寸：24px高度
- 安全区域：Logo周围至少1倍Logo高度的空白
- 颜色变体：全彩、单色、反白

### 10.2 图标系统
- 使用24px网格系统
- 线条粗细：1.5px
- 圆角：2px
- 风格：线性图标为主

### 10.3 插画风格
- 扁平化设计
- 渐变色彩
- 几何形状
- 科技感元素

---

**注意**: 本设计系统规范应与开发团队密切配合，确保设计与实现的一致性。建议定期review和更新规范内容。

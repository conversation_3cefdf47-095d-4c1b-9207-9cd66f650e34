# 功能规格说明: 干部学习系统 (Cadre Learning System)

## 1. 文档信息

| 项目 | 内容 |
| :--- | :--- |
| **功能名称** | 干部学习系统 (Cadre Learning System) |
| **关联PRD** | `prd_v3.md` (P2优先级功能) |
| **版本** | 1.0 |
| **创建日期** | 2025年7月21日 |
| **状态** | **草稿** |

## 2. 功能概述

干部学习系统是一个独立的、严肃的战略对齐工具。它通过两种方式确保公司的核心干部能深入、完整地理解关键战略情报：

1.  **自动化入职认证**: 当一位干部被新指派给某个AI代理（如“市场拓展专家”）时，系统会**自动为其创建一份“必读认证简报”**，内容涵盖该岗位所需的核心知识。干部必须完成学习后，才能获得正式的“任职认证”。
2.  **手动指派简报**: 管理者可以随时从**AIP空间**或**情报预警**中手动选择内容，将其打包成“必读简报”，用于临时的、特定的战略宣贯。

## 3. 用户故事

| 角色 | 用户故事 |
| :--- | :--- |
| **CEO/管理者** | 当我任命一位新的欧洲市场负责人，并将其权限关联到“市场拓展专家（欧洲）”时，我需要系统**自动地**为他指派一份包含所有欧洲市场核心报告的“入职认证简报”，并让我能追踪他的**认证状态**。 |
| **CEO/管理者** | 作为CEO，当我任命一位新的区域负责人时，我需要一个正式的流程，将关于该区域的所有**AIP空间**打包成一份**必读简报**，并指派给他，以确保他能快速、全面地进入状态。 |
| **CEO/管理者** | 作为管理者，在指派一份必读简报后，我希望能设定一个**完成期限**，并能方便地追踪这位干部的**学习进度**（是否已读完）以及他最终提交的**核心洞察**，以确认他是否真正抓住了要点。 |
| **核心干部** | 作为一名市场总监，当我接到一份关于新产品上市的“必读简报”时，我希望在一个**无干扰的、专注的界面**中学习所有材料，而不是在日常的信息流中被其他信息打断。 |
| **核心干部** | 作为一名干部，在学习完所有指派的材料后，我希望能有一个地方，让我郑重地写下我对本次任务的**核心理解和承诺**，并提交给我的上级，完成这次重要的信息同步。 |

## 4. 交互流程 (UX Flow)

### 4.1. 自动化入职与认证流程 (Automated Onboarding & Certification Flow)

这是系统的核心自动化流程，与“企业成员与权限管理”功能深度联动。

1.  **触发 (Trigger)**: 在“企业成员与权限管理”模块中，管理员将一位用户（如“张三”）加入到某个AI代理的协作范围（例如：“市场拓展专家 - 德国”）。
2.  **自动创建与指派 (Automatic Creation & Assignment)**: 系统检测到这次新的权限分配，自动为张三创建一份名为“**德国市场负责人入职认证**”的必读简报。
3.  **智能内容打包 (Intelligent Content Packaging)**: 系统自动从该AI代理关联的知识库（“目标市场数据库 - 德国”）中，筛选出所有P0级别的核心文档、最新的分析报告和最重要的预警，打包进这份认证简报中。
4.  **干部学习与认证 (Cadre Learning & Certification)**: 
    -   张三在“团队情报中心”和“我的学习任务”中收到这份必读任务。
    -   他必须在“专注学习界面”中完成所有材料的学习，并提交“核心洞察”。
5.  **状态更新 (Status Update)**: 
    -   一旦张三提交洞察，系统自动将其在该岗位的状态更新为“**已认证**”。
    -   管理员可以在“企业成员管理”界面看到张三头像旁的认证徽章，也可以在“干部学习系统”管理面板中看到该认证任务的完成状态。

### 4.2. 手动指派流程 (Manual Assignment Flow)

1.  **进入管理后台**: 管理者从主导航进入“干部学习系统”的管理面板。
2.  **创建简报包**: 点击“创建新的简报包”按钮。
3.  **选择内容**: 系统弹出一个界面，让管理者可以从**所有AI代理的产出（即AIP空间和情报预警）**中，选择一个或多个内容加入到简报包中。界面应提供强大的搜索和筛选功能，允许管理者按`关键字`、`AI代理`、`知识库`、`预警级别`等多个维度，快速定位到所需材料。
    - *示例：管理者可以筛选出“市场情报官”发布的所有“红色警报”，以及“全球战略官”发布的关于“XX市场”的所有分析报告，然后将它们一起打包。*
4.  **配置与指派**: (流程不变)
5.  **追踪进度**: (流程不变)
6.  **复盘学习效果 (Review Learning Analytics)**: 管理者可以进入“学习统计”面板，查看团队整体的学习情况，识别出哪些情报最受关注，哪些干部是学习标兵，从而评估战略内容的触达效果。

### 4.2. 干部端 (Cadre Flow)

(流程无变化)

## 5. 界面元素 (UI Components)

### 5.1. 管理面板 (Admin Dashboard)
- (设计不变)

### 5.2. **内容选择器 (Content Selector UI)**
-   这是创建简报包时的核心界面。
-   应提供强大的搜索和筛选功能，允许管理者按`关键字`、`AI代理`、`知识库`、`预警级别`等多个维度，快速定位到需要指派的**AIP空间**。

### 5.3. 专注学习界面 (Focus Mode UI)
- (设计不变)

### 5.4. 核心洞察提交表单 (Takeaway Submission Form)
- (设计不变)

### 5.5. 学习统计与分析仪表盘 (Learning Analytics Dashboard)

-   **入口**: 在“干部学习系统”的管理面板顶部，提供一个“学习统计”的Tab或入口。
-   **核心指标卡片**: 顶部区域展示关键KPIs。
    -   `总学习任务数`
    -   `平均完成率`
    -   `最受欢迎的情报` (被指派次数最多的AIP空间或预警)
    -   `学习关键贡献者` (完成度最高、最快的干部)
-   **图表一：团队学习进度概览**
    -   **形式**: 一个堆叠条形图（Stacked Bar Chart）。
    -   **X轴**: 每一项是一个“必读简报包”。
    -   **Y轴**: 人数。
    -   **数据**: 每个条形图内部由“已完成”、“进行中”、“未开始”三种颜色构成，直观展示每个任务的整体进度。
-   **图表二：热门学习内容Top 5**
    -   **形式**: 水平条形图（Horizontal Bar Chart）。
    -   **数据**: 展示被指派次数最多、或在学习界面中平均停留时间最长的5个情报（AIP空间/预警）。
-   **表格：干部学习详情**
    -   **形式**: 一个可搜索、可排序的表格。
    -   **列**: `干部姓名`, `已指派任务数`, `已完成任务数`, `平均完成时间`, `操作（查看详情）`。
    -   **交互**: 点击“查看详情”可以钻取到单个干部的所有学习记录和他们提交的“核心洞察”。

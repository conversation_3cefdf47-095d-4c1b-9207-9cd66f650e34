# 功能规格说明: 团队情报中心 (Team Intelligence Hub)

## 1. 文档信息

| 项目 | 内容 |
| :--- | :--- |
| **功能名称** | 团队情报中心 (Team Intelligence Hub) |
| **关联PRD** | `prd_v3.md` (P1优先级功能) |
| **版本** | 1.2 |
| **更新日期** | 2025年7月21日 |
| **状态** | **草稿** |

## 2. 功能概述

团队情报中心是一个中心化的信息流，旨在将**三大AI代理**的产出（包括预警和**AIP空间**）与团队成员自发的知识分享和讨论相结合。它是一个建立在公司核心数据架构之上的、激发好奇心、促进战略共识、培养学习型组织文化的协作空间。

## 3. 用户故事

| 角色 | 用户故事 |
| :--- | :--- |
| **所有高管** | 作为一名高管，我希望能在一个地方看到由**市场情报官、全球战略官和市场拓展专家**分别发布的最新情报，以便我能快速跟上公司的战略思考节奏。 |
| **CEO** | 作为CEO，我今天只想看“全球战略官”产出的深度分析报告，我希望能**一键筛选**，只看这个AI代理的产出。 |
| **市场总监** | 作为市场总监，我只关心“市场情报官”发布的预警，我希望能方便地**过滤**出这些信息。 |
| **所有高管** | 作为一名高管，当我看到一份报告中有一张特别有价值的图表时，我希望能**单独收藏这张图表**，而不是收藏整个报告，方便我以后快速找到它。 |
| **所有高管** | 作为一名高管，当我看到同事**点赞或收藏了**某个报告中的某个数据模型时，我希望能在信息流里看到这个动态，这能帮助我发现自己可能忽略掉的重要信息。 |
| **所有高管** | 作为一名高管，当我分享一个外部链接时，我希望系统能**自动帮我处理**：如果已经有人分享过，就提示我；如果是新的，就自动帮我把它转换成格式统一的内部文档，让分享更高效、更专业。 |
| **CEO/管理员** | 作为CEO，我希望能看到情报中心的活跃度统计，比如谁是贡献最多的“情报专家”，哪个**知识库**或**具体附件**最能引发团队讨论，以便我了解团队的关注点和学习氛围。 |

## 4. 交互流程 (UX Flow)

1.  **进入与浏览**: 用户进入“团队情报中心”，浏览按时间倒序的、包含各类事件的无限滚动信息流。
2.  **按AI代理/知识库筛选**: 在信息流的顶部，提供两组筛选器。
    - **按AI代理**: `[全部]`, `[市场情报官]`, `[全球战略官]`, `[市场拓展专家]`。
    - **按知识库**: `[全部]`, `[企业战略库]`, `[目标市场库]`, `[产品库]`, `[公司库]`。
3.  **与内容互动**:
    -   用户看到一个“AIP空间”卡片，可以直接点击卡片上的“播放播客”、“打开演示”等按钮进行交互。**（详细交互参见 `feature_specs_decision_war_room.md`）**
    -   在“AIP空间”独立页面，**每一个组件**（如来源、报告、图表、数据模型、PPT）都支持独立的 `[👍 点赞]` 和 `[⭐ 收藏]` 按钮。用户的点赞/收藏操作，会**生成一条新的事件**，发布到团队情报中心的信息流中。
4.  **发起智能分享**: (流程不变)
5.  **参与讨论**: (流程不变)

## 5. 界面元素 (UI Components)

### 5.1. 主信息流 (Main Feed)
-   信息流现在包含**三种主要卡片类型**: “AIP空间”卡片、“用户分享”卡片、以及“用户活动”卡片。

### 5.2. 筛选器 (Filter Bar)
-   位于页面顶部，提供“按AI代理”和“按知识库”两组筛选按钮。

### 5.3. “AIP空间”卡片 (Artifact Workspace Card)
-   **AI代理署名**: 在卡片标题的顶部，必须有清晰的署名，标明其来源，如 `发布者: 市场情报官`。
-   **来源知识库标签**: (保持不变)
-   **核心交互**: 卡片上应包含“总览仪表盘”的核心元素：一个内嵌的、极简的 **音频播放器** 用于播放播客，以及 `[🖥️ 打开演示]`, `[📂 查看AIP空间]` 等操作按钮。**（详细设计参见 `feature_specs_decision_war_room.md`）**

### 5.4. “用户分享”卡片 (User Contribution Card)
-   (设计不变)

### 5.5. “用户活动”卡片 (User Activity Card)
-   (设计不变)

### 5.6. “AIP空间”独立页面
-   这是点击`[📂 查看AIP空间]`后进入的页面。**（详细设计参见 `feature_specs_decision_war_room.md`）**

# 原型设计规划: AIP空间 (Artifact Workspace)

## 1. 文档信息

| 项目 | 内容 |
| :--- | :--- |
| **功能名称** | AIP空间 (Artifact Workspace) |
| **关联规格** | `prd/feature_specs_aip_space.md` |
| **版本** | 1.1 |
| **创建日期** | 2025年7月21日 |
| **状态** | **草稿** |
| **设计负责人** | (待定) |

## 2. 设计目标

本原型设计旨在将“AIP空间”的功能规格转化为直观、易用、高效的用户界面。核心目标是：

-   **清晰呈现决策流水线**: 用户能一目了然地理解从“调研”到“分析”再到“决策落地”的全过程。
-   **高效信息消费**: 用户能快速获取核心信息（播客、仪表盘），并能便捷地深入细节（各站内容）。
-   **促进协作与决策**: 界面设计应鼓励用户参与“决策清单”的填写和讨论，并支持管理员高效管理决策过程。
-   **统一视觉体验**: 与Foxu.AI整体产品风格保持一致。

## 3. 原型设计范围

本次原型设计将涵盖“AIP空间”的以下核心视图和交互流程：

-   **AIP空间总览页**: 包含总览仪表盘、播客播放器、决策流水线概览。
-   **各站详情页**: “调研成果”、“分析产出”、“决策简报”、“决策落地”的独立展示区域。
-   **决策清单问卷交互**: 会前填写、会中结果展示、会后管理员填写决议的流程。
-   **原子化交互**: 点赞、收藏功能的视觉呈现和交互反馈。

## 4. 关键设计点与思考

### 4.1. 总览仪表盘 (Workspace Dashboard) - 详细设计

采纳您的建议，我们取消浮动设计，将播客播放器与核心指标整合到同一行，以实现更紧凑和一体化的布局。

-   **1. 顶部核心信息区 (Top Information Row)**
    -   **整体布局**: 在全局导航栏下方，设计为单一的水平行（Horizontal Row）。该行从左到右依次包含 **播客播放器卡片** 和 **三个核心指标卡片**。
    -   **播客播放器卡片 (Player Card)**:
        -   **定位**: 位于该行的最左侧，占据约30%的宽度。
        -   **设计**: 为了适应行内布局，播放器将设计得更为紧凑。
            -   **顶部**: 显示播客标题。
            -   **中部**: 简洁的播放/暂停按钮和一条紧凑的线性进度条。
            -   **底部**: `当前时间 / 总时长` 的文本显示。
    -   **核心指标卡片 (Metric Cards)**:
        -   **定位**: 紧跟在播客播放器卡片右侧，均分剩余的行空间。
        -   **卡片一：调研成果**:
            -   **主数据**: 来源文件总数 (例如 `15 份`)。
            -   **辅助图表**: 甜甜圈图，显示**可信度**分布。
        -   **卡片二：分析产出**:
            -   **主数据**: 已生成报告/图表总数。
            -   **辅助信息**: 最新产出的文件名。
        -   **卡片三：决策清单状态**:
            -   **主数据**: 当前状态 (如 `会前填写中`)。
            -   **辅助图表**: 进度条，显示填写进度 (例如 `8/10 人`)。

-   **2. 决策流水线概览 (Decision Pipeline)**
    -   **布局**: 位于顶部核心信息区的下方，采用水平时间轴或步骤条（Stepper）的形式，清晰地可视化从左到右的流程。
    -   **阶段节点 (Stage Node)**: 每个阶段（调研、分析、决策、落地）都是一个可点击的节点，包含状态和核心信息，点击可跳转至对应的详情页Tab。

### 4.2. 各站详情页 (优化版：Tab导航结构)

为确保页面结构清晰，并兼容内部复杂的布局（如左右分栏），我们采纳您的建议，采用**顶部Tab导航**作为核心交互框架。整个详情页将由一个Tab容器和四个独立的Tab页面组成，每个Tab的设计如下：

-   **Tab 1: 调研成果**
    -   **Tab标签设计**: 标签上可以增加一个数字角标，实时显示来源文件的总数，例如 `调研成果 (5)`。
    -   **内部布局**: 采用**左右布局**。
        -   **左侧**: 显示一个可展开的**树状文件列表**，初始状态为一个 `root` 根节点。
        -   **交互流程**: 点击 `root` 节点后，右侧展开一个包含所有文件的**文件表格**，同时左侧 `root` 节点下树状展开**文件编号列表**（因文件名可能过长）。用户可点击左侧的文件编号，右侧表格会高亮对应行，并可在下方区域预览文件详情。
        -   **右侧 (文件表格)**: 表格包含以下列：`文件编号`, `文件名`, `文件发布机构`, `搜集时间`, `可信度`, `相关性评估`, `文件来源`, `文件字数`, `阅读时间`。

-   **Tab 2: 分析产出**
    -   **Tab标签设计**: 标签上可使用文本标签（Tag）注明核心产出的类型，例如 `分析产出 [报告]`。
    -   **内部布局**: 采用**左右布局**。
        -   **左侧**: 固定显示三个分类：`报告`, `图片`, `Excel`。
        -   **交互流程**:
            -   点击 `报告` 或 `Excel` 分类：右侧显示该分类下所有文件的**文件表格**，同时左侧对应分类下树状展开**文件编号列表**。
            -   点击 `图片` 分类：右侧以**缩略图网格**形式展示所有图片，点击任意图片可全屏查看。
        -   **右侧 (文件表格)**: `报告`和`Excel`分类的表格包含以下列：`文件编号`, `文件名`, `文件生成时间`, `文件字数`, `阅读时长`。

-   **Tab 3: 决策简报**
    -   **Tab标签设计**: 标签可以保持简洁，直接显示 `决策简报`。
    -   **内部布局**: 此Tab页内容为**PPT嵌入式预览**，布局相对单一，以内容为中心，支持翻页和全屏演示。

-   **Tab 4: 决策清单**
    -   **Tab标签设计**: 标签上应用**状态标签**来动态提示当前进度，例如 `决策清单 (会前填写中)` 或 `决策清单 (决议已定)`。
    -   **内部布局**: 此Tab页内容为动态的**决策清单**，根据不同阶段（会前、会中、会后）呈现不同视图，具体设计如下：
        -   **核心理念**: 动态视图，聚焦当前阶段任务。
        -   **顶部区域：清单概览与状态控制**:
            -   **清单标题**: 醒目显示本次决策清单的主题。
            -   **状态指示**: 清晰显示当前所处阶段（例如：“会前填写中”、“会中讨论”、“决议已定”）。
            -   **管理员控制区 (仅管理员可见)**: 按钮：`[启动会前填写]`、`[进入会中讨论]`、`[完成决议]`。进度条/统计：显示当前已填写人数/比例。
        -   **阶段一：会前填写 (Participant View)**:
            -   **布局**: 简洁的单列问卷形式，一次显示一个或一组问题，避免信息过载。
            -   **问题呈现**: 清晰的单选按钮或多选框，直观的滑块、星级评分或数字选择器。
            -   **特点**: 在此阶段，参与者**看不到任何其他人的填写结果**，确保独立思考。
        -   **阶段二：会中讨论 (Presentation View)**:
            -   **布局**: 强调数据可视化和讨论空间。可采用**左右分栏**或**上下分块**布局。
            -   **问题与结果可视化**: 突出显示当前讨论的问题。实时更新的图表（饼图、柱状图、平均分），清晰展示各选项的比例或得分。
            -   **匿名/实名切换**: 在图表区域附近，提供一个醒目的 `[匿名显示]` / `[显示姓名]` 切换按钮。
            -   **讨论区**: 与“团队情报中心”的评论区类似，支持实时评论、@同事、点赞等。
        -   **阶段三：会后决议 (Administrator View)**:
            -   **布局**: 结合了结果展示和管理员输入。
            -   **问题呈现**: 在每个问题下方，清晰显示**会前所有人的选择比例或平均分**（图表或文字总结）。
            -   **最终决议输入框**: 管理员专属的文本输入框或选择器，用于记录会议最终达成的决议。

### 4.3. 原子化交互 (点赞/收藏)
-   在每个可交互组件（来源、报告、图表、数据模型、PPT、决策清单）旁，提供统一的图标（如👍⭐）。
-   点击后有即时视觉反馈（如图标变色、计数增加）。

### 4.4. 分享功能 (Share Functionality)

为了方便用户将AIP空间的工作成果分享给外部协作者或管理者，我们设计了基于设备类型提供不同访问权限的分享功能。

-   **1. 分享入口 (Share Entry Point)**
    -   **位置**: 在AIP空间主界面的顶部标题栏，放置一个清晰的“分享”按钮。
    -   **操作**: 点击“分享”按钮后，弹出一个模态框。

-   **2. 分享操作 (Sharing Action)**
    -   **模态框内容**: 模态框中会生成一个唯一的、可复制的分享链接。
    -   **便捷操作**: 提供一个“复制链接”的按钮，方便用户一键获取。

-   **3. 访问体验 (Recipient Experience)**
    -   系统会自动检测访问者所使用的设备类型，并呈现相应视图：
    -   **桌面端访问 (Desktop Access)**:
        -   **权限**: 访问者将获得对整个AIP空间的**只读权限**。
        -   **内容**: 可以查看完整的“总览仪表盘”以及所有“各站详情页”（调研成果、分析产出、决策简报、决策清单）的全部内容。所有交互（如点击Tab、展开列表）均可用，但无法进行编辑、评论或修改操作。
    -   **移动端访问 (Mobile Access)**:
        -   **权限**: 访问者将进入一个**移动端优化的只读视图**。
        -   **内容**: 考虑到播客是移动端的核心消费内容，此视图将进行优化，**顶部将重点突出播客播放器**。播放器下方，依次展示“总览仪表盘”的核心内容，包括**关键指标卡片**和**决策流水线概览**。为保证移动端体验的简洁性，将**禁用**从仪表盘跳转至“各站详情页”的链接。

## 5. 原型阶段与交付物

### 4.5. 全局顶部导航栏 (Global Header)

为了提供一致的品牌识别和核心导航功能，所有AIP空间页面共享一个全局顶部导航栏。

-   **1. 布局 (Layout)**
    -   采用标准的三段式布局：左、中、右。
    -   **左侧**: Foxu.AI 的产品Logo。**交互**: 点击Logo可直接返回应用首页。
    -   **中间**: **Artifact（工作空间）名称**。使用醒目的字体清晰展示当前工作空间的标题，帮助用户定位。
    -   **右侧**: 功能与用户信息区，从左到右依次排列：
        -   `分享` 按钮 (详见 4.4. 分享功能)。
        -   `用户头像` 或 `登录` 按钮。

-   **2. 用户状态 (User States)**
    -   **已登录用户 (Logged-in User)**:
        -   右侧显示用户的**圆形头像**。
        -   **交互**: 点击头像后，出现一个下拉菜单，提供 `我的主页`、`设置`、`退出登录` 等选项。
    -   **匿名用户 (Anonymous/Guest User)**:
        -   此状态适用于通过分享链接访问的用户。
        -   右侧将不显示用户头像，而是显示一个 `登录/注册` 按钮。
        -   **交互**: 点击该按钮会将用户引导至应用的登录或注册页面。

-   **阶段1: 低保真线框图 (Low-Fidelity Wireframes)**
    -   **目标**: 快速验证核心布局和信息架构。
    -   **交付物**: 主要页面（总览页、各站详情页）的线框图。
-   **阶段2: 中保真原型 (Mid-Fidelity Prototypes)**
    -   **目标**: 细化交互流程，验证用户路径。
    -   **交付物**: 包含关键交互（如决策清单填写、结果展示）的原型。
-   **阶段3: 高保真UI设计 (High-Fidelity UI Design)**
    -   **目标**: 确定最终视觉风格和细节。
    -   **交付物**: 完整的UI设计稿，包含设计规范。

## 6. 待讨论事项

-   决策清单的问卷题目需要AI根据分析内容自动生成。
-   会中展示结果时，需要支持匿名/实名切换。

期待与设计团队的进一步沟通，共同打造卓越的用户体验。

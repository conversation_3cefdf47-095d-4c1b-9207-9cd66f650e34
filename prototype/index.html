<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Foxu AI Team - 我的AI团队</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="custom.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        /* 隐藏滚动条但保持功能 */
        .scrollbar-hide {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;  /* Chrome, Safari and Opera */
        }

        /* 员工卡片动画 */
        .group:hover .group-hover\:scale-110 {
            transform: scale(1.1);
        }

        /* 滚动按钮过渡效果 */
        #scrollLeft, #scrollRight {
            transition: opacity 0.3s ease;
        }

        /* 员工卡片选中状态 */
        [data-employee].border-2 {
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
        }

        /* 统计卡片样式 */
        .stat-card {
            @apply bg-white p-6 rounded-2xl border border-gray-100 hover:shadow-lg transition-all duration-300;
        }

        /* 图表容器样式 */
        .chart-container {
            @apply bg-white p-6 rounded-2xl border border-gray-100 hover:shadow-lg transition-all duration-300;
            height: 400px;
        }
    </style>
</head>
<body>
    <header class="header sticky top-0 z-50 bg-white/80 backdrop-blur-xl border-b border-gray-100 px-4 rounded-b-3xl shadow-lg">
        <div class="header-container max-w-[1600px] mx-auto flex justify-between items-center h-16">
            <div class="logo">
                <a href="index.html" class="text-2xl font-black bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent hover:from-purple-600 hover:to-blue-600 transition-all duration-300 cursor-pointer">Foxu AI Team</a>
            </div>
            <nav class="nav flex items-center gap-8">
                <div class="user-menu flex items-center gap-3 relative">
                    <div class="user-status flex items-center gap-2">
                        <div class="status-indicator online w-2 h-2 bg-emerald-500 rounded-full relative animate-pulse"></div>
                        <span class="user-name text-gray-700 font-semibold text-sm">David Chen</span>
                    </div>
                    <div class="notification-badge w-6 h-6 bg-gradient-to-r from-emerald-500 to-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold cursor-pointer hover:scale-110 transition-transform shadow-md border-2 border-white" id="notificationBadge">3</div>
                    <div class="user-dropdown relative">
                        <button class="user-dropdown-btn flex items-center gap-2 p-2 hover:bg-gray-100 rounded-lg transition-all" id="userDropdownBtn">
                            <img src="assets/avatars/david-chen.svg" alt="用户头像" class="user-avatar-small w-8 h-8 rounded-full border-2 border-blue-200">
                            <span class="dropdown-arrow text-xs text-gray-500 transition-transform">▼</span>
                        </button>
                        <div class="user-dropdown-menu" id="userDropdownMenu">
                            <div class="dropdown-header">
                                <img src="assets/avatars/david-chen.svg" alt="用户头像" class="user-avatar-menu">
                                <div class="user-info">
                                    <span class="user-name-menu">David Chen</span>
                                    <span class="user-email"><EMAIL></span>
                                </div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <a href="#" class="dropdown-item">
                                <span class="item-icon">👤</span>
                                个人资料
                            </a>
                            <a href="#" class="dropdown-item">
                                <span class="item-icon">⚙️</span>
                                账户设置
                            </a>
                            <a href="#" class="dropdown-item">
                                <span class="item-icon">💳</span>
                                订阅管理
                            </a>
                            <a href="#" class="dropdown-item">
                                <span class="item-icon">📊</span>
                                使用统计
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="#" class="dropdown-item">
                                <span class="item-icon">❓</span>
                                帮助中心
                            </a>
                            <a href="login.html" class="dropdown-item text-red">
                                <span class="item-icon">🚪</span>
                                退出登录
                            </a>
                        </div>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <main class="main-content max-w-[1600px] mx-auto px-4 py-4">
        <!-- 团队成员卡片轮播区域 -->
        <section class="team-members mb-6">
            <h3 class="text-2xl font-bold text-gray-800 mb-4">您的24/7全球市场部</h3>
            <div class="relative">
                <!-- 左侧滚动按钮 -->
                <button class="absolute left-0 top-1/2 -translate-y-1/2 z-10 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all" id="scrollLeft">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </button>
                
                <!-- 卡片滚动区域 -->
                <div class="flex overflow-x-auto scrollbar-hide py-3 gap-4 px-10" id="teamScroll">
                    <!-- Alex卡片 -->
                    <div class="flex-shrink-0 w-80 bg-gradient-to-br from-white to-blue-50 p-6 rounded-2xl border border-blue-200/60 hover:-translate-y-2 hover:scale-105 transition-all duration-300 shadow-lg backdrop-blur-lg relative overflow-hidden group">
                        <div class="mb-4">
                            <img src="assets/avatars/alex-director.svg" alt="市场总监" class="w-16 h-16 rounded-full border-4 border-blue-300 shadow-lg">
                        </div>
                        <div class="mb-4">
                            <h4 class="text-xl font-bold text-gray-800 mb-2">Alex - 市场总监</h4>
                            <p class="text-green-600 font-medium text-sm mb-4 flex items-center gap-2">
                                <span class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                                正在分析全球市场机会
                            </p>
                            <div class="member-actions">
                                <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors" onclick="window.location.href='workspace-director.html'">进入工作区</button>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-1 rounded-full text-xs font-bold">总监</div>
                    </div>

                    <!-- Emma卡片 -->
                    <div class="flex-shrink-0 w-80 bg-gradient-to-br from-white to-green-50 p-6 rounded-2xl border border-green-200/60 hover:-translate-y-2 hover:scale-105 transition-all duration-300 shadow-lg backdrop-blur-lg relative overflow-hidden group">
                        <div class="mb-4">
                            <img src="assets/avatars/emma-germany.svg" alt="德国专员" class="w-16 h-16 rounded-full border-4 border-green-300 shadow-lg">
                        </div>
                        <div class="mb-4">
                            <h4 class="text-xl font-bold text-gray-800 mb-2">Emma - 德国市场专员</h4>
                            <p class="text-green-600 font-medium text-sm mb-4 flex items-center gap-2">
                                <span class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                                监控竞品价格变动
                            </p>
                            <div class="member-actions">
                                <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors" onclick="window.location.href='workspace-new.html'">进入工作区</button>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white px-3 py-1 rounded-full text-xs font-bold">专员</div>
                    </div>

                    <!-- Michael卡片 -->
                    <div class="flex-shrink-0 w-80 bg-gradient-to-br from-white to-red-50 p-6 rounded-2xl border border-red-200/60 hover:-translate-y-2 hover:scale-105 transition-all duration-300 shadow-lg backdrop-blur-lg relative overflow-hidden group">
                        <div class="mb-4">
                            <img src="assets/avatars/michael-usa.svg" alt="美国专员" class="w-16 h-16 rounded-full border-4 border-red-300 shadow-lg">
                        </div>
                        <div class="mb-4">
                            <h4 class="text-xl font-bold text-gray-800 mb-2">Michael - 美国市场专员</h4>
                            <p class="text-green-600 font-medium text-sm mb-4 flex items-center gap-2">
                                <span class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                                🇺🇸 已分配美国市场
                            </p>
                            <div class="member-actions">
                                <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors" onclick="window.location.href='workspace-new.html'">进入工作区</button>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 bg-gradient-to-r from-red-600 to-rose-600 text-white px-3 py-1 rounded-full text-xs font-bold">专员</div>
                    </div>

                    <!-- Sarah卡片 (新雇佣的员工) -->
                    <div class="flex-shrink-0 w-80 bg-gradient-to-br from-white to-blue-50 p-6 rounded-2xl border border-blue-200/60 hover:-translate-y-2 hover:scale-105 transition-all duration-300 shadow-lg backdrop-blur-lg relative overflow-hidden group">
                        <div class="mb-4">
                            <img src="assets/avatars/sarah-uk.svg" alt="英国专员" class="w-16 h-16 rounded-full border-4 border-blue-300 shadow-lg">
                        </div>
                        <div class="mb-4">
                            <h4 class="text-xl font-bold text-gray-800 mb-2">Sarah - 英国市场专员</h4>
                            <p class="text-orange-600 font-medium text-sm mb-4 flex items-center gap-2">
                                <span class="w-2 h-2 bg-orange-500 rounded-full"></span>
                                等待分配国家
                            </p>
                            <div class="member-actions flex gap-2">
                                <button class="bg-orange-600 hover:bg-orange-700 text-white px-3 py-2 rounded-lg font-medium transition-colors text-sm" onclick="openCountryAssignmentModal('sarah')">分配国家</button>
                                <button class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg font-medium transition-colors text-sm" onclick="window.location.href='workspace-new.html'">工作区</button>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-3 py-1 rounded-full text-xs font-bold">专员</div>
                        <!-- 新员工标识 -->
                        <div class="absolute top-4 left-4 bg-gradient-to-r from-yellow-400 to-orange-400 text-white px-2 py-1 rounded-full text-xs font-bold">新成员</div>
                    </div>

                    <!-- 雇佣新成员卡片 -->
                    <div class="flex-shrink-0 w-80 bg-gradient-to-br from-white to-purple-50 p-6 rounded-2xl border border-purple-200/60 hover:-translate-y-2 hover:scale-105 transition-all duration-300 shadow-lg backdrop-blur-lg relative overflow-hidden group cursor-pointer" onclick="window.location.href='hire-new.html'">
                        <div class="h-full flex flex-col items-center justify-center text-center">
                            <div class="w-20 h-20 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mb-4">
                                <svg class="w-10 h-10 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="12" y1="5" x2="12" y2="19"/>
                                    <line x1="5" y1="12" x2="19" y2="12"/>
                                </svg>
                            </div>
                            <h4 class="text-xl font-bold text-gray-800 mb-2">雇佣新成员</h4>
                            <p class="text-gray-600 text-sm">扩充您的AI团队，提升业务能力</p>
                        </div>
                    </div>
                </div>

                <!-- 右侧滚动按钮 -->
                <button class="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all" id="scrollRight">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </button>
            </div>
        </section>

        <!-- 统计和动态区域 -->
        <div class="grid grid-cols-3 gap-4">
            <!-- 左侧统计区域 -->
            <div class="col-span-2 space-y-4">
                <!-- 全天候的超人成果 -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">市场部绩效仪表盘</h3>
                    
                    <!-- 统计卡片区域 -->
                    <div class="grid grid-cols-4 gap-4">
                        <!-- 总体投入成本卡片 -->
                        <div class="stat-card bg-gradient-to-br from-blue-500 to-blue-700 text-white p-5 rounded-2xl hover:shadow-xl hover:-translate-y-1 transition-all duration-300">
                            <div class="stat-icon bg-white/20 backdrop-blur-sm w-12 h-12 rounded-xl flex items-center justify-center mb-4">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                                </svg>
                            </div>
                            <div class="stat-content">
                                <h4 class="stat-title text-white/80 text-sm font-medium mb-1">月度投入成本</h4>
                                <p class="stat-value text-2xl font-bold text-white mb-1" id="monthly-cost">¥3.2万元/月</p>
                                <p class="stat-desc text-xs text-white/70">市场总监+专员团队综合成本</p>
                            </div>
                        </div>

                        <!-- 创造价值总额卡片 -->
                        <div class="stat-card bg-gradient-to-br from-emerald-500 to-green-700 text-white p-5 rounded-2xl hover:shadow-xl hover:-translate-y-1 transition-all duration-300">
                            <div class="stat-icon bg-white/20 backdrop-blur-sm w-12 h-12 rounded-xl flex items-center justify-center mb-4">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                                </svg>
                            </div>
                            <div class="stat-content">
                                <h4 class="stat-title text-white/80 text-sm font-medium mb-1">累计创造价值</h4>
                                <p class="stat-value text-2xl font-bold text-white mb-1" id="total-value">¥235.3万元/月</p>
                                <p class="stat-desc text-xs text-white/70">总监156.8万+专员78.5万</p>
                            </div>
                        </div>

                        <!-- 投资回报率卡片 -->
                        <div class="stat-card bg-gradient-to-br from-purple-500 to-purple-700 text-white p-5 rounded-2xl hover:shadow-xl hover:-translate-y-1 transition-all duration-300">
                            <div class="stat-icon bg-white/20 backdrop-blur-sm w-12 h-12 rounded-xl flex items-center justify-center mb-4">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                </svg>
                            </div>
                            <div class="stat-content">
                                <h4 class="stat-title text-white/80 text-sm font-medium mb-1">投资回报率</h4>
                                <p class="stat-value text-2xl font-bold text-white mb-1" id="roi-value">73.5倍 ROI</p>
                                <p class="stat-desc text-xs text-white/70">相比传统咨询团队成本</p>
                            </div>
                        </div>

                        <!-- 等效团队规模卡片 -->
                        <div class="stat-card bg-gradient-to-br from-orange-500 to-red-600 text-white p-5 rounded-2xl hover:shadow-xl hover:-translate-y-1 transition-all duration-300">
                            <div class="stat-icon bg-white/20 backdrop-blur-sm w-12 h-12 rounded-xl flex items-center justify-center mb-4">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                </svg>
                            </div>
                            <div class="stat-content">
                                <h4 class="stat-title text-white/80 text-sm font-medium mb-1">等效专家团队</h4>
                                <p class="stat-value text-2xl font-bold text-white mb-1" id="total-experts">8名高级专家</p>
                                <p class="stat-desc text-xs text-white/70">5名战略顾问+3名分析师</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 市场部绩效仪表盘 -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                  
                    
                    <!-- 重构后的市场部绩效仪表盘 -->
                    <div class="dashboard-matrix">
                   
                        
                        <!-- 价值创造趋势图表 -->
                        <div class="mt-8 border-t border-gray-200 pt-6">
                            <div class="bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-100">
                                <!-- 图表标题和控制选项 -->
                                <div class="flex items-center justify-between mb-6">
                                    <h4 class="text-lg font-bold text-gray-800 flex items-center gap-2">
                                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                        </svg>
                                        价值创造趋势分析
                                    </h4>
                                    
                                    <!-- 控制选项 -->
                                    <div class="flex items-center gap-4">
                                        <!-- 年份选择 -->
                                        <div class="flex items-center gap-2">
                                            <label class="text-sm font-medium text-gray-600">年份:</label>
                                            <select id="yearSelector" class="bg-white border border-gray-300 rounded-lg px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                <option value="2024">2024</option>
                                                <option value="2023">2023</option>
                                            </select>
                                        </div>
                                        
                                        <!-- 团队成员选择 -->
                                        <div class="flex items-center gap-2">
                                            <label class="text-sm font-medium text-gray-600">查看:</label>
                                            <select id="memberSelector" class="bg-white border border-gray-300 rounded-lg px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                <option value="team">整个团队</option>
                                                <option value="director">Alex - 市场总监</option>
                                                <option value="specialist">Emma - 德国专员</option>
                                                <option value="specialist-usa">Michael - 美国专员</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 图表容器 -->
                                <div class="bg-white rounded-lg border border-gray-200 p-4" style="height: 400px;">
                                    <canvas id="valueChart" width="100%" height="100%"></canvas>
                                </div>
                                
                                <!-- 图例说明 -->
                                <div class="flex items-center justify-center gap-6 mt-4 text-sm">
                                    <div class="flex items-center gap-2">
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        <span class="text-gray-600">累计创造价值</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                        <span class="text-gray-600">AI团队月度投入成本</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧团队动态区域 -->
            <div class="col-span-1">
                <!-- 团队动态卡片 -->
                <div class="bg-white rounded-2xl border border-gray-200 shadow-sm">
                    <div class="p-6">
                        <h2 class="text-xl font-semibold text-gray-800 mb-4">团队动态</h2>
                        <div class="divide-y divide-gray-100">
                            <!-- Emma动态 -->
                            <div class="p-4 hover:bg-gray-50 transition-colors">
                                <div class="flex items-start gap-3">
                                    <img src="assets/avatars/emma-germany.svg" alt="Emma" class="w-10 h-10 rounded-full flex-shrink-0">
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center justify-between mb-1">
                                            <div class="flex items-center gap-2">
                                                <strong class="text-gray-800 font-semibold">Emma - 德国市场专员</strong>
                                                <span class="px-2 py-0.5 bg-red-100 text-red-800 text-xs font-medium rounded-full">🚨 智能预警</span>
                                            </div>
                                            <span class="text-gray-500 text-sm">2小时前</span>
                                        </div>
                                        <p class="text-gray-600 text-sm">Bosch在Amazon.de价格下调10%，建议立即调整定价策略</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Alex动态 -->
                            <div class="p-4 hover:bg-gray-50 transition-colors">
                                <div class="flex items-start gap-3">
                                    <img src="assets/avatars/alex-director.svg" alt="Alex" class="w-10 h-10 rounded-full flex-shrink-0">
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center justify-between mb-1">
                                            <div class="flex items-center gap-2">
                                                <strong class="text-gray-800 font-semibold">Alex - 市场总监</strong>
                                                <span class="px-2 py-0.5 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">📊 报告交付</span>
                                            </div>
                                            <span class="text-gray-500 text-sm">1天前</span>
                                        </div>
                                        <p class="text-gray-600 text-sm">已完成《全球市场机会分析报告A2》，推荐德国、美国、英国作为优先市场</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Emma动态2 -->
                            <div class="p-4 hover:bg-gray-50 transition-colors">
                                <div class="flex items-start gap-3">
                                    <img src="assets/avatars/emma-germany.svg" alt="Emma" class="w-10 h-10 rounded-full flex-shrink-0">
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center justify-between mb-1">
                                            <div class="flex items-center gap-2">
                                                <strong class="text-gray-800 font-semibold">Emma - 德国市场专员</strong>
                                                <span class="px-2 py-0.5 bg-green-100 text-green-800 text-xs font-medium rounded-full">✅ 任务完成</span>
                                            </div>
                                            <span class="text-gray-500 text-sm">2天前</span>
                                        </div>
                                        <p class="text-gray-600 text-sm">德国市场深度分析报告已完成，发现3个关键增长机会</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Michael动态 -->
                            <div class="p-4 hover:bg-gray-50 transition-colors">
                                <div class="flex items-start gap-3">
                                    <img src="assets/avatars/michael-usa.svg" alt="Michael" class="w-10 h-10 rounded-full flex-shrink-0">
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center justify-between mb-1">
                                            <div class="flex items-center gap-2">
                                                <strong class="text-gray-800 font-semibold">Michael - 美国市场专员</strong>
                                                <span class="px-2 py-0.5 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">👋 新成员</span>
                                            </div>
                                            <span class="text-gray-500 text-sm">3天前</span>
                                        </div>
                                        <p class="text-gray-600 text-sm">很高兴加入团队！我将专注于美国市场的深度分析与监控</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Sarah动态 -->
                            <div class="p-4 hover:bg-gray-50 transition-colors">
                                <div class="flex items-start gap-3">
                                    <img src="assets/avatars/sarah-uk.svg" alt="Sarah" class="w-10 h-10 rounded-full flex-shrink-0">
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center justify-between mb-1">
                                            <div class="flex items-center gap-2">
                                                <strong class="text-gray-800 font-semibold">Sarah - 英国市场专员</strong>
                                                <span class="px-2 py-0.5 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">📈 市场洞察</span>
                                            </div>
                                            <span class="text-gray-500 text-sm">4天前</span>
                                        </div>
                                        <p class="text-gray-600 text-sm">发现英国市场对智能家居产品需求增长30%，建议增加相关品类</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Alex动态2 -->
                            <div class="p-4 hover:bg-gray-50 transition-colors">
                                <div class="flex items-start gap-3">
                                    <img src="assets/avatars/alex-director.svg" alt="Alex" class="w-10 h-10 rounded-full flex-shrink-0">
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center justify-between mb-1">
                                            <div class="flex items-center gap-2">
                                                <strong class="text-gray-800 font-semibold">Alex - 市场总监</strong>
                                                <span class="px-2 py-0.5 bg-indigo-100 text-indigo-800 text-xs font-medium rounded-full">🎯 策略调整</span>
                                            </div>
                                            <span class="text-gray-500 text-sm">5天前</span>
                                        </div>
                                        <p class="text-gray-600 text-sm">根据市场反馈，调整了Q2季度目标，重点关注欧洲市场增长</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Emma动态3 -->
                            <div class="p-4 hover:bg-gray-50 transition-colors">
                                <div class="flex items-start gap-3">
                                    <img src="assets/avatars/emma-germany.svg" alt="Emma" class="w-10 h-10 rounded-full flex-shrink-0">
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center justify-between mb-1">
                                            <div class="flex items-center gap-2">
                                                <strong class="text-gray-800 font-semibold">Emma - 德国市场专员</strong>
                                                <span class="px-2 py-0.5 bg-pink-100 text-pink-800 text-xs font-medium rounded-full">🤝 合作达成</span>
                                            </div>
                                            <span class="text-gray-500 text-sm">6天前</span>
                                        </div>
                                        <p class="text-gray-600 text-sm">与德国最大电商平台达成战略合作，预计提升30%市场份额</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Michael动态2 -->
                            <div class="p-4 hover:bg-gray-50 transition-colors">
                                <div class="flex items-start gap-3">
                                    <img src="assets/avatars/michael-usa.svg" alt="Michael" class="w-10 h-10 rounded-full flex-shrink-0">
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center justify-between mb-1">
                                            <div class="flex items-center gap-2">
                                                <strong class="text-gray-800 font-semibold">Michael - 美国市场专员</strong>
                                                <span class="px-2 py-0.5 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">📊 数据分析</span>
                                            </div>
                                            <span class="text-gray-500 text-sm">7天前</span>
                                        </div>
                                        <p class="text-gray-600 text-sm">完成美国市场竞品分析，发现5个潜在市场机会点</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 引入消息中心组件 -->
    <notification-center></notification-center>

    <script src="components/notification-center.js"></script>
    <script src="script.js"></script>
    <script>
        // 初始化通知中心和加载新员工
        document.addEventListener('DOMContentLoaded', () => {
            const notificationBadge = document.getElementById('notificationBadge');
            const notificationCenter = document.querySelector('notification-center');

            if (notificationBadge && notificationCenter) {
                notificationBadge.addEventListener('click', () => {
                    notificationCenter.open();
                });
            }

            // 加载新雇佣的员工
            loadHiredEmployees();
        });

        // 加载新雇佣的员工
        function loadHiredEmployees() {
            const employees = JSON.parse(localStorage.getItem('hiredEmployees') || '[]');
            
            employees.forEach(employee => {
                // 检查是否已经在页面中存在（通过查找包含该员工名字的标题）
                const existingCard = Array.from(document.querySelectorAll('h4')).find(h4 => h4.textContent.includes(employee.name));
                if (!existingCard) {
                    addEmployeeCard(employee);
                }
            });
        }

        function addEmployeeCard(employee) {
            const teamScroll = document.getElementById('teamScroll');
            const hireButton = teamScroll.querySelector('.cursor-pointer');
            
            // 生成适合的颜色主题
            const themes = [
                { bg: 'from-white to-blue-50', border: 'border-blue-200/60', borderColor: 'border-blue-300', gradient: 'from-blue-600 to-indigo-600' },
                { bg: 'from-white to-purple-50', border: 'border-purple-200/60', borderColor: 'border-purple-300', gradient: 'from-purple-600 to-violet-600' },
                { bg: 'from-white to-teal-50', border: 'border-teal-200/60', borderColor: 'border-teal-300', gradient: 'from-teal-600 to-cyan-600' },
                { bg: 'from-white to-indigo-50', border: 'border-indigo-200/60', borderColor: 'border-indigo-300', gradient: 'from-indigo-600 to-blue-600' }
            ];
            const theme = themes[Math.floor(Math.random() * themes.length)];
            
            const newEmployeeCard = document.createElement('div');
            newEmployeeCard.className = `flex-shrink-0 w-80 bg-gradient-to-br ${theme.bg} p-6 rounded-2xl border ${theme.border} hover:-translate-y-2 hover:scale-105 transition-all duration-300 shadow-lg backdrop-blur-lg relative overflow-hidden group`;
            
            // 确定状态显示
            let statusText, statusColor, statusIcon, actionButtons;
            if (employee.country) {
                statusText = `${employee.countryFlag} 已分配${employee.country}市场`;
                statusColor = 'text-green-600';
                statusIcon = '<span class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>';
                actionButtons = `
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors" onclick="window.location.href='workspace-new.html'">进入工作区</button>
                `;
            } else {
                statusText = '等待分配国家';
                statusColor = 'text-orange-600';
                statusIcon = '<span class="w-2 h-2 bg-orange-500 rounded-full"></span>';
                actionButtons = `
                    <div class="flex gap-2">
                        <button class="bg-orange-600 hover:bg-orange-700 text-white px-3 py-2 rounded-lg font-medium transition-colors text-sm" onclick="openCountryAssignmentModal('${employee.name.toLowerCase()}')">分配国家</button>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg font-medium transition-colors text-sm" onclick="window.location.href='workspace-new.html'">工作区</button>
                    </div>
                `;
            }
            
            newEmployeeCard.innerHTML = `
                <div class="mb-4">
                    <img src="${employee.avatar}" alt="${employee.name}" class="w-16 h-16 rounded-full border-4 ${theme.borderColor} shadow-lg">
                </div>
                <div class="mb-4">
                    <h4 class="text-xl font-bold text-gray-800 mb-2">${employee.name} - 市场专员</h4>
                    <p class="${statusColor} font-medium text-sm mb-4 flex items-center gap-2">
                        ${statusIcon}
                        ${statusText}
                    </p>
                    <div class="member-actions">
                        ${actionButtons}
                    </div>
                </div>
                <div class="absolute top-4 right-4 bg-gradient-to-r ${theme.gradient} text-white px-3 py-1 rounded-full text-xs font-bold">专员</div>
                ${!employee.country ? '<div class="absolute top-4 left-4 bg-gradient-to-r from-yellow-400 to-orange-400 text-white px-2 py-1 rounded-full text-xs font-bold">新成员</div>' : ''}
            `;
            
            teamScroll.insertBefore(newEmployeeCard, hireButton);
        }

        // 团队成员卡片滚动功能
        const teamScroll = document.getElementById('teamScroll');
        const scrollLeft = document.getElementById('scrollLeft');
        const scrollRight = document.getElementById('scrollRight');

        if (teamScroll && scrollLeft && scrollRight) {
            const scrollStep = 400;

            scrollLeft.addEventListener('click', () => {
                teamScroll.scrollBy({
                    left: -scrollStep,
                    behavior: 'smooth'
                });
            });

            scrollRight.addEventListener('click', () => {
                teamScroll.scrollBy({
                    left: scrollStep,
                    behavior: 'smooth'
                });
            });

            teamScroll.addEventListener('scroll', () => {
                scrollLeft.style.opacity = teamScroll.scrollLeft > 0 ? '1' : '0';
                scrollRight.style.opacity = 
                    teamScroll.scrollLeft < (teamScroll.scrollWidth - teamScroll.clientWidth) ? '1' : '0';
            });

            scrollLeft.style.opacity = '0';
        }

        // 全球市场组合绩效仪表盘已重构为表格形式，无需Chart.js代码

        // 国家分配模态框功能
        function openCountryAssignmentModal(employeeId) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-6 max-w-lg w-full mx-4 shadow-2xl">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">🌍</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">分配目标国家</h3>
                        <p class="text-gray-600">为Sarah选择一个专注的目标市场</p>
                    </div>

                    <!-- 员工预览 -->
                    <div class="bg-gray-50 p-4 rounded-xl mb-6">
                        <div class="flex items-center gap-3">
                            <img src="assets/avatars/sarah-uk.svg" alt="员工头像" class="w-12 h-12 rounded-full border-2 border-blue-300">
                            <div>
                                <div class="font-semibold text-gray-800">Sarah</div>
                                <div class="text-sm text-gray-600">市场专员 · 即将分配到</div>
                            </div>
                        </div>
                    </div>

                    <!-- 热门国家选择 -->
                    <div class="mb-6">
                        <h4 class="text-lg font-bold text-gray-800 mb-4">热门市场推荐</h4>
                        <div class="grid grid-cols-2 gap-3">
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-gray-200 hover:border-blue-300" onclick="selectCountry('uk', '🇬🇧', '英国', '欧洲金融中心')">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇬🇧</span>
                                    <div>
                                        <div class="font-medium text-gray-800">英国</div>
                                        <div class="text-xs text-gray-500">欧洲 · 金融中心</div>
                                    </div>
                                </div>
                            </div>
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-gray-200 hover:border-blue-300" onclick="selectCountry('france', '🇫🇷', '法国', '奢侈品大国')">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇫🇷</span>
                                    <div>
                                        <div class="font-medium text-gray-800">法国</div>
                                        <div class="text-xs text-gray-500">欧洲 · 奢侈品大国</div>
                                    </div>
                                </div>
                            </div>
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-gray-200 hover:border-blue-300" onclick="selectCountry('japan', '🇯🇵', '日本', '技术先进')">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇯🇵</span>
                                    <div>
                                        <div class="font-medium text-gray-800">日本</div>
                                        <div class="text-xs text-gray-500">亚洲 · 技术先进</div>
                                    </div>
                                </div>
                            </div>
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-gray-200 hover:border-blue-300" onclick="selectCountry('canada', '🇨🇦', '加拿大', '稳定增长')">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇨🇦</span>
                                    <div>
                                        <div class="font-medium text-gray-800">加拿大</div>
                                        <div class="text-xs text-gray-500">美洲 · 稳定增长</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 选中国家预览 -->
                    <div id="selectedCountryPreview" class="hidden bg-blue-50 p-4 rounded-xl border border-blue-200 mb-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <span id="selectedCountryFlag" class="text-2xl"></span>
                                <div>
                                    <div class="font-semibold text-blue-800" id="selectedCountryName"></div>
                                    <div class="text-sm text-blue-600" id="selectedCountryDesc"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex gap-4">
                        <button class="flex-1 bg-gray-600 text-white py-3 rounded-lg font-semibold hover:bg-gray-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove();">
                            稍后分配
                        </button>
                        <button id="confirmCountryBtn" class="flex-1 bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed" onclick="confirmCountryAssignment()" disabled>
                            确认分配
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        let selectedCountryData = null;

        function selectCountry(code, flag, name, desc) {
            selectedCountryData = { code, flag, name, desc };
            
            // 更新选中状态
            document.querySelectorAll('.country-option').forEach(opt => {
                opt.classList.remove('bg-blue-100', 'border-blue-400');
            });
            event.currentTarget.classList.add('bg-blue-100', 'border-blue-400');
            
            // 显示预览
            document.getElementById('selectedCountryFlag').textContent = flag;
            document.getElementById('selectedCountryName').textContent = name;
            document.getElementById('selectedCountryDesc').textContent = desc;
            document.getElementById('selectedCountryPreview').classList.remove('hidden');
            
            // 启用确认按钮
            document.getElementById('confirmCountryBtn').disabled = false;
        }

        function confirmCountryAssignment() {
            if (selectedCountryData) {
                // 显示成功消息
                const successModal = document.createElement('div');
                successModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                successModal.innerHTML = `
                    <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-2xl text-white">✅</span>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-800 mb-4">分配成功！</h3>
                            <div class="bg-green-50 p-4 rounded-xl border border-green-200 mb-6">
                                <div class="flex items-center justify-center gap-3 mb-3">
                                    <span class="text-2xl">${selectedCountryData.flag}</span>
                                    <span class="font-semibold text-green-800">Sarah</span>
                                </div>
                                <div class="text-sm text-green-700">现在负责${selectedCountryData.name}市场</div>
                                <div class="text-xs text-green-600 mt-2">可以在工作区配置具体任务了</div>
                            </div>
                            <button class="w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-all" onclick="updateEmployeeCard(); this.parentElement.parentElement.parentElement.remove(); document.querySelectorAll('.fixed.inset-0')[0].remove();">
                                确定
                            </button>
                        </div>
                    </div>
                `;
                document.body.appendChild(successModal);
            }
        }

        function updateEmployeeCard() {
            // 更新Sarah卡片状态
            const sarahH4 = Array.from(document.querySelectorAll('h4')).find(h4 => h4.textContent.includes('Sarah'));
            if (sarahH4) {
                const sarahCard = sarahH4.closest('.flex-shrink-0');
                const statusElement = sarahCard.querySelector('.text-orange-600');
                const actionsElement = sarahCard.querySelector('.member-actions');
                
                statusElement.className = 'text-green-600 font-medium text-sm mb-4 flex items-center gap-2';
                statusElement.innerHTML = `
                    <span class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                    ${selectedCountryData.flag} 已分配${selectedCountryData.name}市场
                `;
                
                actionsElement.className = 'member-actions';
                actionsElement.innerHTML = `
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors" onclick="window.location.href='workspace-new.html'">进入工作区</button>
                `;
                
                // 移除新成员标识
                const newMemberBadge = sarahCard.querySelector('.absolute.top-4.left-4');
                if (newMemberBadge) {
                    newMemberBadge.remove();
                }

                // 更新localStorage中的员工状态
                const employees = JSON.parse(localStorage.getItem('hiredEmployees') || '[]');
                const employeeIndex = employees.findIndex(emp => emp.name === 'Sarah');
                if (employeeIndex !== -1) {
                    employees[employeeIndex].country = selectedCountryData.name;
                    employees[employeeIndex].countryFlag = selectedCountryData.flag;
                    localStorage.setItem('hiredEmployees', JSON.stringify(employees));
                }
            }
        }

        // 动态添加新员工功能 (从雇佣页面回调使用)
        function addNewEmployee(name, avatar, type) {
            const teamScroll = document.getElementById('teamScroll');
            const hireButton = teamScroll.querySelector('.cursor-pointer');
            
            const newEmployeeCard = document.createElement('div');
            newEmployeeCard.className = 'flex-shrink-0 w-80 bg-gradient-to-br from-white to-blue-50 p-6 rounded-2xl border border-blue-200/60 hover:-translate-y-2 hover:scale-105 transition-all duration-300 shadow-lg backdrop-blur-lg relative overflow-hidden group';
            newEmployeeCard.innerHTML = `
                <div class="mb-4">
                    <img src="${avatar}" alt="${name}" class="w-16 h-16 rounded-full border-4 border-blue-300 shadow-lg">
                </div>
                <div class="mb-4">
                    <h4 class="text-xl font-bold text-gray-800 mb-2">${name} - 市场专员</h4>
                    <p class="text-orange-600 font-medium text-sm mb-4 flex items-center gap-2">
                        <span class="w-2 h-2 bg-orange-500 rounded-full"></span>
                        等待分配国家
                    </p>
                    <div class="member-actions flex gap-2">
                        <button class="bg-orange-600 hover:bg-orange-700 text-white px-3 py-2 rounded-lg font-medium transition-colors text-sm" onclick="openCountryAssignmentModal('${name.toLowerCase()}')">分配国家</button>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg font-medium transition-colors text-sm" onclick="window.location.href='workspace-new.html'">工作区</button>
                    </div>
                </div>
                <div class="absolute top-4 right-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-3 py-1 rounded-full text-xs font-bold">专员</div>
                <div class="absolute top-4 left-4 bg-gradient-to-r from-yellow-400 to-orange-400 text-white px-2 py-1 rounded-full text-xs font-bold">新成员</div>
            `;
            
            teamScroll.insertBefore(newEmployeeCard, hireButton);
        }
    </script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- 统计数据管理 -->
    <script src="js/stats-data.js"></script>
</body>
</html>
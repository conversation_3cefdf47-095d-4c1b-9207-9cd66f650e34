<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Foxu AI Team - 我的AI团队</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="custom.css">
    
    <style>
        /* 隐藏滚动条但保持功能 */
        .scrollbar-hide {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;  /* Chrome, Safari and Opera */
        }

        /* 员工卡片动画 */
        .group:hover .group-hover\:scale-110 {
            transform: scale(1.1);
        }

        /* 滚动按钮过渡效果 */
        #scrollLeft, #scrollRight {
            transition: opacity 0.3s ease;
        }

        /* 引导弹窗样式 */
        .guidance-modal {
            backdrop-filter: blur(10px);
            background-color: rgba(0, 0, 0, 0.4);
        }

        .modal-content {
            animation: slideInUp 0.3s ease-out;
            max-height: 90vh;
            overflow-y: auto;
        }

        @keyframes slideInUp {
            from {
                transform: translateY(100px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* 新用户专属样式 */
        .first-time-user .trial-badge {
            animation: pulse 2s infinite;
        }

        .first-time-user .unassigned-badge {
            animation: bounce 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translateY(0px); }
            40%, 43% { transform: translateY(-5px); }
            70% { transform: translateY(-3px); }
            90% { transform: translateY(-2px); }
        }
    </style>
</head>
<body class="first-time-user">
    <header class="header sticky top-0 z-50 bg-white/80 backdrop-blur-xl border-b border-gray-100 px-4 rounded-b-3xl shadow-lg">
        <div class="header-container max-w-[1600px] mx-auto flex justify-between items-center h-16">
            <div class="logo">
                <a href="index.html" class="text-2xl font-black bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent hover:from-purple-600 hover:to-blue-600 transition-all duration-300 cursor-pointer">Foxu AI Team</a>
            </div>
            <nav class="nav flex items-center gap-8">
                <div class="user-menu flex items-center gap-3 relative">
                    <div class="user-status flex items-center gap-2">
                        <div class="status-indicator online w-2 h-2 bg-emerald-500 rounded-full relative animate-pulse"></div>
                        <span class="user-name text-gray-700 font-semibold text-sm">David Chen</span>
                    </div>
                    <div class="notification-badge w-6 h-6 bg-gradient-to-r from-emerald-500 to-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold cursor-pointer hover:scale-110 transition-transform shadow-md border-2 border-white" id="notificationBadge">1</div>
                    <div class="user-dropdown relative">
                        <button class="user-dropdown-btn flex items-center gap-2 p-2 hover:bg-gray-100 rounded-lg transition-all" id="userDropdownBtn">
                            <img src="assets/avatars/david-chen.svg" alt="用户头像" class="user-avatar-small w-8 h-8 rounded-full border-2 border-blue-200">
                            <span class="dropdown-arrow text-xs text-gray-500 transition-transform">▼</span>
                        </button>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <main class="main-content max-w-[1600px] mx-auto px-4 py-4">
        <!-- 欢迎新用户横幅 -->
        <section class="welcome-banner mb-6 bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 p-6 rounded-2xl border border-blue-200">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">🎉 欢迎加入 Foxu AI Team！</h2>
                    <p class="text-gray-600">您的专属AI团队已准备就绪，让我们开始您的全球化之旅</p>
                </div>
                <div class="bg-white/80 p-4 rounded-xl">
                    <div class="text-3xl font-bold text-blue-600">3</div>
                    <div class="text-sm text-gray-600">名AI专员</div>
                </div>
            </div>
        </section>

        <!-- 团队成员卡片轮播区域 -->
        <section class="team-members mb-6">
            <h3 class="text-2xl font-bold text-gray-800 mb-4">您的24/7全球市场部</h3>
            <div class="relative">
                <!-- 左侧滚动按钮 -->
                <button class="absolute left-0 top-1/2 -translate-y-1/2 z-10 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all" id="scrollLeft">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </button>
                
                <!-- 卡片滚动区域 -->
                <div class="flex overflow-x-auto scrollbar-hide py-3 gap-4 px-10" id="teamScroll">
                    <!-- Alex卡片 - 试用期状态 -->
                    <div class="flex-shrink-0 w-80 bg-gradient-to-br from-white to-blue-50 p-6 rounded-2xl border border-blue-200/60 hover:-translate-y-2 hover:scale-105 transition-all duration-300 shadow-lg backdrop-blur-lg relative overflow-hidden group">
                        <!-- 试用期标识 -->
                        <div class="absolute top-4 right-4 bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-full text-xs font-bold trial-badge">
                            试用期 6天
                        </div>
                        
                        <div class="mb-4">
                            <img src="assets/avatars/alex-director.svg" alt="市场总监" class="w-16 h-16 rounded-full border-4 border-blue-300 shadow-lg">
                        </div>
                        <div class="mb-4">
                            <h4 class="text-xl font-bold text-gray-800 mb-2">Alex - 市场总监</h4>
                            <div class="bg-gradient-to-r from-purple-100 to-pink-100 p-3 rounded-lg mb-4 border border-purple-200">
                                <p class="text-purple-700 font-medium text-sm flex items-center gap-2">
                                    <span class="text-lg">✨</span>
                                    让她帮你扫描全球商机试试先！
                                </p>
                            </div>
                            <div class="member-actions">
                                <button class="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-all shadow-lg hover:shadow-xl transform hover:scale-105" onclick="startGlobalScan()">
                                    🔍 开始扫描全球商机
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Emma卡片 - 未分配国家 -->
                    <div class="flex-shrink-0 w-80 bg-gradient-to-br from-white to-green-50 p-6 rounded-2xl border border-green-200/60 hover:-translate-y-2 hover:scale-105 transition-all duration-300 shadow-lg backdrop-blur-lg relative overflow-hidden group">
                        <!-- 未分配状态标识 -->
                        <div class="absolute top-4 right-4 bg-gradient-to-r from-orange-400 to-yellow-500 text-white px-3 py-1 rounded-full text-xs font-bold unassigned-badge">
                            未分配国家
                        </div>
                        
                        <div class="mb-4">
                            <img src="assets/avatars/emma-germany.svg" alt="德国专员" class="w-16 h-16 rounded-full border-4 border-green-300 shadow-lg opacity-80">
                        </div>
                        <div class="mb-4">
                            <h4 class="text-xl font-bold text-gray-800 mb-2">Emma - 市场专员</h4>
                            <p class="text-gray-500 font-medium text-sm mb-4 flex items-center gap-2">
                                <span class="w-2 h-2 bg-gray-400 rounded-full"></span>
                                等待分配目标市场
                            </p>
                            <div class="member-actions">
                                <button class="bg-gray-400 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-medium transition-colors" onclick="showGuidanceModal('emma')">
                                    分配国家
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Michael卡片 - 未分配国家 -->
                    <div class="flex-shrink-0 w-80 bg-gradient-to-br from-white to-red-50 p-6 rounded-2xl border border-red-200/60 hover:-translate-y-2 hover:scale-105 transition-all duration-300 shadow-lg backdrop-blur-lg relative overflow-hidden group">
                        <!-- 未分配状态标识 -->
                        <div class="absolute top-4 right-4 bg-gradient-to-r from-orange-400 to-yellow-500 text-white px-3 py-1 rounded-full text-xs font-bold unassigned-badge">
                            未分配国家
                        </div>
                        
                        <div class="mb-4">
                            <img src="assets/avatars/michael-usa.svg" alt="美国专员" class="w-16 h-16 rounded-full border-4 border-red-300 shadow-lg opacity-80">
                        </div>
                        <div class="mb-4">
                            <h4 class="text-xl font-bold text-gray-800 mb-2">Michael - 市场专员</h4>
                            <p class="text-gray-500 font-medium text-sm mb-4 flex items-center gap-2">
                                <span class="w-2 h-2 bg-gray-400 rounded-full"></span>
                                等待分配目标市场
                            </p>
                            <div class="member-actions">
                                <button class="bg-gray-400 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-medium transition-colors" onclick="showGuidanceModal('michael')">
                                    分配国家
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 雇佣新成员卡片 -->
                    <div class="flex-shrink-0 w-80 bg-gradient-to-br from-white to-purple-50 p-6 rounded-2xl border border-purple-200/60 hover:-translate-y-2 hover:scale-105 transition-all duration-300 shadow-lg backdrop-blur-lg relative overflow-hidden group cursor-pointer" onclick="window.location.href='hire.html'">
                        <div class="h-full flex flex-col items-center justify-center text-center">
                            <div class="w-20 h-20 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mb-4">
                                <svg class="w-10 h-10 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="12" y1="5" x2="12" y2="19"/>
                                    <line x1="5" y1="12" x2="19" y2="12"/>
                                </svg>
                            </div>
                            <h4 class="text-xl font-bold text-gray-800 mb-2">雇佣新成员</h4>
                            <p class="text-gray-600 text-sm">扩充您的AI团队，提升业务能力</p>
                        </div>
                    </div>
                </div>

                <!-- 右侧滚动按钮 -->
                <button class="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all" id="scrollRight">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </button>
            </div>
        </section>

        <!-- 入门指导区域 -->
        <section class="guidance-section mb-6">
            <div class="bg-gradient-to-r from-indigo-50 to-blue-50 p-6 rounded-2xl border border-indigo-200">
                <div class="flex items-start gap-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-xl flex items-center justify-center text-white text-2xl">
                        💡
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-bold text-gray-800 mb-2">快速入门建议</h3>
                        <p class="text-gray-600 mb-4">为了获得最佳的团队配置，我们建议您：</p>
                        <div class="space-y-2">
                            <div class="flex items-center gap-3 text-sm">
                                <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center font-bold text-xs">1</span>
                                <span class="text-gray-700">让市场总监Alex先扫描全球商机，了解市场潜力</span>
                            </div>
                            <div class="flex items-center gap-3 text-sm">
                                <span class="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center font-bold text-xs">2</span>
                                <span class="text-gray-700">根据Alex的建议，为专员们分配最适合的目标市场</span>
                            </div>
                            <div class="flex items-center gap-3 text-sm">
                                <span class="w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center font-bold text-xs">3</span>
                                <span class="text-gray-700">开始深度市场分析，获取竞争情报和商机预警</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 简化版团队概览 -->
        <section class="team-overview">
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">团队状态概览</h3>
                <div class="grid grid-cols-3 gap-6">
                    <!-- 市场总监状态 -->
                    <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl border border-blue-200">
                        <img src="assets/avatars/alex-director.svg" alt="Alex" class="w-16 h-16 mx-auto rounded-full border-4 border-blue-300 mb-3">
                        <h4 class="font-semibold text-gray-800 mb-1">Alex - 市场总监</h4>
                        <div class="text-sm text-orange-600 font-medium bg-orange-100 px-2 py-1 rounded-full">试用期 6天</div>
                        <p class="text-xs text-gray-500 mt-2">准备扫描全球商机</p>
                    </div>
                    
                    <!-- Emma状态 -->
                    <div class="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <img src="assets/avatars/emma-germany.svg" alt="Emma" class="w-16 h-16 mx-auto rounded-full border-4 border-gray-300 mb-3 opacity-60">
                        <h4 class="font-semibold text-gray-600 mb-1">Emma - 市场专员</h4>
                        <div class="text-sm text-gray-500 font-medium bg-gray-100 px-2 py-1 rounded-full">待分配国家</div>
                        <p class="text-xs text-gray-400 mt-2">等待目标市场</p>
                    </div>
                    
                    <!-- Michael状态 -->
                    <div class="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                        <img src="assets/avatars/michael-usa.svg" alt="Michael" class="w-16 h-16 mx-auto rounded-full border-4 border-gray-300 mb-3 opacity-60">
                        <h4 class="font-semibold text-gray-600 mb-1">Michael - 市场专员</h4>
                        <div class="text-sm text-gray-500 font-medium bg-gray-100 px-2 py-1 rounded-full">待分配国家</div>
                        <p class="text-xs text-gray-400 mt-2">等待目标市场</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 引导弹窗 -->
    <div id="guidanceModal" class="fixed inset-0 z-50 guidance-modal hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-content bg-white rounded-2xl shadow-2xl max-w-md w-full mx-auto">
                <div class="p-6">
                    <!-- 弹窗头部 -->
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-bold text-gray-800">💡 智能建议</h3>
                        <button onclick="hideGuidanceModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>

                    <!-- 弹窗内容 -->
                    <div class="mb-6">
                        <div class="bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-xl border border-yellow-200 mb-4">
                            <div class="flex items-start gap-3">
                                <div class="text-2xl">🎯</div>
                                <div>
                                    <p class="text-gray-700 font-medium mb-2">建议您先让市场总监扫描全球商机</p>
                                    <p class="text-gray-600 text-sm">了解Alex的市场建议后，再为专员们分配最适合的国家，这样能确保您的团队发挥最大价值。</p>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-3">
                            <div class="flex items-center gap-3 text-sm text-gray-600">
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span>Alex会分析全球市场潜力和竞争态势</span>
                            </div>
                            <div class="flex items-center gap-3 text-sm text-gray-600">
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span>提供专业的市场优先级建议</span>
                            </div>
                            <div class="flex items-center gap-3 text-sm text-gray-600">
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span>帮助您做出最优的团队配置决策</span>
                            </div>
                        </div>
                    </div>

                    <!-- 弹窗按钮 -->
                    <div class="space-y-3">
                        <button onclick="goToDirectorScan()" class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 px-4 rounded-xl font-medium transition-all shadow-lg hover:shadow-xl">
                            🔍 先让总监扫描商机（推荐）
                        </button>
                        <button onclick="proceedWithAssignment()" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-xl font-medium transition-colors">
                            我已有明确目标，直接分配
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentEmployee = '';

        // 显示引导弹窗
        function showGuidanceModal(employee) {
            currentEmployee = employee;
            document.getElementById('guidanceModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // 隐藏引导弹窗
        function hideGuidanceModal() {
            document.getElementById('guidanceModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
            currentEmployee = '';
        }

        // 跳转到总监扫描商机
        function goToDirectorScan() {
            hideGuidanceModal();
            // 这里可以跳转到Alex的工作区，并显示扫描商机的界面
            alert('正在跳转到Alex的工作区，开始全球商机扫描...');
            // 实际实现中可以跳转到 workspace.html?employee=alex&action=scan
        }

        // 继续分配流程
        function proceedWithAssignment() {
            hideGuidanceModal();
            // 这里可以跳转到雇佣页面，选择具体国家
            alert(`正在为${currentEmployee}分配国家...`);
            // 实际实现中可以跳转到 hire.html?employee=${currentEmployee}
        }

        // 开始全球扫描
        function startGlobalScan() {
            alert('正在启动Alex的全球市场扫描功能...');
            // 实际实现中可以跳转到 workspace.html?employee=alex&action=scan
        }

        // 团队成员卡片滚动功能
        const teamScroll = document.getElementById('teamScroll');
        const scrollLeft = document.getElementById('scrollLeft');
        const scrollRight = document.getElementById('scrollRight');

        if (teamScroll && scrollLeft && scrollRight) {
            const scrollStep = 400;

            scrollLeft.addEventListener('click', () => {
                teamScroll.scrollBy({
                    left: -scrollStep,
                    behavior: 'smooth'
                });
            });

            scrollRight.addEventListener('click', () => {
                teamScroll.scrollBy({
                    left: scrollStep,
                    behavior: 'smooth'
                });
            });

            teamScroll.addEventListener('scroll', () => {
                scrollLeft.style.opacity = teamScroll.scrollLeft > 0 ? '1' : '0';
                scrollRight.style.opacity = 
                    teamScroll.scrollLeft < (teamScroll.scrollWidth - teamScroll.clientWidth) ? '1' : '0';
            });

            scrollLeft.style.opacity = '0';
        }

        // 点击弹窗外部关闭
        document.getElementById('guidanceModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideGuidanceModal();
            }
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideGuidanceModal();
            }
        });
    </script>
</body>
</html>
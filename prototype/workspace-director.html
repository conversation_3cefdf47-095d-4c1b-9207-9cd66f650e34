<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alex - 市场总监工作区 - Foxu AI Team</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="custom.css">
    
    <style>
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .feed-item {
            transition: all 0.3s ease;
        }
        
        .feed-item:hover {
            transform: translateX(4px);
        }
        
        .feed-item.unread {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-left: 4px solid #0ea5e9;
        }
        
        .feed-item.read {
            background: #f8fafc;
            opacity: 0.8;
        }
        
        .market-card {
            transition: all 0.3s ease;
        }
        
        .market-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }

        @keyframes spin-slow {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
        .animate-spin-slow {
            animation: spin-slow 3s linear infinite;
        }

        @keyframes pulse-radar {
            0% {
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
            }
            70% {
                box-shadow: 0 0 0 12px rgba(255, 255, 255, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
            }
        }
        .animate-pulse-radar {
            animation: pulse-radar 2.5s infinite;
            border-radius: 9999px; /* Make shadow circular */
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/80 backdrop-blur-xl border-b border-gray-100 px-4 rounded-b-3xl shadow-lg">
        <div class="max-w-7xl mx-auto flex justify-between items-center h-16">
            <div class="logo">
                <a href="index.html" class="text-2xl font-black bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent hover:from-purple-600 hover:to-blue-600 transition-all duration-300 cursor-pointer">Foxu AI Team</a>
            </div>
            <nav class="flex items-center gap-8">
                <div class="flex items-center gap-3 relative">
                    <div class="flex items-center gap-2">
                        <div class="w-2 h-2 bg-emerald-500 rounded-full relative animate-pulse"></div>
                        <span class="text-gray-700 font-semibold text-sm">David Chen</span>
                    </div>
                    <div class="w-6 h-6 bg-gradient-to-r from-emerald-500 to-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold cursor-pointer hover:scale-110 transition-transform shadow-md border-2 border-white" id="notificationBadge">5</div>
                    <img src="assets/avatars/david-chen.svg" alt="用户头像" class="w-8 h-8 rounded-full border-2 border-blue-200">
                </div>
            </nav>
        </div>
    </header>

    <main class="max-w-7xl mx-auto px-4 py-8">
        <!-- 上部区域：员工Profile和工作量统计 -->
        <section class="mb-8">
            <!-- 试用状态栏 -->
            <div class="bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-2xl border border-purple-200 mb-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
                            <span class="text-2xl">⭐</span>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800">试用期剩余 5 天</h3>
                            <p class="text-sm text-gray-600">试用期间可完整使用全局战略功能</p>
                        </div>
                    </div>
                    <button class="px-6 py-2 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white rounded-lg transition-all font-medium shadow-lg hover:shadow-xl" id="renewBtn">
                        立即续费
                    </button>
                </div>
            </div>

            <!-- 员工Profile -->
            <div class="bg-white rounded-3xl p-8 shadow-xl border border-gray-100 mb-6">
                <div class="flex items-center gap-8">
                    <div class="relative">
                        <img src="assets/avatars/alex-director.svg" alt="Alex" class="w-32 h-32 rounded-full border-4 border-purple-300 shadow-lg">
                        <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center border-4 border-white">
                            <span class="w-3 h-3 bg-white rounded-full animate-pulse"></span>
                        </div>
                    </div>
                    
                    <div class="flex-1">
                        <div class="flex items-center gap-4 mb-4">
                            <h1 class="text-4xl font-bold text-gray-800">Alex</h1>
                            <span class="px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-500 text-white rounded-full text-sm font-semibold">市场总监</span>
                        </div>
                        
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-800">激活能力</h3>
                                <div class="flex items-center gap-2">
                                    <label class="text-sm text-gray-600">数据月份:</label>
                                    <select id="monthSelector" class="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                        <option value="2024-01" selected>2024年1月</option>
                                        <option value="2023-12">2023年12月</option>
                                        <option value="2023-11">2023年11月</option>
                                        <option value="2023-10">2023年10月</option>
                                        <option value="2023-09">2023年9月</option>
                                        <option value="2023-08">2023年8月</option>
                                    </select>
                                </div>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-xl">
                                <div class="flex flex-wrap gap-2 text-sm text-gray-700">
                                    <span class="font-medium">A1产业链定位</span> |
                                    <span class="font-medium">A2全球商机扫描</span> |
                                    <span class="font-medium">A3目标市场研究</span> |
                                    <span class="font-medium">A8整合策略规划</span>
                                </div>
                                <div class="mt-2 text-xs text-gray-500">
                                    共4类能力已激活，当前正在7×24小时持续执行全球战略分析
                                </div>
                            </div>
                        </div>
                                            </div>
                </div>
            </div>

            <!-- 数字员工价值统计 -->
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-6" id="statsContainer">
                <!-- 7×24小时工作状态卡片 (第一位) -->
                <div class="bg-gradient-to-br from-purple-500 to-indigo-700 text-white p-6 rounded-2xl hover:shadow-xl hover:-translate-y-1 transition-all duration-300 cursor-pointer">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                            <svg class="w-8 h-8 animate-spin-slow" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 002.572 1.065c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>
                        </div>
                        <div class="text-xs bg-white/20 px-2 py-1 rounded-full">7×24小时</div>
                    </div>
                    <div class="mb-2">
                        <h3 class="text-lg font-bold mb-1">本月任务流</h3>
                        <div class="text-2xl font-extrabold" id="statusStats">89个</div>
                    </div>
                    <div class="text-white/80 text-sm">4项战略职责</div>
                    <div class="text-white/60 text-xs mt-2">持续全球市场监控中</div>
                </div>

                <!-- 实时监控中卡片 (第二位) -->
                <div class="bg-gradient-to-br from-cyan-500 to-blue-600 text-white p-6 rounded-2xl hover:shadow-xl hover:-translate-y-1 transition-all duration-300 cursor-pointer">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center animate-pulse-radar">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path></svg>
                        </div>
                        <div class="text-xs bg-white/20 px-2 py-1 rounded-full">全球覆盖</div>
                    </div>
                    <div class="mb-2">
                        <h3 class="text-lg font-bold mb-1">实时监控中</h3>
                        <div class="text-lg font-bold mb-2">5个国家潜在商机</div>
                        <div class="flex flex-wrap gap-1 text-lg">
                            <span title="德国">🇩🇪</span>
                            <span title="美国">🇺🇸</span>
                            <span title="英国">🇬🇧</span>
                            <span title="法国">🇫🇷</span>
                            <span title="日本">🇯🇵</span>
                        </div>
                    </div>
                    <div class="text-white/60 text-xs mt-2">32个潜力市场扫描中</div>
                </div>

                <!-- 等效人力价值卡片 (第三位) -->
                <div class="bg-gradient-to-br from-blue-500 to-blue-700 text-white p-6 rounded-2xl hover:shadow-xl hover:-translate-y-1 transition-all duration-300 cursor-pointer">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                            </svg>
                        </div>
                        <div class="text-xs bg-white/20 px-2 py-1 rounded-full">高效</div>
                    </div>
                    <div class="mb-2">
                        <h3 class="text-lg font-bold mb-1">等效人力工作量</h3>
                        <div class="text-2xl font-extrabold" id="workforceStats">78 人天</div>
                    </div>
                    <div class="text-white/80 text-sm">相当于传统战略团队工作量</div>
                    <div class="text-white/60 text-xs mt-2">需要5名高级战略顾问的团队</div>
                </div>

                <!-- 成本效益价值卡片 (第四位) -->
                <div class="bg-gradient-to-br from-green-500 to-emerald-700 text-white p-6 rounded-2xl hover:shadow-xl hover:-translate-y-1 transition-all duration-300 cursor-pointer">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                            </svg>
                        </div>
                        <div class="text-xs bg-white/20 px-2 py-1 rounded-full">当月数据</div>
                    </div>
                    <div class="mb-2">
                        <h3 class="text-lg font-bold mb-1">当月累计创造价值</h3>
                        <div class="text-2xl font-extrabold" id="valueCreationStats">156.8万元</div>
                    </div>
                    <div class="text-white/80 text-sm">本月实际消耗：20,000点数</div>
                    <div class="text-white/60 text-xs mt-2">相当于投入成本¥2万元</div>
                </div>
            </div>
        </section>

        <!-- 下部区域：Tab导航 -->
        <section>
            <!-- Tab导航 -->
            <div class="flex border-b border-gray-200 mb-8 bg-white rounded-t-2xl px-2">
                <button class="tab-btn px-6 py-4 font-semibold text-purple-600 border-b-2 border-purple-600 bg-purple-50 rounded-t-lg" data-tab="feed">
                    📰 Feed流
                </button>
                <button class="tab-btn px-6 py-4 font-semibold text-gray-600 hover:text-purple-600 hover:bg-purple-50 transition-colors rounded-t-lg" data-tab="kanban">
                    📋 工作看板
                </button>
                <button class="tab-btn px-6 py-4 font-semibold text-gray-600 hover:text-purple-600 hover:bg-purple-50 transition-colors rounded-t-lg" data-tab="reports">
                    📄 交付成果
                </button>
                <button class="tab-btn px-6 py-4 font-semibold text-gray-600 hover:text-purple-600 hover:bg-purple-50 transition-colors rounded-t-lg" data-tab="tasks">
                    ⚙️ 任务委托
                </button>
                <button class="tab-btn px-6 py-4 font-semibold text-gray-600 hover:text-purple-600 hover:bg-purple-50 transition-colors rounded-t-lg" data-tab="profile">
                    👤 员工信息
                </button>
            </div>

            <!-- Tab 1: Feed流 -->
            <div class="tab-content active bg-white rounded-b-2xl rounded-tr-2xl shadow-lg" id="feed">
                <div class="p-8">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">Alex的全球战略动态</h2>
                        <div class="flex gap-3">
                            <button class="filter-btn px-4 py-2 bg-purple-600 text-white rounded-lg font-medium" data-filter="all">全部</button>
                            <button class="filter-btn px-4 py-2 bg-gray-100 text-gray-700 hover:bg-purple-50 hover:text-purple-600 rounded-lg font-medium transition-colors" data-filter="strategy">战略洞察</button>
                            <button class="filter-btn px-4 py-2 bg-gray-100 text-gray-700 hover:bg-purple-50 hover:text-purple-600 rounded-lg font-medium transition-colors" data-filter="market">市场机会</button>
                            <button class="filter-btn px-4 py-2 bg-gray-100 text-gray-700 hover:bg-purple-50 hover:text-purple-600 rounded-lg font-medium transition-colors" data-filter="alert">重要预警</button>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <!-- 全球市场机会预警 -->
                        <div class="feed-item unread p-6 rounded-xl shadow-sm" data-type="market">
                            <div class="flex items-start gap-4">
                                <div class="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center text-white">
                                    🌍
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-start justify-between mb-2">
                                        <h3 class="text-lg font-semibold text-gray-800">发现新兴市场机会：越南</h3>
                                        <div class="flex items-center gap-2">
                                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-semibold rounded-full">市场机会</span>
                                            <span class="text-sm text-gray-500">1小时前</span>
                                        </div>
                                    </div>
                                    <p class="text-gray-600 mb-3">基于最新的全球市场扫描，越南电商市场增长率达到45%，建议优先评估进入机会。市场规模预计2024年将达到180亿美元。</p>
                                    <div class="flex gap-3">
                                        <button class="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg text-sm transition-colors">查看详细分析</button>
                                        <button class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm transition-colors">启动市场评估</button>
                                        <button class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg text-sm transition-colors">标记已读</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 战略洞察完成 -->
                        <div class="feed-item unread p-6 rounded-xl shadow-sm" data-type="strategy">
                            <div class="flex items-start gap-4">
                                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full flex items-center justify-center text-white">
                                    📊
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-start justify-between mb-2">
                                        <h3 class="text-lg font-semibold text-gray-800">A2全球商机扫描报告已完成</h3>
                                        <div class="flex items-center gap-2">
                                            <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs font-semibold rounded-full">战略洞察</span>
                                            <span class="text-sm text-gray-500">3小时前</span>
                                        </div>
                                    </div>
                                    <p class="text-gray-600 mb-3">已完成对全球32个潜力市场的深度扫描，识别出8个高优先级目标市场。德国、美国、英国排名前三，建议优先布局。</p>
                                    <div class="flex gap-3">
                                        <button class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm transition-colors">查看完整报告</button>
                                        <button class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors">安排战略会议</button>
                                        <button class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg text-sm transition-colors">标记已读</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 重要预警 -->
                        <div class="feed-item unread p-6 rounded-xl shadow-sm" data-type="alert">
                            <div class="flex items-start gap-4">
                                <div class="w-12 h-12 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center text-white">
                                    🚨
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-start justify-between mb-2">
                                        <h3 class="text-lg font-semibold text-gray-800">德国市场表现异常下滑</h3>
                                        <div class="flex items-center gap-2">
                                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs font-semibold rounded-full">重要预警</span>
                                            <span class="text-sm text-gray-500">6小时前</span>
                                        </div>
                                    </div>
                                    <p class="text-gray-600 mb-3">检测到德国市场本月表现较预期下滑15%，主要原因为新竞争对手进入和政策变化。建议立即启动应对策略。</p>
                                    <div class="flex gap-3">
                                        <button class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm transition-colors">紧急分析</button>
                                        <button class="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg text-sm transition-colors">联系Emma</button>
                                        <button class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg text-sm transition-colors">标记已读</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 已读项目 -->
                        <div class="feed-item read p-6 rounded-xl shadow-sm" data-type="strategy">
                            <div class="flex items-start gap-4">
                                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white">
                                    📈
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-start justify-between mb-2">
                                        <h3 class="text-lg font-semibold text-gray-600">A14全球市场组合绩效报告</h3>
                                        <div class="flex items-center gap-2">
                                            <span class="px-2 py-1 bg-gray-200 text-gray-600 text-xs font-semibold rounded-full">战略洞察</span>
                                            <span class="text-sm text-gray-400">1天前</span>
                                        </div>
                                    </div>
                                    <p class="text-gray-500 mb-3">本月整体市场组合ROI为156%，德国表现最佳，美国增长稳定，英国需要优化策略。</p>
                                    <div class="flex gap-3">
                                        <button class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg text-sm transition-colors">重新查看</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 加载更多 -->
                    <div class="text-center mt-8">
                        <button class="px-6 py-3 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                            加载更多动态
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tab 2: 工作看板 -->
            <div class="tab-content bg-white rounded-b-2xl rounded-tr-2xl shadow-lg" id="kanban">
                <div class="p-8">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">Alex的全球战略看板</h2>
                        <div class="flex items-center gap-4">
                            <span class="px-3 py-1 bg-purple-100 text-purple-800 text-sm font-semibold rounded-full">按年订阅</span>
                            <span class="text-sm text-gray-600">订阅到期：2025年1月15日</span>
                        </div>
                    </div>

                    <!-- 全局战略模块 -->
                    <div class="space-y-8 mb-8">
                        <!-- 全球扫描与定位 -->
                        <div class="bg-gradient-to-r from-indigo-50 to-purple-50 p-6 rounded-2xl border border-indigo-200">
                            <div class="flex items-center gap-3 mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center text-white">
                                    <span class="text-xl">🌍</span>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-xl font-bold text-gray-800">全球扫描与定位</h3>
                                    <p class="text-sm text-gray-600">识别全球机会，制定进入策略</p>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-2 mb-1">
                                        <span class="w-2 h-2 bg-indigo-500 rounded-full animate-pulse"></span>
                                        <span class="text-xs font-medium text-indigo-600">持续扫描</span>
                                    </div>
                                    <span class="text-xs text-gray-500">覆盖 32 个市场</span>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                <div class="bg-white p-4 rounded-xl shadow-sm">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A1</div>
                                        <div class="flex-1">
                                            <h4 class="font-semibold text-gray-800">产业链定位与机会评估</h4>
                                            <div class="flex items-center gap-2 mt-1">
                                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded-full">已完成</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full bg-green-200 rounded-full h-2 mb-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                                    </div>
                                    <div class="bg-green-50 p-3 rounded-lg border border-green-200 mb-2 mt-2">
                                        <div class="flex items-center gap-2 text-sm">
                                            <span class="text-green-600">📅</span>
                                            <span class="font-medium text-green-800">季度更新：</span>
                                            <span class="text-green-700">下次更新 4月15日</span>
                                        </div>
                                    </div>
                                    <span class="text-xs text-gray-500">完成于 12月28日</span>
                                </div>

                                <div class="bg-white p-4 rounded-xl shadow-sm">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A2</div>
                                        <div class="flex-1">
                                            <h4 class="font-semibold text-gray-800">全球商机扫描</h4>
                                            <div class="flex items-center gap-2 mt-1">
                                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded-full">已完成</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full bg-green-200 rounded-full h-2 mb-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                                    </div>
                                    <div class="bg-indigo-50 p-3 rounded-lg border border-indigo-200 mb-2">
                                        <div class="flex items-center gap-2 text-sm">
                                            <span class="text-indigo-600">📅</span>
                                            <span class="font-medium text-indigo-800">季度更新：</span>
                                            <span class="text-indigo-700">下次更新 3月15日</span>
                                        </div>
                                    </div>
                                    <span class="text-xs text-gray-500">完成于 1月8日</span>
                                </div>
                            </div>
                        </div>

                        <!-- 机会雷达 -->
                        <div class="bg-gradient-to-r from-emerald-50 to-teal-50 p-6 rounded-2xl border border-emerald-200">
                            <div class="flex items-center gap-3 mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center text-white">
                                    <span class="text-xl">🎯</span>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-xl font-bold text-gray-800">机会雷达</h3>
                                    <p class="text-sm text-gray-600">持续扫描全球3个市场，实时发现新机会</p>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-2 mb-1">
                                        <span class="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></span>
                                        <span class="text-xs font-medium text-emerald-600">月度监控</span>
                                    </div>
                                    <span class="text-xs text-gray-500">下次更新 2月15日</span>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                                <!-- A3 德国市场研究 -->
                                <div class="bg-white p-4 rounded-xl shadow-sm">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A3</div>
                                        <div class="flex-1">
                                            <h4 class="font-semibold text-gray-800">德国市场研究与细分</h4>
                                            <div class="flex items-center gap-2 mt-1">
                                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded-full">已完成</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full bg-green-200 rounded-full h-2 mb-3">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                                    </div>
                                    <span class="text-xs text-gray-500">完成于 1月10日</span>
                                </div>
                                
                                <!-- A3 美国市场研究 -->
                                <div class="bg-white p-4 rounded-xl shadow-sm">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A3</div>
                                        <div class="flex-1">
                                            <h4 class="font-semibold text-gray-800">美国市场研究与细分</h4>
                                            <div class="flex items-center gap-2 mt-1">
                                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded-full">进行中</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full bg-blue-200 rounded-full h-2 mb-3">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: 75%"></div>
                                    </div>
                                    <div class="flex justify-between text-xs text-gray-500">
                                        <span>75% 完成</span>
                                        <span>预计 1月22日</span>
                                    </div>
                                </div>
                                
                                <!-- A3 英国市场研究 -->
                                <div class="bg-white p-4 rounded-xl shadow-sm">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A3</div>
                                        <div class="flex-1">
                                            <h4 class="font-semibold text-gray-800">英国市场研究与细分</h4>
                                            <div class="flex items-center gap-2 mt-1">
                                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-semibold rounded-full">待开始</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full bg-yellow-200 rounded-full h-2 mb-3">
                                        <div class="bg-yellow-500 h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <span class="text-xs text-gray-500">预计 2月01日开始</span>
                                </div>
                            </div>
                        </div>

                        <!-- 战略复盘与优化 -->
                        <div class="bg-gradient-to-r from-orange-50 to-red-50 p-6 rounded-2xl border border-orange-200">
                            <div class="flex items-center gap-3 mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center text-white">
                                    <span class="text-xl">🎯</span>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-xl font-bold text-gray-800">战略复盘与优化</h3>
                                    <p class="text-sm text-gray-600">定期复盘，动态调整策略</p>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-2 mb-1">
                                        <span class="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></span>
                                        <span class="text-xs font-medium text-orange-600">月度复盘</span>
                                    </div>
                                    <span class="text-xs text-gray-500">下次会议：1月30日</span>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                                <div class="bg-white p-4 rounded-xl shadow-sm">
                                    <h4 class="font-semibold text-gray-800 mb-2">德国市场复盘</h4>
                                    <div class="flex items-center gap-2 mb-2">
                                        <span class="w-3 h-3 bg-green-500 rounded-full"></span>
                                        <span class="text-green-600 font-medium text-sm">表现优异</span>
                                    </div>
                                    <p class="text-xs text-gray-600">ROI 195%，超出预期25%</p>
                                </div>
                                
                                <div class="bg-white p-4 rounded-xl shadow-sm">
                                    <h4 class="font-semibold text-gray-800 mb-2">美国市场复盘</h4>
                                    <div class="flex items-center gap-2 mb-2">
                                        <span class="w-3 h-3 bg-blue-500 rounded-full"></span>
                                        <span class="text-blue-600 font-medium text-sm">稳定增长</span>
                                    </div>
                                    <p class="text-xs text-gray-600">ROI 142%，符合预期</p>
                                </div>
                                
                                <div class="bg-white p-4 rounded-xl shadow-sm">
                                    <h4 class="font-semibold text-gray-800 mb-2">英国市场复盘</h4>
                                    <div class="flex items-center gap-2 mb-2">
                                        <span class="w-3 h-3 bg-yellow-500 rounded-full"></span>
                                        <span class="text-yellow-600 font-medium text-sm">需要优化</span>
                                    </div>
                                    <p class="text-xs text-gray-600">ROI 108%，低于预期</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab 3: 交付成果中心 -->
            <div class="tab-content bg-white rounded-b-2xl rounded-tr-2xl shadow-lg" id="reports">
                <div class="p-8">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">全球战略成果中心</h2>
                        <div class="flex gap-3">
                            <input type="text" placeholder="搜索报告..." class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                            <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                <option>全部类型</option>
                                <option>全球战略</option>
                                <option>市场扫描</option>
                                <option>组合分析</option>
                                <option>绩效报告</option>
                            </select>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- A1报告 -->
                        <div class="bg-gradient-to-br from-white to-purple-50 rounded-2xl shadow-lg border border-purple-100 overflow-hidden hover:shadow-xl transition-all duration-300">
                            <div class="h-48 bg-gradient-to-br from-purple-100 to-indigo-200 flex items-center justify-center relative">
                                <img src="assets/reports/report-a1.svg" alt="A1报告" class="w-40 h-32 object-cover rounded-lg shadow-md">
                                <div class="absolute top-4 right-4 bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                                    v1.0
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-3">
                                    <h3 class="text-xl font-bold text-gray-800">A1 - 产业链定位与机会评估</h3>
                                    <select class="text-sm border border-gray-300 rounded px-2 py-1 bg-white">
                                        <option>v1.0 (初版)</option>
                                    </select>
                                </div>
                                <p class="text-gray-600 mb-4 leading-relaxed">在宏观价值链中找到最有利的出海角色和商业模式，为全球化战略奠定基础。</p>
                                
                                <!-- 主PPT -->
                                <div class="bg-purple-50 p-4 rounded-lg border border-purple-200 mb-4">
                                    <div class="flex items-center gap-3 mb-3">
                                        <span class="text-purple-600">🎯</span>
                                        <span class="font-semibold text-purple-800">主要演示文稿</span>
                                    </div>
                                    <div class="flex items-center gap-4 mb-3">
                                        <span class="px-3 py-1 bg-purple-100 text-purple-800 text-sm font-semibold rounded-full">A1 PPT</span>
                                        <span class="text-sm text-gray-600">2023-12-28</span>
                                        <span class="text-sm text-purple-600 font-medium">32页</span>
                                    </div>
                                    <div class="flex flex-wrap gap-2">
                                        <button class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>👁️</span>
                                            预览PPT
                                        </button>
                                        <button class="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>🎧</span>
                                            AI讲解
                                        </button>
                                        <button class="px-3 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>📥</span>
                                            下载
                                        </button>
                                    </div>
                                </div>

                                <!-- 附件文档 -->
                                <div class="border-t border-gray-200 pt-4">
                                    <div class="flex items-center gap-2 mb-3">
                                        <span class="text-gray-600">📎</span>
                                        <span class="font-medium text-gray-700">附件文档 (2个)</span>
                                        <button class="text-purple-600 text-sm hover:underline">展开</button>
                                    </div>
                                    <div class="space-y-2 text-sm">
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">产业链价值分析.pdf (28页)</span>
                                            <button class="text-purple-600 hover:underline">预览</button>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">商业模式评估矩阵.xlsx</span>
                                            <button class="text-purple-600 hover:underline">预览</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- A2报告 -->
                        <div class="bg-gradient-to-br from-white to-blue-50 rounded-2xl shadow-lg border border-blue-100 overflow-hidden hover:shadow-xl transition-all duration-300">
                            <div class="h-48 bg-gradient-to-br from-blue-100 to-cyan-200 flex items-center justify-center relative">
                                <img src="assets/reports/report-a2.svg" alt="A2报告" class="w-40 h-32 object-cover rounded-lg shadow-md">
                                <div class="absolute top-4 right-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                                    v2.1
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-3">
                                    <h3 class="text-xl font-bold text-gray-800">A2 - 全球商机扫描</h3>
                                    <select class="text-sm border border-gray-300 rounded px-2 py-1 bg-white">
                                        <option>v2.1 (月度更新)</option>
                                        <option>v2.0 (Q1更新)</option>
                                        <option>v1.0 (初版)</option>
                                    </select>
                                </div>
                                <p class="text-gray-600 mb-4 leading-relaxed">在全球范围内精准筛选最具潜力的国家市场，包含最新市场机会雷达数据。</p>
                                
                                <!-- 主PPT -->
                                <div class="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-4">
                                    <div class="flex items-center gap-3 mb-3">
                                        <span class="text-blue-600">🎯</span>
                                        <span class="font-semibold text-blue-800">主要演示文稿</span>
                                    </div>
                                    <div class="flex items-center gap-4 mb-3">
                                        <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-semibold rounded-full">A2 PPT</span>
                                        <span class="text-sm text-gray-600">2024-01-08</span>
                                        <span class="text-sm text-blue-600 font-medium">58页</span>
                                    </div>
                                    <div class="flex flex-wrap gap-2">
                                        <button class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>👁️</span>
                                            预览PPT
                                        </button>
                                        <button class="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>🎧</span>
                                            AI讲解
                                        </button>
                                        <button class="px-3 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>📥</span>
                                            下载
                                        </button>
                                    </div>
                                </div>

                                <!-- 附件文档 -->
                                <div class="border-t border-gray-200 pt-4">
                                    <div class="flex items-center gap-2 mb-3">
                                        <span class="text-gray-600">📎</span>
                                        <span class="font-medium text-gray-700">附件文档 (4个)</span>
                                        <button class="text-blue-600 text-sm hover:underline">展开</button>
                                    </div>
                                    <div class="space-y-2 text-sm">
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">全球32市场详细评估.pdf (156页)</span>
                                            <button class="text-blue-600 hover:underline">预览</button>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">市场潜力评分矩阵.xlsx</span>
                                            <button class="text-blue-600 hover:underline">预览</button>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">机会雷达实时数据.pdf (24页)</span>
                                            <button class="text-blue-600 hover:underline">预览</button>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">竞争态势分析.pdf (32页)</span>
                                            <button class="text-blue-600 hover:underline">预览</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- A3德国市场研究报告 -->
                        <div class="bg-gradient-to-br from-white to-emerald-50 rounded-2xl shadow-lg border border-emerald-100 overflow-hidden hover:shadow-xl transition-all duration-300">
                            <div class="h-48 bg-gradient-to-br from-emerald-100 to-teal-200 flex items-center justify-center relative">
                                <img src="assets/reports/report-a3-germany.svg" alt="A3德国报告" class="w-40 h-32 object-cover rounded-lg shadow-md">
                                <div class="absolute top-4 right-4 bg-emerald-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                                    v2.0
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-3">
                                    <h3 class="text-xl font-bold text-gray-800">A3 - 德国市场研究与细分</h3>
                                    <select class="text-sm border border-gray-300 rounded px-2 py-1 bg-white">
                                        <option>v2.0 (最新版)</option>
                                        <option>v1.1 (季度更新)</option>
                                        <option>v1.0 (初版)</option>
                                    </select>
                                </div>
                                <p class="text-gray-600 mb-4 leading-relaxed">德国市场深度研究分析，包含市场规模、消费者行为、竞争格局等核心要素。</p>
                                
                                <!-- 主PPT -->
                                <div class="bg-emerald-50 p-4 rounded-lg border border-emerald-200 mb-4">
                                    <div class="flex items-center gap-3 mb-3">
                                        <span class="text-emerald-600">🎯</span>
                                        <span class="font-semibold text-emerald-800">主要演示文稿</span>
                                    </div>
                                    <div class="flex items-center gap-4 mb-3">
                                        <span class="px-3 py-1 bg-emerald-100 text-emerald-800 text-sm font-semibold rounded-full">A3 PPT</span>
                                        <span class="text-sm text-gray-600">2024-01-10</span>
                                        <span class="text-sm text-emerald-600 font-medium">42页</span>
                                    </div>
                                    <div class="flex flex-wrap gap-2">
                                        <button class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>👁️</span>
                                            预览PPT
                                        </button>
                                        <button class="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>🎧</span>
                                            AI讲解
                                        </button>
                                        <button class="px-3 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>📥</span>
                                            下载
                                        </button>
                                    </div>
                                </div>

                                <!-- 附件文档 -->
                                <div class="border-t border-gray-200 pt-4">
                                    <div class="flex items-center gap-2 mb-3">
                                        <span class="text-gray-600">📎</span>
                                        <span class="font-medium text-gray-700">附件文档 (3个)</span>
                                        <button class="text-emerald-600 text-sm hover:underline">展开</button>
                                    </div>
                                    <div class="space-y-2 text-sm">
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">德国市场细分分析.pdf (38页)</span>
                                            <button class="text-emerald-600 hover:underline">预览</button>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">消费者行为研究.xlsx</span>
                                            <button class="text-emerald-600 hover:underline">预览</button>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">竞争格局分析.pdf (24页)</span>
                                            <button class="text-emerald-600 hover:underline">预览</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- A3美国市场研究报告 -->
                        <div class="bg-gradient-to-br from-white to-red-50 rounded-2xl shadow-lg border border-red-100 overflow-hidden hover:shadow-xl transition-all duration-300">
                            <div class="h-48 bg-gradient-to-br from-red-100 to-pink-200 flex items-center justify-center relative">
                                <img src="assets/reports/report-a3-usa.svg" alt="A3美国报告" class="w-40 h-32 object-cover rounded-lg shadow-md">
                                <div class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                                    v1.0
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-3">
                                    <h3 class="text-xl font-bold text-gray-800">A3 - 美国市场研究与细分</h3>
                                    <select class="text-sm border border-gray-300 rounded px-2 py-1 bg-white">
                                        <option>v1.0 (初版)</option>
                                    </select>
                                </div>
                                <p class="text-gray-600 mb-4 leading-relaxed">美国市场深度研究分析，包含市场规模、消费者行为、竞争格局等核心要素。</p>
                                
                                <!-- 主PPT -->
                                <div class="bg-red-50 p-4 rounded-lg border border-red-200 mb-4">
                                    <div class="flex items-center gap-3 mb-3">
                                        <span class="text-red-600">🎯</span>
                                        <span class="font-semibold text-red-800">主要演示文稿</span>
                                    </div>
                                    <div class="flex items-center gap-4 mb-3">
                                        <span class="px-3 py-1 bg-red-100 text-red-800 text-sm font-semibold rounded-full">A3 PPT</span>
                                        <span class="text-sm text-gray-600">2024-01-22</span>
                                        <span class="text-sm text-red-600 font-medium">48页</span>
                                    </div>
                                    <div class="flex flex-wrap gap-2">
                                        <button class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>👁️</span>
                                            预览PPT
                                        </button>
                                        <button class="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>🎧</span>
                                            AI讲解
                                        </button>
                                        <button class="px-3 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>📥</span>
                                            下载
                                        </button>
                                    </div>
                                </div>

                                <!-- 附件文档 -->
                                <div class="border-t border-gray-200 pt-4">
                                    <div class="flex items-center gap-2 mb-3">
                                        <span class="text-gray-600">📎</span>
                                        <span class="font-medium text-gray-700">附件文档 (3个)</span>
                                        <button class="text-red-600 text-sm hover:underline">展开</button>
                                    </div>
                                    <div class="space-y-2 text-sm">
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">美国市场细分分析.pdf (45页)</span>
                                            <button class="text-red-600 hover:underline">预览</button>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">消费者行为研究.xlsx</span>
                                            <button class="text-red-600 hover:underline">预览</button>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">竞争格局分析.pdf (32页)</span>
                                            <button class="text-red-600 hover:underline">预览</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- A3英国市场研究报告预览 -->
                        <div class="bg-gradient-to-br from-white to-yellow-50 rounded-2xl shadow-lg border border-yellow-100 overflow-hidden hover:shadow-xl transition-all duration-300 opacity-75">
                            <div class="h-48 bg-gradient-to-br from-yellow-100 to-orange-200 flex items-center justify-center relative">
                                <div class="w-40 h-32 bg-white/50 rounded-lg shadow-md flex items-center justify-center">
                                    <span class="text-4xl">🇬🇧</span>
                                </div>
                                <div class="absolute top-4 right-4 bg-yellow-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                                    待开始
                                </div>
                            </div>
                            <div class="p-6">
                                <h3 class="text-xl font-bold text-gray-700 mb-3">A3 - 英国市场研究与细分</h3>
                                <p class="text-gray-500 mb-4 leading-relaxed">英国市场深度研究分析计划，等待美国市场研究完成后启动...</p>
                                <div class="flex items-center gap-4 mb-4">
                                    <span class="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm font-semibold rounded-full">A3 市场研究</span>
                                    <span class="text-sm text-gray-400">预计 2024-02-01</span>
                                    <span class="text-sm text-yellow-600 font-medium">排队中...</span>
                                </div>
                                <div class="w-full bg-yellow-200 rounded-full h-2">
                                    <div class="bg-yellow-500 h-2 rounded-full" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 空白卡片用于对齐 -->
                        <div></div>
                    </div>
                </div>
            </div>

            <!-- Tab 4: 任务委托中心 -->
            <div class="tab-content bg-white rounded-b-2xl rounded-tr-2xl shadow-lg" id="tasks">
                <div class="p-8">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-800 mb-4">Alex的任务委托中心</h2>
                        <p class="text-gray-600">委托Alex执行全球战略分析任务，直接在各任务卡片中购买或管理配置</p>
                    </div>

                    <div class="grid grid-cols-4 gap-8">
                        <!-- 左侧：Artifacts配置 -->
                        <div class="col-span-3">

                            <!-- 全球战略模块 -->
                            <div class="mb-8">
                                <div class="flex items-center gap-3 mb-6">
                                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-xl flex items-center justify-center text-white">
                                        <span class="text-xl">🌍</span>
                                    </div>
                                    <div>
                                        <h3 class="text-xl font-bold text-gray-800">全球战略模块</h3>
                                        <p class="text-sm text-gray-600">制定全球化战略和市场进入方案</p>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                    <!-- A1 - 已激活状态 -->
                                    <div class="artifact-config border-2 rounded-xl p-6 cursor-pointer transition-all border-purple-200 bg-purple-50" data-artifact="A1" data-price="20000" data-status="active">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A1</div>
                                            <div class="flex-1">
                                                <h4 class="font-bold text-gray-800">产业链定位与机会评估</h4>
                                                <div class="flex items-center gap-2 mt-1">
                                                    <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs font-semibold rounded-full">已激活</span>
                                                    <span class="text-xs text-gray-500">按年订阅</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="text-sm text-gray-700">• 全球产业链价值分析</div>
                                            <div class="text-sm text-gray-700">• 商业模式评估与优化</div>
                                            <div class="text-sm text-gray-700">• 竞争优势构建方案</div>
                                            <div class="text-sm text-gray-700">• 出海角色策略设计</div>
                                        </div>
                                        <div class="bg-purple-100 p-3 rounded-lg border border-purple-200 mb-4">
                                            <div class="flex items-center gap-2 text-sm">
                                                <span class="text-purple-600">📅</span>
                                                <span class="font-medium text-purple-800">更新频率：</span>
                                                <span class="text-purple-700">按季度更新</span>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <span class="text-purple-600 font-semibold">20,000 点数/年</span>
                                                <span class="text-xs text-gray-500 line-through">¥20,000</span>
                                            </div>
                                            <button class="px-3 py-1 bg-purple-600 text-white rounded-lg text-sm hover:bg-purple-700 transition-colors" onclick="openSubscriptionConfigModal('A1')">
                                                管理配置
                                            </button>
                                        </div>
                                    </div>

                                    <!-- A2 - 首次免费特殊状态 -->
                                    <div class="artifact-config border-2 rounded-xl p-6 cursor-pointer transition-all border-green-200 bg-green-50 relative" data-artifact="A2" data-price="0" data-status="free-first">
                                        <div class="absolute -top-3 left-4">
                                            <span class="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-3 py-1 rounded-full text-xs font-bold">首次免费</span>
                                        </div>
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A2</div>
                                            <div class="flex-1">
                                                <h4 class="font-bold text-gray-800">全球商机扫描</h4>
                                                <div class="flex items-center gap-2 mt-1">
                                                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded-full">🎁 首次免费</span>
                                                    <span class="text-xs text-gray-500">后续按年订阅</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="text-sm text-gray-700">• 全球32个潜力市场深度扫描</div>
                                            <div class="text-sm text-gray-700">• 市场潜力评分与排名</div>
                                            <div class="text-sm text-gray-700">• 进入时机建议</div>
                                            <div class="text-sm text-gray-700">• 新兴市场机会识别</div>
                                        </div>
                                        <div class="bg-green-100 p-3 rounded-lg border border-green-200 mb-4">
                                            <div class="flex items-center gap-2 text-sm">
                                                <span class="text-green-600">🎯</span>
                                                <span class="font-medium text-green-800">首次体验：</span>
                                                <span class="text-green-700">完全免费，后续20,000点数/年</span>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <span class="text-green-600 font-semibold">免费体验</span>
                                                <span class="text-xs text-gray-500">后续 20,000 点数/年</span>
                                            </div>
                                            <button class="px-3 py-1 bg-green-600 text-white rounded-lg text-sm hover:bg-green-700 transition-colors" onclick="claimFreeA2()">
                                                管理配置
                                            </button>
                                        </div>
                                    </div>

                                    <!-- A3 - 多国家配置 -->
                                    <div class="artifact-config border-2 rounded-xl p-6 cursor-pointer transition-all border-emerald-200 bg-emerald-50" data-artifact="A3" data-price="50000" data-status="active">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A3</div>
                                            <div class="flex-1">
                                                <h4 class="font-bold text-gray-800">目标市场研究与细分</h4>
                                                <div class="flex items-center gap-2 mt-1">
                                                    <span class="px-2 py-1 bg-emerald-100 text-emerald-800 text-xs font-semibold rounded-full">已激活</span>
                                                    <span class="text-xs text-gray-500">按年订阅</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="text-sm text-gray-700">• 深度研究指定目标国家市场</div>
                                            <div class="text-sm text-gray-700">• 市场规模与增长趋势分析</div>
                                            <div class="text-sm text-gray-700">• 消费者行为特征研究</div>
                                            <div class="text-sm text-gray-700">• 竞争格局与市场结构</div>
                                        </div>
                                        <div class="bg-emerald-100 p-3 rounded-lg border border-emerald-200 mb-4">
                                            <div class="flex items-center gap-2 text-sm">
                                                <span class="text-emerald-600">🗺️</span>
                                                <span class="font-medium text-emerald-800">监控中：</span>
                                                <span class="text-emerald-700">5 个国家 (默认包含，可扩展)</span>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <span class="text-emerald-600 font-semibold">50,000 点数/年</span>
                                                <span class="text-xs text-gray-500 line-through">¥50,000</span>
                                            </div>
                                            <button class="px-3 py-1 bg-emerald-600 text-white rounded-lg text-sm hover:bg-emerald-700 transition-colors" onclick="openA3ConfigModal()">
                                                管理配置
                                            </button>
                                        </div>
                                    </div>

                                    <!-- A8 - 整合策略 -->
                                    <div class="artifact-config border-2 rounded-xl p-6 cursor-pointer transition-all border-orange-200 bg-orange-50" data-artifact="A8" data-price="20000" data-status="active">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A8</div>
                                            <div class="flex-1">
                                                <h4 class="font-bold text-gray-800">整合策略规划与动态优化</h4>
                                                <div class="flex items-center gap-2 mt-1">
                                                    <span class="px-2 py-1 bg-orange-100 text-orange-800 text-xs font-semibold rounded-full">已激活</span>
                                                    <span class="text-xs text-gray-500">按年订阅</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="text-sm text-gray-700">• 综合战略报告整合</div>
                                            <div class="text-sm text-gray-700">• 执行时间表与里程碑</div>
                                            <div class="text-sm text-gray-700">• 风险预警与应对方案</div>
                                            <div class="text-sm text-gray-700">• KPI体系设计</div>
                                        </div>
                                        <div class="bg-orange-100 p-3 rounded-lg border border-orange-200 mb-4">
                                            <div class="flex items-center gap-2 text-sm">
                                                <span class="text-orange-600">📅</span>
                                                <span class="font-medium text-orange-800">复盘服务：</span>
                                                <span class="text-orange-700">月度战略复盘会议</span>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <span class="text-orange-600 font-semibold">20,000 点数/年</span>
                                                <span class="text-xs text-gray-500 line-through">¥20,000</span>
                                            </div>
                                            <button class="px-3 py-1 bg-orange-600 text-white rounded-lg text-sm hover:bg-orange-700 transition-colors" onclick="openSubscriptionConfigModal('A8')">
                                                管理配置
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧：任务控制面板 -->
                        <div class="col-span-1">
                            <div class="bg-gray-50 rounded-2xl p-6 sticky top-8">
                                <h3 class="text-lg font-bold text-gray-800 mb-4">任务控制面板</h3>
                                
                                <!-- 点数管理区域 -->
                                <div class="mb-6">
                                    <h4 class="text-sm font-semibold text-gray-700 mb-3">点数管理</h4>
                                    <div class="bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-xl border border-purple-200">
                                        <div class="flex items-center justify-between mb-3">
                                            <div class="flex items-center gap-2">
                                                <div class="w-6 h-6 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full flex items-center justify-center text-white text-xs">💎</div>
                                                <span class="text-sm font-medium text-gray-700">当前余额</span>
                                            </div>
                                            <span class="text-xl font-bold text-purple-600" id="sidebarPointsBalance">85,000</span>
                                        </div>
                                        <button class="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-2 rounded-lg font-semibold hover:from-purple-700 hover:to-indigo-700 transition-all shadow-md text-sm" onclick="openChargeModal()">
                                            💳 充值点数
                                        </button>
                                    </div>
                                </div>

                                <!-- 任务状态概览 -->
                                <div class="mb-6">
                                    <h4 class="text-sm font-semibold text-gray-700 mb-3">任务状态概览</h4>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between p-2 bg-purple-50 rounded-lg border border-purple-200">
                                            <div class="flex items-center gap-2">
                                                <span class="w-4 h-4 bg-purple-500 rounded-full text-xs flex items-center justify-center text-white font-bold">A1</span>
                                                <span class="text-xs text-purple-700">产业链定位</span>
                                            </div>
                                            <span class="text-xs text-purple-600">✓ 已激活</span>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-green-50 rounded-lg border border-green-200">
                                            <div class="flex items-center gap-2">
                                                <span class="w-4 h-4 bg-green-500 rounded-full text-xs flex items-center justify-center text-white font-bold">A2</span>
                                                <span class="text-xs text-green-700">全球商机</span>
                                            </div>
                                            <span class="text-xs text-green-600">🎁 首次免费</span>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-emerald-50 rounded-lg border border-emerald-200">
                                            <div class="flex items-center gap-2">
                                                <span class="w-4 h-4 bg-emerald-500 rounded-full text-xs flex items-center justify-center text-white font-bold">A3</span>
                                                <span class="text-xs text-emerald-700">市场研究</span>
                                            </div>
                                            <span class="text-xs text-emerald-600">✓ 已激活</span>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-orange-50 rounded-lg border border-orange-200">
                                            <div class="flex items-center gap-2">
                                                <span class="w-4 h-4 bg-orange-500 rounded-full text-xs flex items-center justify-center text-white font-bold">A8</span>
                                                <span class="text-xs text-orange-700">策略优化</span>
                                            </div>
                                            <span class="text-xs text-orange-600">✓ 已激活</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 快速统计 -->
                                <div class="mb-6">
                                    <h4 class="text-sm font-semibold text-gray-700 mb-3">任务统计</h4>
                                    <div class="grid grid-cols-2 gap-3">
                                        <div class="bg-white p-3 rounded-lg border border-gray-200 text-center">
                                            <div class="text-lg font-bold text-purple-600">4</div>
                                            <div class="text-xs text-gray-600">已激活</div>
                                        </div>
                                        <div class="bg-white p-3 rounded-lg border border-gray-200 text-center">
                                            <div class="text-lg font-bold text-emerald-600">5</div>
                                            <div class="text-xs text-gray-600">监控市场</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 月度费用概览 -->
                                <div class="mb-6">
                                    <h4 class="text-sm font-semibold text-gray-700 mb-3">月度费用</h4>
                                    <div class="bg-white p-4 rounded-lg border border-gray-200">
                                        <div class="flex justify-between text-sm mb-2">
                                            <span class="text-gray-600">A1 产业链定位</span>
                                            <span class="font-semibold text-gray-800">1,667 点数/月</span>
                                        </div>
                                        <div class="flex justify-between text-sm mb-2">
                                            <span class="text-gray-600">A3 市场研究</span>
                                            <span class="font-semibold text-gray-800">4,167 点数/月</span>
                                        </div>
                                        <div class="flex justify-between text-sm mb-2">
                                            <span class="text-gray-600">A8 策略优化</span>
                                            <span class="font-semibold text-gray-800">1,667 点数/月</span>
                                        </div>
                                        <div class="border-t border-gray-300 pt-2 flex justify-between">
                                            <span class="font-bold text-gray-800 text-sm">月均成本</span>
                                            <span class="font-bold text-purple-600">7,500 点数</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-xs text-gray-500">
                                    <p>💡 提示：点击任务卡片直接购买或管理配置</p>
                                    <p>⚡ 已激活的任务Alex正在为您执行</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab 5: 员工信息 -->
            <div class="tab-content bg-white rounded-b-2xl rounded-tr-2xl shadow-lg" id="profile">
                <div class="p-8">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-800 mb-4">Alex - 员工信息管理</h2>
                        <p class="text-gray-600">管理Alex的基本信息和工作配置</p>
                    </div>

                    <div class="max-w-4xl mx-auto">
                        <!-- 基本信息编辑 -->
                        <div class="space-y-6">
                            <!-- 员工头像和基本信息 -->
                            <div class="bg-gradient-to-br from-white to-purple-50 rounded-2xl p-6 border border-purple-200">
                                <h3 class="text-xl font-bold text-gray-800 mb-6">基本信息</h3>
                                
                                <div class="flex items-start gap-6">
                                    <!-- 头像上传区域 -->
                                    <div class="text-center">
                                        <div class="relative">
                                            <img id="employeeAvatar" src="assets/avatars/alex-director.svg" alt="Alex头像" class="w-24 h-24 rounded-full border-4 border-purple-300 shadow-lg">
                                            <button class="absolute bottom-0 right-0 w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center hover:bg-purple-700 transition-colors" onclick="triggerAvatarUpload()">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                            </button>
                                        </div>
                                        <input type="file" id="avatarUpload" accept="image/jpeg,image/png" class="hidden" onchange="handleAvatarUpload(this)">
                                        <button class="mt-3 text-sm text-purple-600 hover:underline" onclick="resetToDefaultAvatar()">
                                            恢复默认头像
                                        </button>
                                    </div>

                                    <!-- 基本信息表单 -->
                                    <div class="flex-1 space-y-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">员工姓名</label>
                                            <input type="text" id="employeeName" value="Alex" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" maxlength="20" oninput="updatePreview()">
                                            <p class="text-xs text-gray-500 mt-1">建议使用2-8个字符的名字</p>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">工作描述</label>
                                            <textarea id="employeeDescription" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" rows="3" maxlength="200" oninput="updatePreview()">我负责为您制定全球化战略，扫描识别最具潜力的市场机会，并持续监控各地区市场表现。通过数据驱动的决策支持，帮助您在全球化道路上做出最明智的选择。</textarea>
                                            <p class="text-xs text-gray-500 mt-1">200字以内，描述员工的主要工作职责</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 高级设置 -->
                                <div class="mt-6 bg-gray-50 rounded-xl p-4 border border-gray-200">
                                    <h4 class="text-sm font-semibold text-gray-700 mb-4">高级设置</h4>
                                    <div class="space-y-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <span class="font-medium text-gray-800">启用/停用</span>
                                                <p class="text-sm text-gray-600">控制员工是否继续工作</p>
                                            </div>
                                            <label class="relative inline-flex items-center cursor-pointer">
                                                <input type="checkbox" class="sr-only peer" checked>
                                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                                            </label>
                                        </div>

                                        <div class="flex items-center justify-between">
                                            <div>
                                                <span class="font-medium text-gray-800">邮件通知</span>
                                                <p class="text-sm text-gray-600">重要工作完成时发送邮件提醒</p>
                                            </div>
                                            <label class="relative inline-flex items-center cursor-pointer">
                                                <input type="checkbox" class="sr-only peer" checked>
                                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-6 flex gap-4">
                                    <button class="flex-1 bg-purple-600 text-white py-3 rounded-lg font-semibold hover:bg-purple-700 transition-all" onclick="saveEmployeeInfo()">
                                        保存修改
                                    </button>
                                    <button class="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg font-semibold hover:bg-gray-200 transition-all" onclick="resetEmployeeInfo()">
                                        重置
                                    </button>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        // Tab切换功能
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const targetTab = this.dataset.tab;
                
                // 更新tab按钮状态
                document.querySelectorAll('.tab-btn').forEach(b => {
                    b.classList.remove('text-purple-600', 'border-b-2', 'border-purple-600', 'bg-purple-50');
                    b.classList.add('text-gray-600');
                });
                this.classList.remove('text-gray-600');
                this.classList.add('text-purple-600', 'border-b-2', 'border-purple-600', 'bg-purple-50');
                
                // 更新tab内容
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById(targetTab).classList.add('active');
            });
        });

        // Feed筛选功能
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const filter = this.dataset.filter;
                
                // 更新筛选按钮状态
                document.querySelectorAll('.filter-btn').forEach(b => {
                    b.classList.remove('bg-purple-600', 'text-white');
                    b.classList.add('bg-gray-100', 'text-gray-700');
                });
                this.classList.remove('bg-gray-100', 'text-gray-700');
                this.classList.add('bg-purple-600', 'text-white');
                
                // 筛选feed项
                document.querySelectorAll('.feed-item').forEach(item => {
                    if (filter === 'all' || item.dataset.type === filter) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });

        // 统计时间维度切换
        function toggleTimeframe(type) {
            const periodSpan = document.getElementById(`${type}-period`);
            const valueSpan = document.getElementById(`${type}-value`);
            
            if (periodSpan.textContent === '本月') {
                periodSpan.textContent = '本年';
                // 更新为年度数据
                if (type === 'reports') valueSpan.textContent = '145';
                if (type === 'meetings') valueSpan.textContent = '96';
                if (type === 'value') valueSpan.textContent = '1,856.8';
            } else if (periodSpan.textContent === '本年') {
                periodSpan.textContent = '总计';
                // 更新为总计数据
                if (type === 'markets') valueSpan.textContent = '8';
                if (type === 'reports') valueSpan.textContent = '486';
                if (type === 'meetings') valueSpan.textContent = '268';
                if (type === 'value') valueSpan.textContent = '4,268.5';
            } else {
                periodSpan.textContent = '本月';
                // 更新为月度数据
                if (type === 'markets') valueSpan.textContent = '8';
                if (type === 'reports') valueSpan.textContent = '12';
                if (type === 'meetings') valueSpan.textContent = '8';
                if (type === 'value') valueSpan.textContent = '156.8';
            }
        }

        // 任务委托相关功能
        let selectedTasks = new Set();
        let selectedMode = '';
        
        const taskPrices = {
            'A1': 1200,
            'A2': 1500,
            'A14': 2000,
            'strategy-review': 1999
        };

        // 任务选择
        document.querySelectorAll('.task-option').forEach(option => {
            option.addEventListener('click', function() {
                const checkbox = this.querySelector('.task-checkbox');
                const artifact = this.dataset.artifact;
                const price = parseInt(this.dataset.price);
                
                checkbox.checked = !checkbox.checked;
                
                if (checkbox.checked) {
                    selectedTasks.add(artifact);
                    this.classList.add('border-purple-400', 'bg-purple-50');
                } else {
                    selectedTasks.delete(artifact);
                    this.classList.remove('border-purple-400', 'bg-purple-50');
                }
                
                updateTaskSummary();
            });
        });

        // 服务模式选择
        document.querySelectorAll('.service-mode').forEach(mode => {
            mode.addEventListener('click', function() {
                document.querySelectorAll('.service-mode').forEach(m => {
                    m.classList.remove('border-purple-400', 'border-orange-400');
                });
                
                selectedMode = this.dataset.mode;
                
                if (selectedMode === 'onetime') {
                    this.classList.add('border-purple-400');
                } else {
                    this.classList.add('border-orange-400');
                }
                
                updateTaskSummary();
            });
        });

        // 更新任务摘要
        function updateTaskSummary() {
            const count = selectedTasks.size;
            const estimatedTime = count > 0 ? `${count * 5}-${count * 7}天` : '-';
            
            let total = 0;
            selectedTasks.forEach(task => {
                total += taskPrices[task] || 0;
            });
            
            document.getElementById('selected-count').textContent = `${count}个`;
            document.getElementById('estimated-time').textContent = estimatedTime;
            document.getElementById('service-mode').textContent = selectedMode === 'onetime' ? '一次性项目' : selectedMode === 'subscription' ? '战略顾问订阅' : '未选择';
            
            // 更新价格
            document.getElementById('onetime-total').textContent = `¥${total.toLocaleString()}`;
            document.getElementById('subscription-total').textContent = `¥${(total + 5000).toLocaleString()}`;
            document.getElementById('total-cost').textContent = selectedMode === 'subscription' ? `¥${(total + 5000).toLocaleString()}` : `¥${total.toLocaleString()}`;
            
            // 更新按钮状态
            const assignBtn = document.getElementById('assignTaskBtn');
            if (count > 0 && selectedMode) {
                assignBtn.disabled = false;
                assignBtn.classList.remove('opacity-50');
            } else {
                assignBtn.disabled = true;
                assignBtn.classList.add('opacity-50');
            }
        }

        // 员工信息管理功能
        function uploadAvatar() {
            // 创建文件输入
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/jpeg,image/png';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file && file.size <= 2 * 1024 * 1024) { // 2MB限制
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        document.getElementById('currentAvatar').src = e.target.result;
                        alert('✅ 头像上传成功！');
                    };
                    reader.readAsDataURL(file);
                } else {
                    alert('❌ 文件大小不能超过2MB');
                }
            };
            input.click();
        }

        function resetAvatar() {
            document.getElementById('currentAvatar').src = 'assets/avatars/alex-director.svg';
            alert('✅ 头像已恢复为默认');
        }

        function saveProfile() {
            const name = document.getElementById('employeeName').value;
            const jobTitle = document.getElementById('jobTitle').value;
            const description = document.getElementById('workDescription').value;
            
            if (name.length < 2 || name.length > 20) {
                alert('❌ 员工姓名必须为2-20个字符');
                return;
            }
            
            // 模拟保存
            alert('✅ 员工信息保存成功！所有页面将同步更新。');
        }

        function resetProfileForm() {
            document.getElementById('employeeName').value = 'Alex';
            document.getElementById('jobTitle').value = '市场总监';
            document.getElementById('workDescription').value = '我负责为您制定全球化战略，扫描识别最具潜力的市场机会，并持续监控各地区市场表现。通过数据驱动的决策支持，帮助您在全球化道路上做出最明智的选择。';
            alert('✅ 表单已重置为默认值');
        }

        // 任务委托功能
        function openChargeModal() {
            alert('💰 充值功能开发中...\n\n支持充值选项：\n• 100点 (¥100)\n• 500点 (¥500)\n• 1000点 (¥1000)\n• 2000点 (¥2000)\n• 5000点 (¥5000)');
        }

        function claimFreeA2() {
            alert('🎉 A2全球商机扫描免费体验已激活！\n\nAlex将在24小时内开始执行全球32个市场的深度扫描。您将免费获得完整的分析报告。');
        }

        // A1、A8、A2 订阅管理弹窗（参考市场专员A9弹窗）
        function openSubscriptionConfigModal(artifactId) {
            const artifactNames = {
                'A1': '产业链定位与机会评估',
                'A8': '整合策略规划与动态优化'
            };

            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-6 max-w-2xl w-full mx-4 shadow-2xl">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center text-white">
                            <span class="text-xl">${artifactId}</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-800">${artifactNames[artifactId]} - 订阅管理</h3>
                            <p class="text-gray-600">按季度更新，年费2万点数</p>
                        </div>
                    </div>

                    <!-- 当前订阅状态 -->
                    <div class="bg-purple-50 p-4 rounded-xl border border-purple-200 mb-6">
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <div class="text-lg font-bold text-purple-800">当前订阅：20,000点数/年</div>
                                <div class="text-sm text-purple-600">有效期至：2024年12月31日</div>
                            </div>
                            <div class="text-2xl text-purple-600">⭐</div>
                        </div>
                        <div class="text-sm text-purple-700">下次更新：2024年4月1日（Q2季度更新）</div>
                    </div>

                    <!-- 订阅选项 -->
                    <div class="mb-6">
                        <h4 class="text-lg font-bold text-gray-800 mb-4">订阅选项</h4>
                        <div class="space-y-3">
                            <div class="p-4 border border-gray-200 rounded-xl hover:border-purple-300 transition-colors cursor-pointer" onclick="selectSubscriptionOption('renew-1')">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-semibold text-gray-800">续订 1 年</div>
                                        <div class="text-sm text-gray-600">继续享受季度更新服务</div>
                                    </div>
                                    <div class="text-lg font-bold text-green-600">20,000点数</div>
                                </div>
                            </div>
                            <div class="p-4 border border-gray-200 rounded-xl hover:border-purple-300 transition-colors cursor-pointer" onclick="selectSubscriptionOption('renew-2')">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-semibold text-gray-800">续订 2 年</div>
                                        <div class="text-sm text-gray-600">长期订阅享受95折优惠</div>
                                    </div>
                                    <div class="text-lg font-bold text-green-600">38,000点数 <span class="text-sm text-gray-500 line-through">40,000点数</span></div>
                                </div>
                            </div>
                            <div class="p-4 border border-red-200 bg-red-50 rounded-xl">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-semibold text-red-800">取消订阅</div>
                                        <div class="text-sm text-red-600">服务将在当前周期结束后停止</div>
                                    </div>
                                    <button class="text-red-600 hover:text-red-800 font-medium" onclick="cancelSubscription('${artifactId}')">
                                        取消订阅
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex gap-4">
                        <button class="flex-1 bg-gray-600 text-white py-3 rounded-lg font-semibold hover:bg-gray-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove();">
                            关闭
                        </button>
                        <button class="flex-1 bg-purple-600 text-white py-3 rounded-lg font-semibold hover:bg-purple-700 transition-all" onclick="saveSubscriptionConfig('${artifactId}')">
                            确认操作
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // A2 立即体验弹窗（参考A1/A8）
        function claimFreeA2() {
            openSubscriptionConfigModal('A2');
        }

        // A3 配置管理弹窗（参考市场专员A6弹窗，增加国家管理）
        function openA3ConfigModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-6 max-w-4xl w-full mx-4 shadow-2xl max-h-[90vh] overflow-y-auto">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="w-12 h-12 bg-emerald-500 rounded-xl flex items-center justify-center text-white">
                            <span class="text-xl">A3</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-800">目标市场研究与细分 - 配置管理</h3>
                            <p class="text-gray-600">管理监控国家列表，年费5万点数（含5个国家），每增加1个国家+1万点数/年</p>
                        </div>
                    </div>

                    <!-- 价格信息 -->
                    <div class="bg-emerald-50 p-4 rounded-xl border border-emerald-200 mb-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-lg font-bold text-emerald-800">当前配置：50,000点数/年</div>
                                <div class="text-sm text-emerald-600">已监控 5/5 个国家（基础套餐）| 每增加1个国家 +10,000点数/年</div>
                            </div>
                            <div class="text-2xl text-emerald-600">🗺️</div>
                        </div>
                    </div>

                    <!-- 国家列表 -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-bold text-gray-800">监控国家列表</h4>
                            <button class="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors" onclick="addNewCountry()">
                                + 添加国家
                            </button>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-xl overflow-hidden">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">国家</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">区域</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">市场规模</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-4 py-3 text-sm text-gray-800">🇩🇪 德国</td>
                                        <td class="px-4 py-3"><span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">欧洲</span></td>
                                        <td class="px-4 py-3 text-sm text-gray-600">大型成熟市场</td>
                                        <td class="px-4 py-3">
                                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">基础套餐</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 text-sm text-gray-800">🇺🇸 美国</td>
                                        <td class="px-4 py-3"><span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">北美</span></td>
                                        <td class="px-4 py-3 text-sm text-gray-600">全球最大市场</td>
                                        <td class="px-4 py-3">
                                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">基础套餐</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 text-sm text-gray-800">🇬🇧 英国</td>
                                        <td class="px-4 py-3"><span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">欧洲</span></td>
                                        <td class="px-4 py-3 text-sm text-gray-600">金融中心</td>
                                        <td class="px-4 py-3">
                                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">基础套餐</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 text-sm text-gray-800">🇫🇷 法国</td>
                                        <td class="px-4 py-3"><span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">欧洲</span></td>
                                        <td class="px-4 py-3 text-sm text-gray-600">奢侈品大国</td>
                                        <td class="px-4 py-3">
                                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">基础套餐</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 text-sm text-gray-800">🇯🇵 日本</td>
                                        <td class="px-4 py-3"><span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">亚洲</span></td>
                                        <td class="px-4 py-3 text-sm text-gray-600">技术先进市场</td>
                                        <td class="px-4 py-3">
                                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">基础套餐</span>
                                        </td>
                                    </tr>
                                    <tr class="bg-orange-50">
                                        <td class="px-4 py-3 text-sm text-gray-800">🇨🇦 加拿大</td>
                                        <td class="px-4 py-3"><span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">北美</span></td>
                                        <td class="px-4 py-3 text-sm text-gray-600">稳定增长市场</td>
                                        <td class="px-4 py-3">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm mr-3">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                            <span class="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full ml-2">+10,000点数/年</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex gap-4">
                        <button class="flex-1 bg-gray-600 text-white py-3 rounded-lg font-semibold hover:bg-gray-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove();">
                            关闭
                        </button>
                        <button class="flex-1 bg-emerald-600 text-white py-3 rounded-lg font-semibold hover:bg-emerald-700 transition-all" onclick="saveA3Config()">
                            保存配置
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 保存配置函数
        function saveSubscriptionConfig(artifactId) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl text-white">✅</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">订阅操作成功！</h3>
                        <div class="bg-purple-50 p-4 rounded-xl border border-purple-200 mb-6">
                            <div class="text-sm text-purple-700">${artifactId}订阅配置已更新</div>
                            <div class="text-xs text-purple-600 mt-2">订阅变更将在下个计费周期生效</div>
                        </div>
                        <button class="w-full bg-purple-600 text-white py-3 rounded-lg font-semibold hover:bg-purple-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove(); document.querySelectorAll('.fixed.inset-0')[0].remove();">
                            确定
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function saveA3Config() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl text-white">✅</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">配置保存成功！</h3>
                        <div class="bg-emerald-50 p-4 rounded-xl border border-emerald-200 mb-6">
                            <div class="text-sm text-emerald-700">A3国家配置已更新</div>
                            <div class="text-xs text-emerald-600 mt-2">市场监控将在1个工作日内调整</div>
                        </div>
                        <button class="w-full bg-emerald-600 text-white py-3 rounded-lg font-semibold hover:bg-emerald-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove(); document.querySelectorAll('.fixed.inset-0')[0].remove();">
                            确定
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 取消订阅确认
        function cancelSubscription(artifactId) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl text-white">⚠️</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">确认取消订阅？</h3>
                        <div class="bg-red-50 p-4 rounded-xl border border-red-200 mb-6">
                            <div class="text-sm text-red-700 mb-2">取消${artifactId}订阅后：</div>
                            <div class="text-xs text-red-600">• 服务将在当前周期结束后停止</div>
                            <div class="text-xs text-red-600">• 已支付费用不予退还</div>
                            <div class="text-xs text-red-600 mt-2 font-semibold">此操作可随时重新订阅</div>
                        </div>
                        <div class="flex gap-4">
                            <button class="flex-1 bg-gray-600 text-white py-3 rounded-lg font-semibold hover:bg-gray-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove();">
                                取消
                            </button>
                            <button class="flex-1 bg-red-600 text-white py-3 rounded-lg font-semibold hover:bg-red-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove(); document.querySelectorAll('.fixed.inset-0')[0].remove(); alert('订阅已取消（演示功能）');">
                                确认取消
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }
    </script>
</body>
</html>
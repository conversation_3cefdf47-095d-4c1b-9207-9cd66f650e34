<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>雇佣市场专员 - Foxu AI Team</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="custom.css">
    
    <style>
        .step-indicator {
            transition: all 0.3s ease;
        }
        
        .step-indicator.completed {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }
        
        .step-indicator.active {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            transform: scale(1.1);
        }
        
        .step-indicator.pending {
            background: #f3f4f6;
            color: #6b7280;
        }
        
        .avatar-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .avatar-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .avatar-card.selected {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: linear-gradient(135deg, #eff6ff, #dbeafe);
        }
        
        .wizard-step {
            display: none;
            transition: all 0.3s ease;
        }
        
        .wizard-step.active {
            display: block;
        }

        .suggested-name {
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .suggested-name:hover {
            background-color: #dbeafe;
            color: #1d4ed8;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/80 backdrop-blur-xl border-b border-gray-100 px-4 rounded-b-3xl shadow-lg">
        <div class="max-w-7xl mx-auto flex justify-between items-center h-16">
            <div class="logo">
                <a href="index.html" class="text-2xl font-black bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent hover:from-purple-600 hover:to-blue-600 transition-all duration-300 cursor-pointer">Foxu AI Team</a>
            </div>
            <nav class="flex items-center gap-8">
                <div class="flex items-center gap-3">
                    <div class="flex items-center gap-2">
                        <div class="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                        <span class="text-gray-700 font-semibold text-sm">David Chen</span>
                    </div>
                    <img src="assets/avatars/david-chen.svg" alt="用户头像" class="w-8 h-8 rounded-full border-2 border-blue-200">
                </div>
            </nav>
        </div>
    </header>

    <main class="max-w-7xl mx-auto px-4 py-8">
        <!-- Progress Indicator -->
        <div class="flex justify-center mb-8">
            <div class="flex items-center gap-4">
                <div class="flex items-center gap-2">
                    <div class="step-indicator w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold active" id="step1-indicator">1</div>
                    <span class="text-gray-700 font-medium">选择形象</span>
                </div>
                <div class="w-12 h-px bg-gray-300"></div>
                <div class="flex items-center gap-2">
                    <div class="step-indicator w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold pending" id="step2-indicator">2</div>
                    <span class="text-gray-600">员工命名</span>
                </div>
                <div class="w-12 h-px bg-gray-300"></div>
                <div class="flex items-center gap-2">
                    <div class="step-indicator w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold pending" id="step3-indicator">3</div>
                    <span class="text-gray-600">完成雇佣</span>
                </div>
            </div>
        </div>

        <!-- Step 1: Avatar Selection -->
        <div id="step1" class="wizard-step active">
            <div class="text-center mb-8">
                <h1 class="text-4xl font-black bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">选择员工形象</h1>
                <p class="text-gray-600 text-lg">从我们精心设计的AI专员中选择一位加入您的团队</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
                <!-- Avatar 1: Emma-style -->
                <div class="avatar-card bg-white p-6 rounded-2xl border border-gray-200 shadow-lg" data-avatar="emma" data-default-name="Emma">
                    <div class="text-center mb-6">
                        <img src="assets/avatars/emma-germany.svg" alt="专员形象" class="w-24 h-24 mx-auto rounded-full border-4 border-green-300 shadow-lg mb-4">
                        <h3 class="text-xl font-bold text-gray-800 mb-2">专业型专员</h3>
                        <p class="text-gray-600 text-sm">专注、严谨，擅长深度市场分析</p>
                    </div>
                    
                    <div class="space-y-3 mb-6">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">专业技能</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">沟通能力</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-gray-300 rounded-full"></span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">创新思维</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-gray-300 rounded-full"></span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 p-4 rounded-lg border border-green-200 mb-4">
                        <div class="flex flex-wrap gap-2">
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">市场分析</span>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">竞品监控</span>
                            <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">数据洞察</span>
                        </div>
                    </div>

                    <button class="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-3 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all shadow-lg" onclick="selectAvatar('emma', '专业型专员')">
                        选择此形象
                    </button>
                </div>

                <!-- Avatar 2: Michael-style -->
                <div class="avatar-card bg-white p-6 rounded-2xl border border-gray-200 shadow-lg" data-avatar="michael" data-default-name="Michael">
                    <div class="text-center mb-6">
                        <img src="assets/avatars/michael-usa.svg" alt="专员形象" class="w-24 h-24 mx-auto rounded-full border-4 border-red-300 shadow-lg mb-4">
                        <h3 class="text-xl font-bold text-gray-800 mb-2">活力型专员</h3>
                        <p class="text-gray-600 text-sm">积极主动，擅长发现市场机会</p>
                    </div>
                    
                    <div class="space-y-3 mb-6">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">专业技能</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-gray-300 rounded-full"></span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">沟通能力</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">创新思维</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-red-50 p-4 rounded-lg border border-red-200 mb-4">
                        <div class="flex flex-wrap gap-2">
                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">机会发现</span>
                            <span class="px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">渠道拓展</span>
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">创新策略</span>
                        </div>
                    </div>

                    <button class="w-full bg-gradient-to-r from-red-600 to-orange-600 text-white py-3 rounded-lg font-semibold hover:from-red-700 hover:to-orange-700 transition-all shadow-lg" onclick="selectAvatar('michael', '活力型专员')">
                        选择此形象
                    </button>
                </div>

                <!-- Avatar 3: Sarah-style -->
                <div class="avatar-card bg-white p-6 rounded-2xl border border-gray-200 shadow-lg" data-avatar="sarah" data-default-name="Sarah">
                    <div class="text-center mb-6">
                        <img src="assets/avatars/sarah-uk.svg" alt="专员形象" class="w-24 h-24 mx-auto rounded-full border-4 border-blue-300 shadow-lg mb-4">
                        <h3 class="text-xl font-bold text-gray-800 mb-2">平衡型专员</h3>
                        <p class="text-gray-600 text-sm">全面发展，适应各种市场环境</p>
                    </div>
                    
                    <div class="space-y-3 mb-6">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">专业技能</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-gray-300 rounded-full"></span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">沟通能力</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">创新思维</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-gray-300 rounded-full"></span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-4">
                        <div class="flex flex-wrap gap-2">
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">综合分析</span>
                            <span class="px-2 py-1 bg-indigo-100 text-indigo-800 text-xs font-medium rounded-full">策略规划</span>
                            <span class="px-2 py-1 bg-cyan-100 text-cyan-800 text-xs font-medium rounded-full">执行监控</span>
                        </div>
                    </div>

                    <button class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all shadow-lg" onclick="selectAvatar('sarah', '平衡型专员')">
                        选择此形象
                    </button>
                </div>

                <!-- Avatar 4: 新形象1 -->
                <div class="avatar-card bg-white p-6 rounded-2xl border border-gray-200 shadow-lg" data-avatar="lisa" data-default-name="Lisa">
                    <div class="text-center mb-6">
                        <div class="w-24 h-24 mx-auto rounded-full border-4 border-purple-300 shadow-lg mb-4 bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center">
                            <span class="text-4xl">👩‍💼</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">创新型专员</h3>
                        <p class="text-gray-600 text-sm">思维活跃，擅长创新营销策略</p>
                    </div>
                    
                    <div class="space-y-3 mb-6">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">专业技能</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-gray-300 rounded-full"></span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">沟通能力</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">创新思维</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-purple-50 p-4 rounded-lg border border-purple-200 mb-4">
                        <div class="flex flex-wrap gap-2">
                            <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">创新营销</span>
                            <span class="px-2 py-1 bg-pink-100 text-pink-800 text-xs font-medium rounded-full">品牌策略</span>
                            <span class="px-2 py-1 bg-fuchsia-100 text-fuchsia-800 text-xs font-medium rounded-full">用户体验</span>
                        </div>
                    </div>

                    <button class="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all shadow-lg" onclick="selectAvatar('lisa', '创新型专员')">
                        选择此形象
                    </button>
                </div>

                <!-- Avatar 5: 新形象2 -->
                <div class="avatar-card bg-white p-6 rounded-2xl border border-gray-200 shadow-lg" data-avatar="alex" data-default-name="Alex">
                    <div class="text-center mb-6">
                        <div class="w-24 h-24 mx-auto rounded-full border-4 border-teal-300 shadow-lg mb-4 bg-gradient-to-br from-teal-100 to-cyan-100 flex items-center justify-center">
                            <span class="text-4xl">🧑‍💻</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">技术型专员</h3>
                        <p class="text-gray-600 text-sm">数据驱动，擅长技术产品分析</p>
                    </div>
                    
                    <div class="space-y-3 mb-6">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">专业技能</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">沟通能力</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-gray-300 rounded-full"></span>
                                <span class="w-2 h-2 bg-gray-300 rounded-full"></span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">创新思维</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-gray-300 rounded-full"></span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-teal-50 p-4 rounded-lg border border-teal-200 mb-4">
                        <div class="flex flex-wrap gap-2">
                            <span class="px-2 py-1 bg-teal-100 text-teal-800 text-xs font-medium rounded-full">数据分析</span>
                            <span class="px-2 py-1 bg-cyan-100 text-cyan-800 text-xs font-medium rounded-full">技术评估</span>
                            <span class="px-2 py-1 bg-emerald-100 text-emerald-800 text-xs font-medium rounded-full">产品洞察</span>
                        </div>
                    </div>

                    <button class="w-full bg-gradient-to-r from-teal-600 to-cyan-600 text-white py-3 rounded-lg font-semibold hover:from-teal-700 hover:to-cyan-700 transition-all shadow-lg" onclick="selectAvatar('alex', '技术型专员')">
                        选择此形象
                    </button>
                </div>

                <!-- Avatar 6: 新形象3 -->
                <div class="avatar-card bg-white p-6 rounded-2xl border border-gray-200 shadow-lg" data-avatar="david" data-default-name="David">
                    <div class="text-center mb-6">
                        <div class="w-24 h-24 mx-auto rounded-full border-4 border-amber-300 shadow-lg mb-4 bg-gradient-to-br from-amber-100 to-orange-100 flex items-center justify-center">
                            <span class="text-4xl">👨‍🎯</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">战略型专员</h3>
                        <p class="text-gray-600 text-sm">全局思维，擅长战略规划分析</p>
                    </div>
                    
                    <div class="space-y-3 mb-6">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">专业技能</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">沟通能力</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-gray-300 rounded-full"></span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">创新思维</span>
                            <div class="flex gap-1">
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-amber-50 p-4 rounded-lg border border-amber-200 mb-4">
                        <div class="flex flex-wrap gap-2">
                            <span class="px-2 py-1 bg-amber-100 text-amber-800 text-xs font-medium rounded-full">战略规划</span>
                            <span class="px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">市场定位</span>
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">竞争策略</span>
                        </div>
                    </div>

                    <button class="w-full bg-gradient-to-r from-amber-600 to-orange-600 text-white py-3 rounded-lg font-semibold hover:from-amber-700 hover:to-orange-700 transition-all shadow-lg" onclick="selectAvatar('david', '战略型专员')">
                        选择此形象
                    </button>
                </div>
            </div>
        </div>

        <!-- Step 2: Employee Naming -->
        <div id="step2" class="wizard-step">
            <div class="max-w-2xl mx-auto">
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-black bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">为您的专员命名</h1>
                    <p class="text-gray-600 text-lg">给她一个独特的名字，让她成为您团队的一员</p>
                </div>

                <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
                    <div class="text-center mb-8">
                        <img id="selected-avatar-preview" src="assets/avatars/emma-germany.svg" alt="选中的专员" class="w-24 h-24 mx-auto rounded-full border-4 border-blue-300 shadow-lg mb-4">
                        <div class="text-lg text-gray-600" id="selected-avatar-type">专业型专员</div>
                    </div>

                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">专员姓名</label>
                            <input type="text" id="employee-name" class="w-full p-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg" placeholder="输入自定义姓名..." maxlength="20">
                            <p class="text-xs text-gray-500 mt-2">建议使用2-8个字符的名字</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">或从建议中选择</label>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-3" id="suggested-names">
                                <!-- 建议名称将通过JavaScript动态生成 -->
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">个性化描述 (可选)</label>
                            <textarea id="employee-description" class="w-full p-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows="3" placeholder="描述您希望这位专员的工作重点或特殊要求..." maxlength="200"></textarea>
                            <p class="text-xs text-gray-500 mt-2">这将帮助专员更好地理解您的需求</p>
                        </div>

                        <!-- 实时预览 -->
                        <div class="bg-blue-50 p-4 rounded-xl border border-blue-200">
                            <h4 class="font-semibold text-blue-800 mb-2">实时预览</h4>
                            <div class="flex items-center gap-3">
                                <img id="preview-avatar" src="assets/avatars/emma-germany.svg" alt="预览" class="w-12 h-12 rounded-full border-2 border-blue-300">
                                <div>
                                    <div class="font-medium text-gray-800">
                                        <span id="preview-name">Emma</span> - 市场专员
                                    </div>
                                    <div class="text-sm text-gray-600" id="preview-description">专注于市场分析和竞品监控</div>
                                </div>
                            </div>
                        </div>

                        <div class="flex gap-4">
                            <button class="flex-1 bg-gray-100 text-gray-700 py-3 rounded-xl font-semibold hover:bg-gray-200 transition-all" onclick="goBack()">
                                ← 返回选择
                            </button>
                            <button class="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all" onclick="nextToComplete()">
                                确认并继续 →
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 3: Complete Hiring -->
        <div id="step3" class="wizard-step">
            <div class="max-w-2xl mx-auto">
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-black bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent mb-4">雇佣完成！</h1>
                    <p class="text-gray-600 text-lg">您的新团队成员已经准备就绪</p>
                </div>

                <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
                    <div class="text-center mb-8">
                        <div class="w-24 h-24 mx-auto mb-6 relative">
                            <img id="final-avatar" src="assets/avatars/emma-germany.svg" alt="新员工" class="w-24 h-24 rounded-full border-4 border-green-300 shadow-lg">
                            <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center border-4 border-white">
                                <span class="text-white text-sm">✓</span>
                            </div>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-2" id="final-name">Emma</h3>
                        <p class="text-gray-600 mb-4">市场专员 · 已加入您的团队</p>
                        <div class="inline-flex items-center gap-2 px-4 py-2 bg-orange-100 text-orange-800 rounded-full text-sm font-medium">
                            <span class="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></span>
                            等待分配国家
                        </div>
                    </div>

                    <div class="bg-green-50 p-6 rounded-xl border border-green-200 mb-6">
                        <h4 class="font-semibold text-green-800 mb-3">🎉 雇佣成功!</h4>
                        <div class="space-y-2 text-sm text-green-700">
                            <p>• 新员工已出现在您的首页团队列表中</p>
                            <p>• 当前状态：等待分配目标市场国家</p>
                            <p>• 工作任务配置可在员工工作区的"任务委托"中设置</p>
                            <p>• 享受7天试用期，体验完整功能</p>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <button class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 rounded-xl font-bold text-lg hover:from-blue-700 hover:to-purple-700 transition-all shadow-lg" onclick="assignCountry()">
                            🌍 立即分配国家
                        </button>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <button class="bg-gray-100 text-gray-700 py-3 rounded-xl font-semibold hover:bg-gray-200 transition-all" onclick="goToHomepage()">
                                回到首页
                            </button>
                            <button class="bg-green-600 text-white py-3 rounded-xl font-semibold hover:bg-green-700 transition-all" onclick="goToWorkspace()">
                                进入工作区
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Enhanced Country Assignment Modal -->
    <div id="countryModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-2xl p-8 max-w-lg w-full mx-4 shadow-2xl">
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">🌍</span>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-2">分配目标国家</h3>
                <p class="text-gray-600">为您的专员选择一个专注的目标市场</p>
            </div>

            <!-- Employee Preview -->
            <div class="bg-gray-50 p-4 rounded-xl mb-6">
                <div class="flex items-center gap-3">
                    <img id="modalEmployeeAvatar" src="assets/avatars/emma-germany.svg" alt="员工头像" class="w-12 h-12 rounded-full border-2 border-blue-300">
                    <div>
                        <div class="font-semibold text-gray-800" id="modalEmployeeName">Emma</div>
                        <div class="text-sm text-gray-600">市场专员 · 即将分配到</div>
                    </div>
                </div>
            </div>

            <div class="space-y-4">
                <!-- Region Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">按区域筛选</label>
                    <div class="flex flex-wrap gap-2">
                        <button class="region-filter px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium hover:bg-blue-200 transition-colors active" data-region="all">
                            全部
                        </button>
                        <button class="region-filter px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200 transition-colors" data-region="europe">
                            🇪🇺 欧洲
                        </button>
                        <button class="region-filter px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200 transition-colors" data-region="americas">
                            🌎 美洲
                        </button>
                        <button class="region-filter px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200 transition-colors" data-region="asia">
                            🌏 亚洲
                        </button>
                        <button class="region-filter px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200 transition-colors" data-region="oceania">
                            🌊 大洋洲
                        </button>
                    </div>
                </div>

                <!-- Search Input -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">搜索国家</label>
                    <div class="relative">
                        <input type="text" id="countrySearch" class="w-full p-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="输入国家名称...">
                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Country Grid -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">选择国家</label>
                    <div class="max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-2">
                        <div id="countryGrid" class="grid grid-cols-1 gap-1">
                            <!-- Europe -->
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="germany" data-region="europe">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇩🇪</span>
                                    <div>
                                        <div class="font-medium text-gray-800">德国</div>
                                        <div class="text-xs text-gray-500">欧洲 · 经济强国</div>
                                    </div>
                                </div>
                            </div>
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="uk" data-region="europe">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇬🇧</span>
                                    <div>
                                        <div class="font-medium text-gray-800">英国</div>
                                        <div class="text-xs text-gray-500">欧洲 · 金融中心</div>
                                    </div>
                                </div>
                            </div>
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="france" data-region="europe">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇫🇷</span>
                                    <div>
                                        <div class="font-medium text-gray-800">法国</div>
                                        <div class="text-xs text-gray-500">欧洲 · 奢侈品大国</div>
                                    </div>
                                </div>
                            </div>
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="italy" data-region="europe">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇮🇹</span>
                                    <div>
                                        <div class="font-medium text-gray-800">意大利</div>
                                        <div class="text-xs text-gray-500">欧洲 · 设计之都</div>
                                    </div>
                                </div>
                            </div>
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="spain" data-region="europe">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇪🇸</span>
                                    <div>
                                        <div class="font-medium text-gray-800">西班牙</div>
                                        <div class="text-xs text-gray-500">欧洲 · 文化中心</div>
                                    </div>
                                </div>
                            </div>
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="netherlands" data-region="europe">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇳🇱</span>
                                    <div>
                                        <div class="font-medium text-gray-800">荷兰</div>
                                        <div class="text-xs text-gray-500">欧洲 · 贸易枢纽</div>
                                    </div>
                                </div>
                            </div>
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="switzerland" data-region="europe">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇨🇭</span>
                                    <div>
                                        <div class="font-medium text-gray-800">瑞士</div>
                                        <div class="text-xs text-gray-500">欧洲 · 精品制造</div>
                                    </div>
                                </div>
                            </div>
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="sweden" data-region="europe">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇸🇪</span>
                                    <div>
                                        <div class="font-medium text-gray-800">瑞典</div>
                                        <div class="text-xs text-gray-500">欧洲 · 创新国度</div>
                                    </div>
                                </div>
                            </div>
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="norway" data-region="europe">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇳🇴</span>
                                    <div>
                                        <div class="font-medium text-gray-800">挪威</div>
                                        <div class="text-xs text-gray-500">欧洲 · 富裕市场</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Americas -->
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="usa" data-region="americas">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇺🇸</span>
                                    <div>
                                        <div class="font-medium text-gray-800">美国</div>
                                        <div class="text-xs text-gray-500">美洲 · 全球最大市场</div>
                                    </div>
                                </div>
                            </div>
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="canada" data-region="americas">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇨🇦</span>
                                    <div>
                                        <div class="font-medium text-gray-800">加拿大</div>
                                        <div class="text-xs text-gray-500">美洲 · 稳定增长</div>
                                    </div>
                                </div>
                            </div>
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="brazil" data-region="americas">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇧🇷</span>
                                    <div>
                                        <div class="font-medium text-gray-800">巴西</div>
                                        <div class="text-xs text-gray-500">美洲 · 新兴市场</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Asia -->
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="japan" data-region="asia">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇯🇵</span>
                                    <div>
                                        <div class="font-medium text-gray-800">日本</div>
                                        <div class="text-xs text-gray-500">亚洲 · 技术先进</div>
                                    </div>
                                </div>
                            </div>
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="korea" data-region="asia">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇰🇷</span>
                                    <div>
                                        <div class="font-medium text-gray-800">韩国</div>
                                        <div class="text-xs text-gray-500">亚洲 · 文化输出</div>
                                    </div>
                                </div>
                            </div>
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="singapore" data-region="asia">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇸🇬</span>
                                    <div>
                                        <div class="font-medium text-gray-800">新加坡</div>
                                        <div class="text-xs text-gray-500">亚洲 · 商业枢纽</div>
                                    </div>
                                </div>
                            </div>
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="india" data-region="asia">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇮🇳</span>
                                    <div>
                                        <div class="font-medium text-gray-800">印度</div>
                                        <div class="text-xs text-gray-500">亚洲 · 快速增长</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Oceania -->
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="australia" data-region="oceania">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇦🇺</span>
                                    <div>
                                        <div class="font-medium text-gray-800">澳大利亚</div>
                                        <div class="text-xs text-gray-500">大洋洲 · 资源丰富</div>
                                    </div>
                                </div>
                            </div>
                            <div class="country-option p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors border border-transparent hover:border-blue-200" data-country="newzealand" data-region="oceania">
                                <div class="flex items-center gap-3">
                                    <span class="text-2xl">🇳🇿</span>
                                    <div>
                                        <div class="font-medium text-gray-800">新西兰</div>
                                        <div class="text-xs text-gray-500">大洋洲 · 优质生活</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Selected Country Preview -->
                <div id="selectedCountryPreview" class="hidden bg-blue-50 p-4 rounded-xl border border-blue-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <span id="selectedCountryFlag" class="text-2xl"></span>
                            <div>
                                <div class="font-semibold text-blue-800" id="selectedCountryName"></div>
                                <div class="text-sm text-blue-600" id="selectedCountryDesc"></div>
                            </div>
                        </div>
                        <button class="text-blue-600 hover:text-blue-800 text-sm font-medium" onclick="clearCountrySelection()">
                            清除选择
                        </button>
                    </div>
                </div>

                <div class="flex gap-4 mt-6">
                    <button class="flex-1 bg-gray-100 text-gray-700 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-all" onclick="closeCountryModal()">
                        稍后分配
                    </button>
                    <button id="confirmCountryBtn" class="flex-1 bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed" onclick="confirmCountry()" disabled>
                        确认分配
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedAvatar = '';
        let selectedAvatarType = '';
        let selectedAvatarImage = '';
        let currentStep = 1;
        let finalEmployeeName = '';
        let finalEmployeeDescription = '';

        const avatarData = {
            'emma': {
                image: 'assets/avatars/emma-germany.svg',
                type: '专业型专员',
                suggestedNames: ['Emma', 'Anna', 'Sophie', 'Clara']
            },
            'michael': {
                image: 'assets/avatars/michael-usa.svg', 
                type: '活力型专员',
                suggestedNames: ['Michael', 'David', 'James', 'Ryan']
            },
            'sarah': {
                image: 'assets/avatars/sarah-uk.svg',
                type: '平衡型专员', 
                suggestedNames: ['Sarah', 'Emily', 'Jessica', 'Amy']
            },
            'lisa': {
                image: 'assets/avatars/emma-germany.svg', // 临时使用现有头像
                type: '创新型专员',
                suggestedNames: ['Lisa', 'Nina', 'Mia', 'Eva']
            },
            'alex': {
                image: 'assets/avatars/michael-usa.svg', // 临时使用现有头像
                type: '技术型专员',
                suggestedNames: ['Alex', 'Sam', 'Jordan', 'Taylor']
            },
            'david': {
                image: 'assets/avatars/alex-director.svg', // 使用总监头像作为临时替代
                type: '战略型专员',
                suggestedNames: ['David', 'Mark', 'Chris', 'Kevin']
            }
        };

        function selectAvatar(avatar, type) {
            selectedAvatar = avatar;
            selectedAvatarType = type;
            selectedAvatarImage = avatarData[avatar].image;
            
            // Update UI
            document.querySelectorAll('.avatar-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-avatar="${avatar}"]`).classList.add('selected');
            
            // Continue to next step after selection
            setTimeout(() => {
                goToStep(2);
            }, 500);
        }

        function goToStep(step) {
            // Hide all steps
            document.querySelectorAll('.wizard-step').forEach(el => {
                el.classList.remove('active');
            });
            
            // Show target step
            document.getElementById(`step${step}`).classList.add('active');
            
            // Update step indicators
            updateStepIndicators(step);
            
            currentStep = step;
            
            if (step === 2) {
                setupNamingStep();
            }
        }

        function updateStepIndicators(activeStep) {
            for (let i = 1; i <= 3; i++) {
                const indicator = document.getElementById(`step${i}-indicator`);
                indicator.classList.remove('active', 'completed', 'pending');
                
                if (i < activeStep) {
                    indicator.classList.add('completed');
                    indicator.textContent = '✓';
                } else if (i === activeStep) {
                    indicator.classList.add('active');
                    indicator.textContent = i;
                } else {
                    indicator.classList.add('pending');
                    indicator.textContent = i;
                }
            }
        }

        function setupNamingStep() {
            // Update preview images
            document.getElementById('selected-avatar-preview').src = selectedAvatarImage;
            document.getElementById('selected-avatar-type').textContent = selectedAvatarType;
            document.getElementById('preview-avatar').src = selectedAvatarImage;
            
            // Generate suggested names
            const suggestedNamesContainer = document.getElementById('suggested-names');
            suggestedNamesContainer.innerHTML = '';
            
            avatarData[selectedAvatar].suggestedNames.forEach(name => {
                const nameButton = document.createElement('button');
                nameButton.className = 'suggested-name px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-blue-50 hover:text-blue-600 transition-colors';
                nameButton.textContent = name;
                nameButton.onclick = () => selectSuggestedName(name);
                suggestedNamesContainer.appendChild(nameButton);
            });
            
            // Set default name
            const defaultName = avatarData[selectedAvatar].suggestedNames[0];
            document.getElementById('employee-name').value = defaultName;
            updatePreview();
            
            // Add event listeners
            document.getElementById('employee-name').addEventListener('input', updatePreview);
            document.getElementById('employee-description').addEventListener('input', updatePreview);
        }

        function selectSuggestedName(name) {
            document.getElementById('employee-name').value = name;
            updatePreview();
        }

        function updatePreview() {
            const name = document.getElementById('employee-name').value || avatarData[selectedAvatar].suggestedNames[0];
            const description = document.getElementById('employee-description').value || '专注于市场分析和竞品监控';
            
            document.getElementById('preview-name').textContent = name;
            document.getElementById('preview-description').textContent = description;
        }

        function goBack() {
            goToStep(1);
        }

        function nextToComplete() {
            finalEmployeeName = document.getElementById('employee-name').value || avatarData[selectedAvatar].suggestedNames[0];
            finalEmployeeDescription = document.getElementById('employee-description').value;
            
            // Setup completion step
            document.getElementById('final-avatar').src = selectedAvatarImage;
            document.getElementById('final-name').textContent = finalEmployeeName;
            
            goToStep(3);
        }

        let selectedCountryData = null;

        const countryMappings = {
            'germany': { flag: '🇩🇪', name: '德国', desc: '欧洲 · 经济强国', region: 'europe' },
            'usa': { flag: '🇺🇸', name: '美国', desc: '美洲 · 全球最大市场', region: 'americas' },
            'uk': { flag: '🇬🇧', name: '英国', desc: '欧洲 · 金融中心', region: 'europe' },
            'france': { flag: '🇫🇷', name: '法国', desc: '欧洲 · 奢侈品大国', region: 'europe' },
            'japan': { flag: '🇯🇵', name: '日本', desc: '亚洲 · 技术先进', region: 'asia' },
            'australia': { flag: '🇦🇺', name: '澳大利亚', desc: '大洋洲 · 资源丰富', region: 'oceania' },
            'canada': { flag: '🇨🇦', name: '加拿大', desc: '美洲 · 稳定增长', region: 'americas' },
            'italy': { flag: '🇮🇹', name: '意大利', desc: '欧洲 · 设计之都', region: 'europe' },
            'spain': { flag: '🇪🇸', name: '西班牙', desc: '欧洲 · 文化中心', region: 'europe' },
            'netherlands': { flag: '🇳🇱', name: '荷兰', desc: '欧洲 · 贸易枢纽', region: 'europe' },
            'switzerland': { flag: '🇨🇭', name: '瑞士', desc: '欧洲 · 精品制造', region: 'europe' },
            'sweden': { flag: '🇸🇪', name: '瑞典', desc: '欧洲 · 创新国度', region: 'europe' },
            'norway': { flag: '🇳🇴', name: '挪威', desc: '欧洲 · 富裕市场', region: 'europe' },
            'singapore': { flag: '🇸🇬', name: '新加坡', desc: '亚洲 · 商业枢纽', region: 'asia' },
            'korea': { flag: '🇰🇷', name: '韩国', desc: '亚洲 · 文化输出', region: 'asia' },
            'brazil': { flag: '🇧🇷', name: '巴西', desc: '美洲 · 新兴市场', region: 'americas' },
            'india': { flag: '🇮🇳', name: '印度', desc: '亚洲 · 快速增长', region: 'asia' },
            'newzealand': { flag: '🇳🇿', name: '新西兰', desc: '大洋洲 · 优质生活', region: 'oceania' }
        };

        function assignCountry() {
            document.getElementById('countryModal').classList.remove('hidden');
            document.getElementById('countryModal').classList.add('flex');
            
            // Update employee preview
            document.getElementById('modalEmployeeAvatar').src = selectedAvatarImage;
            document.getElementById('modalEmployeeName').textContent = document.getElementById('employee-name').value || avatarData[selectedAvatar].suggestedNames[0];
            
            // Initialize region filter functionality
            initializeRegionFilter();
            
            // Initialize search functionality
            initializeCountrySearch();
            
            // Initialize country selection
            initializeCountrySelection();
        }

        function initializeRegionFilter() {
            const regionFilters = document.querySelectorAll('.region-filter');
            const countryOptions = document.querySelectorAll('.country-option');
            
            regionFilters.forEach(filter => {
                filter.addEventListener('click', function() {
                    const selectedRegion = this.dataset.region;
                    
                    // Update filter button states
                    regionFilters.forEach(f => {
                        f.classList.remove('active', 'bg-blue-100', 'text-blue-700');
                        f.classList.add('bg-gray-100', 'text-gray-700');
                    });
                    this.classList.remove('bg-gray-100', 'text-gray-700');
                    this.classList.add('active', 'bg-blue-100', 'text-blue-700');
                    
                    // Filter countries
                    countryOptions.forEach(option => {
                        const countryRegion = option.dataset.region;
                        if (selectedRegion === 'all' || countryRegion === selectedRegion) {
                            option.style.display = 'block';
                        } else {
                            option.style.display = 'none';
                        }
                    });
                });
            });
        }

        function initializeCountrySearch() {
            const searchInput = document.getElementById('countrySearch');
            const countryOptions = document.querySelectorAll('.country-option');
            
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                
                countryOptions.forEach(option => {
                    const countryName = option.querySelector('.font-medium').textContent.toLowerCase();
                    const countryDesc = option.querySelector('.text-xs').textContent.toLowerCase();
                    
                    if (countryName.includes(searchTerm) || countryDesc.includes(searchTerm)) {
                        option.style.display = 'block';
                    } else {
                        option.style.display = 'none';
                    }
                });
            });
        }

        function initializeCountrySelection() {
            const countryOptions = document.querySelectorAll('.country-option');
            
            countryOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const countryCode = this.dataset.country;
                    selectedCountryData = countryMappings[countryCode];
                    
                    // Update selection states
                    countryOptions.forEach(opt => {
                        opt.classList.remove('bg-blue-100', 'border-blue-300');
                        opt.classList.add('border-transparent');
                    });
                    
                    this.classList.remove('border-transparent');
                    this.classList.add('bg-blue-100', 'border-blue-300');
                    
                    // Update preview
                    updateSelectedCountryPreview();
                    
                    // Enable confirm button
                    document.getElementById('confirmCountryBtn').disabled = false;
                });
            });
        }

        function updateSelectedCountryPreview() {
            if (selectedCountryData) {
                document.getElementById('selectedCountryFlag').textContent = selectedCountryData.flag;
                document.getElementById('selectedCountryName').textContent = selectedCountryData.name;
                document.getElementById('selectedCountryDesc').textContent = selectedCountryData.desc;
                document.getElementById('selectedCountryPreview').classList.remove('hidden');
            }
        }

        function clearCountrySelection() {
            selectedCountryData = null;
            
            // Clear visual selection
            const countryOptions = document.querySelectorAll('.country-option');
            countryOptions.forEach(opt => {
                opt.classList.remove('bg-blue-100', 'border-blue-300');
                opt.classList.add('border-transparent');
            });
            
            // Hide preview
            document.getElementById('selectedCountryPreview').classList.add('hidden');
            
            // Disable confirm button
            document.getElementById('confirmCountryBtn').disabled = true;
        }

        function closeCountryModal() {
            document.getElementById('countryModal').classList.add('hidden');
            document.getElementById('countryModal').classList.remove('flex');
            
            // Reset modal state
            clearCountrySelection();
            document.getElementById('countrySearch').value = '';
            
            // Reset region filter
            const regionFilters = document.querySelectorAll('.region-filter');
            regionFilters.forEach(f => {
                f.classList.remove('active', 'bg-blue-100', 'text-blue-700');
                f.classList.add('bg-gray-100', 'text-gray-700');
            });
            regionFilters[0].classList.remove('bg-gray-100', 'text-gray-700');
            regionFilters[0].classList.add('active', 'bg-blue-100', 'text-blue-700');
            
            // Show all countries
            const countryOptions = document.querySelectorAll('.country-option');
            countryOptions.forEach(option => {
                option.style.display = 'block';
            });
        }

        function confirmCountry() {
            if (selectedCountryData) {
                const employeeName = finalEmployeeName || document.getElementById('employee-name').value || avatarData[selectedAvatar].suggestedNames[0];
                
                // Show success message with enhanced styling
                const successModal = document.createElement('div');
                successModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                successModal.innerHTML = `
                    <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-2xl text-white">✅</span>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-800 mb-4">国家分配成功！</h3>
                            <div class="bg-green-50 p-4 rounded-xl border border-green-200 mb-6">
                                <div class="flex items-center justify-center gap-3 mb-3">
                                    <span class="text-2xl">${selectedCountryData.flag}</span>
                                    <span class="font-semibold text-green-800">${employeeName}</span>
                                </div>
                                <div class="text-sm text-green-700">现在负责${selectedCountryData.name}市场</div>
                                <div class="text-xs text-green-600 mt-2">状态已更新为"已分配-等待配置任务"</div>
                            </div>
                            <p class="text-gray-600 text-sm mb-6">接下来可以在工作区的"任务委托"中配置具体工作内容</p>
                            <button class="w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove(); closeCountryModal(); completeHiring();">
                                确定
                            </button>
                        </div>
                    </div>
                `;
                document.body.appendChild(successModal);
            } else {
                // Show error message
                const errorToast = document.createElement('div');
                errorToast.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
                errorToast.textContent = '请先选择一个国家';
                document.body.appendChild(errorToast);
                
                setTimeout(() => {
                    errorToast.remove();
                }, 3000);
            }
        }

        function goToHomepage() {
            window.location.href = 'index.html';
        }

        function goToWorkspace() {
            window.location.href = 'workspace-new.html';
        }

        function completeHiring() {
            // 获取雇佣的员工信息
            const employeeName = finalEmployeeName || document.getElementById('employee-name').value || avatarData[selectedAvatar].suggestedNames[0];
            const employeeAvatar = selectedAvatarImage;
            const employeeType = selectedAvatarType;

            // 存储到 localStorage 以便首页获取
            const newEmployee = {
                name: employeeName,
                avatar: employeeAvatar,
                type: employeeType,
                country: selectedCountryData ? selectedCountryData.name : null,
                countryFlag: selectedCountryData ? selectedCountryData.flag : null,
                timestamp: Date.now()
            };

            // 获取现有员工列表
            let employees = JSON.parse(localStorage.getItem('hiredEmployees') || '[]');
            employees.push(newEmployee);
            localStorage.setItem('hiredEmployees', JSON.stringify(employees));

            // 跳转回首页
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 500);
        }

        // Initialize first step
        document.addEventListener('DOMContentLoaded', function() {
            updateStepIndicators(1);
        });
    </script>
</body>
</html>
// Authentication page functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeAuthPage();
});

function initializeAuthPage() {
    initializeAuthTabs();
    initializeFormValidation();
    addAuthEventListeners();
}

function initializeAuthTabs() {
    const tabBtns = document.querySelectorAll('.auth-tabs .tab-btn');
    const authForms = document.querySelectorAll('.auth-form');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetForm = this.dataset.tab;
            
            // Remove active class from all tabs and forms
            tabBtns.forEach(b => b.classList.remove('active'));
            authForms.forEach(f => f.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding form
            this.classList.add('active');
            document.getElementById(targetForm).classList.add('active');
        });
    });
}

function initializeFormValidation() {
    const loginForm = document.getElementById('login');
    const registerForm = document.getElementById('register');
    
    // Login form validation
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleLogin();
        });
    }
    
    // Register form validation
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleRegister();
        });
        
        // Password confirmation validation
        const password = document.getElementById('registerPassword');
        const confirmPassword = document.getElementById('confirmPassword');
        
        if (password && confirmPassword) {
            confirmPassword.addEventListener('input', function() {
                if (this.value && this.value !== password.value) {
                    this.setCustomValidity('密码不匹配');
                } else {
                    this.setCustomValidity('');
                }
            });
        }
    }
}

function addAuthEventListeners() {
    // Social login buttons
    const socialBtns = document.querySelectorAll('.btn-social');
    socialBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const provider = this.textContent.includes('微信') ? '微信' : '钉钉';
            showNotification(`正在跳转到${provider}登录...`, 'info');
        });
    });
    
    // Forgot password link
    const forgotLink = document.querySelector('.forgot-link');
    if (forgotLink) {
        forgotLink.addEventListener('click', function(e) {
            e.preventDefault();
            showNotification('重置密码链接已发送到您的邮箱', 'info');
        });
    }
    
    // Agreement links
    const agreementLinks = document.querySelectorAll('.link');
    agreementLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            showNotification('正在打开相关文档...', 'info');
        });
    });
}

function handleLogin() {
    const form = document.getElementById('login');
    const formData = new FormData(form);
    const email = formData.get('email');
    const password = formData.get('password');
    
    // Basic validation
    if (!email || !password) {
        showNotification('请填写完整的登录信息', 'error');
        return;
    }
    
    // Simulate login process
    showNotification('正在登录...', 'info');
    
    setTimeout(() => {
        // Simulate successful login
        showNotification('登录成功！正在跳转...', 'success');
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1500);
    }, 2000);
}

function handleRegister() {
    const form = document.getElementById('register');
    const formData = new FormData(form);
    
    const company = formData.get('company');
    const name = formData.get('name');
    const email = formData.get('email');
    const password = formData.get('password');
    const confirmPassword = formData.get('confirmPassword');
    const businessStage = formData.get('businessStage');
    const agreement = formData.get('agreement');
    
    // Validation
    if (!company || !name || !email || !password || !businessStage) {
        showNotification('请填写完整的注册信息', 'error');
        return;
    }
    
    if (password !== confirmPassword) {
        showNotification('两次输入的密码不一致', 'error');
        return;
    }
    
    if (password.length < 8) {
        showNotification('密码长度至少为8位', 'error');
        return;
    }
    
    if (!agreement) {
        showNotification('请同意服务协议和隐私政策', 'error');
        return;
    }
    
    // Simulate registration process
    showNotification('正在创建账户...', 'info');
    
    setTimeout(() => {
        showNotification('注册成功！欢迎加入Foxu AI Team！', 'success');
        setTimeout(() => {
            // Switch to login tab
            document.querySelector('.auth-tabs .tab-btn[data-tab="login"]').click();
            
            // Pre-fill email in login form
            document.getElementById('loginEmail').value = email;
            
            showNotification('请使用刚注册的账号登录', 'info');
        }, 2000);
    }, 2000);
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    
    const colors = {
        info: '#4f46e5',
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b'
    };
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1001;
        animation: slideIn 0.3s ease-out;
        max-width: 300px;
        line-height: 1.4;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}
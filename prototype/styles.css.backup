/* CSS Variables - Color System */
:root {
    /* Primary Brand Colors */
    --primary-50: #f0f9ff;
    --primary-100: #e0f2fe;
    --primary-200: #bae6fd;
    --primary-300: #7dd3fc;
    --primary-400: #38bdf8;
    --primary-500: #0ea5e9;
    --primary-600: #0284c7;
    --primary-700: #0369a1;
    --primary-800: #075985;
    --primary-900: #0c4a6e;
    
    /* Secondary Brand Colors */
    --secondary-50: #faf5ff;
    --secondary-100: #f3e8ff;
    --secondary-200: #e9d5ff;
    --secondary-300: #d8b4fe;
    --secondary-400: #c084fc;
    --secondary-500: #a855f7;
    --secondary-600: #9333ea;
    --secondary-700: #7c3aed;
    --secondary-800: #6b21a8;
    --secondary-900: #581c87;
    
    /* Accent Colors */
    --accent-emerald: #10b981;
    --accent-orange: #f59e0b;
    --accent-red: #ef4444;
    --accent-blue: #3b82f6;
    
    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-500) 0%, var(--secondary-700) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-emerald) 0%, var(--primary-500) 100%);
    --gradient-surface: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    --gradient-card: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.95) 100%);
    --gradient-header: linear-gradient(135deg, rgba(79, 70, 229, 0.02) 0%, rgba(168, 85, 247, 0.02) 100%);
    --gradient-button: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    --gradient-button-hover: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    --gradient-floating: linear-gradient(135deg, var(--secondary-500) 0%, var(--secondary-600) 100%);
    --gradient-floating-hover: linear-gradient(135deg, var(--secondary-600) 0%, var(--secondary-700) 100%);
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1), 0 4px 8px rgba(0, 0, 0, 0.06);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.12), 0 8px 16px rgba(0, 0, 0, 0.08);
    --shadow-2xl: 0 20px 40px rgba(0, 0, 0, 0.15), 0 16px 32px rgba(0, 0, 0, 0.1);
    --shadow-colored: 0 8px 25px rgba(79, 70, 229, 0.15), 0 4px 12px rgba(79, 70, 229, 0.1);
    --shadow-inset: inset 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-floating: 0 20px 50px rgba(79, 70, 229, 0.2), 0 10px 25px rgba(79, 70, 229, 0.15);
    --shadow-glow: 0 0 20px rgba(79, 70, 229, 0.3), 0 0 40px rgba(79, 70, 229, 0.1);
    --shadow-depth: 0 4px 16px rgba(0, 0, 0, 0.08), 0 8px 32px rgba(0, 0, 0, 0.04), inset 0 1px 0 rgba(255, 255, 255, 0.8);
    
    /* Border Radius */
    --radius-sm: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.25rem;
    --radius-2xl: 2rem;
    --radius-3xl: 2.5rem;
    
    /* Spacing */
    --space-xs: 0.5rem;
    --space-sm: 0.75rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    
    /* Typography */
    --font-display: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-body: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-body);
    line-height: 1.6;
    color: var(--gray-800);
    background: 
        radial-gradient(ellipse at top, rgba(79, 70, 229, 0.08) 0%, transparent 50%),
        radial-gradient(ellipse at bottom right, rgba(168, 85, 247, 0.06) 0%, transparent 50%),
        linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    animation: fadeIn 0.6s ease-out;
    position: relative;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 15% 85%, rgba(79, 70, 229, 0.12) 0%, transparent 60%),
        radial-gradient(circle at 85% 15%, rgba(168, 85, 247, 0.1) 0%, transparent 60%),
        radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.05) 0%, transparent 70%);
    pointer-events: none;
    z-index: -1;
    animation: atmosphericFloat 20s ease-in-out infinite alternate;
}

@keyframes atmosphericFloat {
    0% {
        transform: translateY(0px) scale(1);
        opacity: 0.8;
    }
    100% {
        transform: translateY(-10px) scale(1.02);
        opacity: 1;
    }
}


@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(79, 70, 229, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(79, 70, 229, 0.6), 0 0 30px rgba(79, 70, 229, 0.4);
    }
}

/* Header */
.header {
    background: var(--gradient-header);
    backdrop-filter: blur(24px);
    border-bottom: 1px solid var(--gray-100);
    padding: 0 var(--space-xl);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-depth);
    border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
    position: relative;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
    opacity: 0.6;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.logo h1 {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 800;
    letter-spacing: -0.025em;
}

.nav {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.main-nav {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.nav-link {
    color: var(--gray-600);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-600);
    background: var(--primary-50);
}

.nav-link.active {
    color: var(--primary-700);
    background: var(--primary-100);
    font-weight: 600;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background: var(--primary-600);
    border-radius: 50%;
}

.back-link {
    color: var(--gray-500);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-md);
}

.back-link:hover {
    color: var(--primary-600);
    background: var(--primary-50);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
}

.user-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    position: relative;
}

.status-indicator.online {
    background: var(--accent-emerald);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.status-indicator.online::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: var(--accent-emerald);
    animation: pulse-online 2s infinite;
}

@keyframes pulse-online {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(1.8);
        opacity: 0;
    }
}

.user-dropdown {
    position: relative;
}

.user-dropdown-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;
}

.user-dropdown-btn:hover {
    background: var(--gray-100);
}

.user-avatar-small {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid var(--primary-200);
}

.dropdown-arrow {
    font-size: 0.75rem;
    color: var(--gray-500);
    transition: transform 0.2s ease;
}

.user-dropdown.active .dropdown-arrow {
    transform: rotate(180deg);
}

.user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    width: 260px;
    background: var(--gradient-card);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(12px);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.95);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-dropdown.active .user-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.dropdown-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
}

.user-avatar-menu {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid var(--primary-200);
}

.user-info {
    display: flex;
    flex-direction: column;
}

.user-name-menu {
    font-weight: 600;
    color: var(--gray-800);
    font-size: 0.875rem;
}

.user-email {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.dropdown-divider {
    height: 1px;
    background: var(--gray-200);
    margin: 0.5rem 0;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--gray-700);
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: var(--primary-50);
    color: var(--primary-700);
}

.dropdown-item.text-red {
    color: var(--accent-red);
}

.dropdown-item.text-red:hover {
    background: rgba(239, 68, 68, 0.1);
    color: var(--accent-red);
}

.item-icon {
    font-size: 1rem;
    opacity: 0.7;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.breadcrumb-link {
    color: var(--gray-500);
    text-decoration: none;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
}

.breadcrumb-link:hover {
    color: var(--primary-600);
    background: var(--primary-50);
}

.breadcrumb-separator {
    color: var(--gray-400);
    font-weight: 400;
}

.breadcrumb-current {
    color: var(--gray-700);
    font-weight: 600;
}

.user-name {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.notification-badge {
    background: var(--gradient-accent);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
    border: 2px solid white;
}

.notification-badge:hover {
    transform: scale(1.15);
    box-shadow: var(--shadow-lg);
}

/* Main content */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--space-xl);
    animation: slideInUp 0.6s ease-out 0.2s both;
}

/* Dashboard */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-2xl);
}

.dashboard-header h2 {
    font-family: var(--font-display);
    font-size: 2.5rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.025em;
}

.hire-btn {
    background: #4f46e5;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}

.hire-btn:hover {
    background: #4338ca;
}

/* Team overview */
.team-overview {
    margin-bottom: 3rem;
}

.team-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--gradient-card);
    padding: var(--space-2xl);
    border-radius: var(--radius-2xl);
    border: 1px solid rgba(255, 255, 255, 0.6);
    text-align: center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 
        var(--shadow-lg),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    position: relative;
    overflow: hidden;
    animation: slideInUp 0.6s ease-out calc(0.4s + var(--delay, 0s)) both;
    backdrop-filter: blur(16px);
}

.stat-card:nth-child(1) { --delay: 0.1s; }
.stat-card:nth-child(2) { --delay: 0.2s; }
.stat-card:nth-child(3) { --delay: 0.3s; }

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
}

.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s;
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-glow);
}

.stat-card:hover::after {
    left: 100%;
}

.stat-number {
    font-family: var(--font-display);
    font-size: 2.75rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--space-xs);
    letter-spacing: -0.025em;
}

.stat-label {
    color: var(--gray-600);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Team members */
.team-members h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #111827;
}

.members-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.member-card {
    background: var(--gradient-card);
    padding: var(--space-2xl);
    border-radius: var(--radius-3xl);
    border: 1px solid rgba(255, 255, 255, 0.7);
    position: relative;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 
        var(--shadow-xl),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        inset 0 -1px 0 rgba(0, 0, 0, 0.03);
    overflow: hidden;
    animation: slideInUp 0.6s ease-out calc(0.6s + var(--delay, 0s)) both;
    backdrop-filter: blur(20px);
}

.member-card:nth-child(1) { --delay: 0.1s; }
.member-card:nth-child(2) { --delay: 0.2s; }
.member-card:nth-child(3) { --delay: 0.3s; }

.member-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
}

.member-card:hover {
    transform: translateY(-8px) rotateX(5deg);
    box-shadow: var(--shadow-2xl);
}

.member-card:hover .member-avatar {
    transform: scale(1.1);
}

.member-card:hover .member-badge {
    animation: pulse 2s infinite;
}

.member-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 1rem;
    transition: all 0.4s ease;
    position: relative;
    z-index: 2;
}

.member-avatar::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: var(--gradient-primary);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.member-card:hover .member-avatar::before {
    opacity: 1;
}

.member-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.member-info h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #111827;
}

.member-status {
    font-size: 0.875rem;
    margin-bottom: 1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    display: inline-block;
}

.member-status.active {
    background: #d1fae5;
    color: #065f46;
}

.member-status.standby {
    background: #fef3c7;
    color: #92400e;
}

.member-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.member-badge.director {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    box-shadow: 0 2px 8px rgba(124, 58, 237, 0.3);
}

.member-badge.specialist {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    box-shadow: 0 2px 8px rgba(5, 150, 105, 0.3);
}

.member-level-indicator {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.level-icon {
    width: 18px;
    height: 18px;
    color: var(--primary-600);
}

/* Member card level styles */
.member-card.director-level {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #7c3aed, #a855f7) border-box;
    position: relative;
}

.member-card.director-level::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    border-radius: 0.75rem 0.75rem 0 0;
}

.member-card.specialist-level {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #059669, #10b981) border-box;
    position: relative;
}

.member-card.specialist-level::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    border-radius: 0.75rem 0.75rem 0 0;
}

.member-card.director-level:hover {
    box-shadow: 0 8px 25px rgba(124, 58, 237, 0.15);
}

.member-card.specialist-level:hover {
    box-shadow: 0 8px 25px rgba(5, 150, 105, 0.15);
}

/* Activity feed */
.activity-feed h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #111827;
}

.feed-container {
    background: 
        linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.92) 100%),
        linear-gradient(135deg, rgba(79, 70, 229, 0.02) 0%, rgba(167, 85, 247, 0.01) 100%);
    border-radius: var(--radius-3xl);
    border: 1px solid rgba(255, 255, 255, 0.6);
    padding: var(--space-2xl);
    box-shadow: 
        var(--shadow-xl),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    animation: slideInUp 0.6s ease-out 0.8s both;
    backdrop-filter: blur(20px);
}

.feed-item {
    display: flex;
    gap: var(--space-lg);
    padding: var(--space-lg);
    border-bottom: 1px solid var(--gray-100);
    transition: all 0.3s ease;
    border-radius: var(--radius-xl);
    margin: 0 calc(-1 * var(--space-md));
    animation: slideInLeft 0.6s ease-out calc(1s + var(--delay, 0s)) both;
}

.feed-item:nth-child(1) { --delay: 0.1s; }
.feed-item:nth-child(2) { --delay: 0.2s; }
.feed-item:nth-child(3) { --delay: 0.3s; }
.feed-item:nth-child(4) { --delay: 0.4s; }

.feed-item:last-child {
    border-bottom: none;
}

.feed-item:hover {
    background: 
        linear-gradient(135deg, rgba(79, 70, 229, 0.05) 0%, rgba(168, 85, 247, 0.03) 100%),
        var(--gradient-surface);
    transform: translateX(8px);
    box-shadow: var(--shadow-md);
}

.feed-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.feed-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.feed-content {
    flex: 1;
}

.feed-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.feed-header strong {
    color: #111827;
}

.feed-time {
    color: #6b7280;
    font-size: 0.875rem;
}

.feed-message {
    color: var(--gray-700);
    margin-bottom: var(--space-sm);
    line-height: 1.6;
    display: flex;
    align-items: flex-start;
    gap: var(--space-xs);
}

.message-icon {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
    margin-top: 2px;
}

.message-icon.alert {
    color: var(--accent-red);
}

.message-icon.report {
    color: var(--primary-600);
}

.message-icon.success {
    color: var(--accent-emerald);
}

.message-icon.welcome {
    color: var(--secondary-600);
}

.feed-actions {
    display: flex;
    gap: 0.5rem;
}

/* Floating CTA */
.floating-cta {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.floating-hire-btn {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background: var(--gradient-floating);
    border: 2px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 
        var(--shadow-xl),
        0 0 20px rgba(79, 70, 229, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    color: white;
    position: relative;
    overflow: hidden;
    animation: float 6s ease-in-out infinite;
}

.floating-hire-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
    border-radius: 50%;
}

.floating-hire-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
}

.floating-hire-btn:hover {
    background: var(--gradient-floating-hover);
    transform: translateY(-8px) scale(1.1);
    box-shadow: var(--shadow-floating);
    animation-play-state: paused;
}

.floating-hire-btn:hover::after {
    width: 100%;
    height: 100%;
}

.floating-hire-btn:active {
    transform: translateY(-4px) scale(1.05);
}

.cta-icon {
    width: 24px;
    height: 24px;
    color: white;
    z-index: 2;
}

/* Chat Modal */
.chat-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    z-index: 1001;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.chat-modal.active {
    display: flex;
    opacity: 1;
    animation: fadeIn 0.3s ease-out;
}

.chat-container {
    background: var(--gradient-card);
    border-radius: var(--radius-3xl);
    width: 90%;
    max-width: 420px;
    height: 640px;
    display: flex;
    flex-direction: column;
    box-shadow: 
        var(--shadow-2xl),
        0 0 40px rgba(79, 70, 229, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        inset 0 -1px 0 rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.7);
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    backdrop-filter: blur(24px);
}

.chat-modal.active .chat-container {
    transform: scale(1) translateY(0);
}

.chat-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.chat-title h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.25rem;
}

.chat-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
}

.chat-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-close:hover {
    color: #374151;
}

.chat-employees {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
    overflow-x: auto;
}

.employee-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    min-width: 0;
    flex-shrink: 0;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    font-size: 0.875rem;
    border-bottom: 2px solid transparent;
}

.employee-tab:hover {
    background: #f3f4f6;
}

.employee-tab.active {
    background: #f0f9ff;
    border-bottom-color: #4f46e5;
}

.employee-tab img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    flex-shrink: 0;
}

.employee-tab span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #374151;
    font-weight: 500;
}

.unread-dot {
    width: 6px;
    height: 6px;
    background: #ef4444;
    border-radius: 50%;
    flex-shrink: 0;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.message {
    display: flex;
    gap: 0.75rem;
    align-items: flex-start;
}

.message-avatar img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.message-content {
    flex: 1;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.message-header strong {
    font-size: 0.875rem;
    color: #111827;
}

.message-time {
    font-size: 0.75rem;
    color: #6b7280;
}

.message-text {
    background: #f3f4f6;
    padding: 0.75rem;
    border-radius: 0.75rem;
    line-height: 1.5;
    color: #374151;
    font-size: 0.875rem;
}

.ai-message .message-text {
    background: #eff6ff;
    border: 1px solid #dbeafe;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-text {
    background: #4f46e5;
    color: white;
}

.chat-input-area {
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
}

.chat-input-container {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.chat-input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    outline: none;
}

.chat-input:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.chat-send {
    background: #4f46e5;
    color: white;
    border: none;
    border-radius: 0.5rem;
    padding: 0.75rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.chat-send:hover {
    background: #4338ca;
}

.quick-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.quick-btn {
    background: #f3f4f6;
    border: none;
    border-radius: 1rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    cursor: pointer;
    transition: background-color 0.2s;
    color: #374151;
}

.quick-btn:hover {
    background: #e5e7eb;
}

/* Responsibilities section */
.responsibilities-section {
    background: 
        linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.92) 100%),
        linear-gradient(135deg, rgba(79, 70, 229, 0.02) 0%, rgba(167, 85, 247, 0.01) 100%);
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.6);
    box-shadow: 
        var(--shadow-lg),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    backdrop-filter: blur(12px);
}

.responsibilities-section h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #111827;
}

.responsibilities-section p {
    color: #6b7280;
    margin-bottom: 2rem;
}

.responsibility-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.responsibility-card {
    background: #f9fafb;
    padding: 1.5rem;
    border-radius: 0.75rem;
    border: 1px solid #e5e7eb;
    transition: box-shadow 0.2s;
}

.responsibility-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.responsibility-icon {
    width: 32px;
    height: 32px;
    margin-bottom: var(--space-lg);
    color: var(--primary-600);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-50);
    border-radius: var(--radius-lg);
    padding: var(--space-xs);
}

.responsibility-card h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #111827;
}

.responsibility-card ul {
    list-style: none;
    padding: 0;
}

.responsibility-card li {
    padding: 0.5rem 0;
    color: #374151;
    position: relative;
    padding-left: 1.5rem;
}

.responsibility-card li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #059669;
    font-weight: 600;
}

.task-assignment-cta {
    background: #f0f9ff;
    padding: 2rem;
    border-radius: 0.75rem;
    border: 1px solid #dbeafe;
}

.task-assignment-cta h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #111827;
}

.task-assignment-cta p {
    color: #6b7280;
    margin-bottom: 2rem;
}

/* Work Analytics */
.board-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.work-stats {
    display: flex;
    gap: 1.5rem;
}

.stat-item {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.work-analytics {
    background: 
        linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.92) 100%),
        linear-gradient(135deg, rgba(79, 70, 229, 0.02) 0%, rgba(167, 85, 247, 0.01) 100%);
    padding: var(--space-2xl);
    border-radius: var(--radius-3xl);
    border: 1px solid rgba(255, 255, 255, 0.6);
    margin-bottom: var(--space-2xl);
    box-shadow: 
        var(--shadow-xl),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    backdrop-filter: blur(20px);
}

.work-analytics h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #111827;
}

.analytics-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-section h5,
.activity-heatmap h5 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #374151;
}

/* Frequency Chart */
.frequency-chart {
    background: 
        linear-gradient(145deg, rgba(249, 250, 251, 0.8) 0%, rgba(243, 244, 246, 0.6) 100%),
        linear-gradient(135deg, rgba(79, 70, 229, 0.03) 0%, rgba(167, 85, 247, 0.02) 100%);
    padding: var(--space-xl);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.5);
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.7),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    backdrop-filter: blur(12px);
}

.chart-bars {
    display: flex;
    align-items: end;
    gap: 0.5rem;
    height: 120px;
}

.bar-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.bar {
    width: 100%;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border-radius: 0.25rem 0.25rem 0 0;
    min-height: 4px;
    transition: all 0.3s ease;
}

.bar:hover {
    background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
}

.bar-label {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
}

.bar-value {
    font-size: 0.75rem;
    color: #4f46e5;
    font-weight: 600;
}

/* Activity Heatmap */
.activity-heatmap {
    background: 
        linear-gradient(145deg, rgba(249, 250, 251, 0.8) 0%, rgba(243, 244, 246, 0.6) 100%),
        linear-gradient(135deg, rgba(79, 70, 229, 0.03) 0%, rgba(167, 85, 247, 0.02) 100%);
    padding: var(--space-xl);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.5);
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.7),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    backdrop-filter: blur(12px);
}

.heatmap-grid {
    display: flex;
    flex-direction: column;
    gap: 2px;
    margin-bottom: 1rem;
}

.heatmap-row {
    display: flex;
    gap: 2px;
}

.heatmap-cell {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    cursor: pointer;
    transition: all 0.2s;
}

.heatmap-cell:hover {
    transform: scale(1.2);
    border: 1px solid #374151;
}

.heatmap-cell.level-0 {
    background: #f3f4f6;
}

.heatmap-cell.level-1 {
    background: #c7d2fe;
}

.heatmap-cell.level-2 {
    background: #a5b4fc;
}

.heatmap-cell.level-3 {
    background: #818cf8;
}

.heatmap-cell.level-4 {
    background: #4f46e5;
}

.heatmap-legend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
}

.legend-scale {
    display: flex;
    gap: 2px;
}

.legend-cell {
    width: 10px;
    height: 10px;
    border-radius: 1px;
}

/* Performance Metrics */
.performance-metrics {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
}

.metric-card {
    background: 
        linear-gradient(145deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.7) 100%),
        linear-gradient(135deg, rgba(79, 70, 229, 0.03) 0%, rgba(167, 85, 247, 0.02) 100%);
    padding: var(--space-xl);
    border-radius: var(--radius-2xl);
    border: 1px solid rgba(255, 255, 255, 0.6);
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    transition: all 0.3s ease;
    backdrop-filter: blur(16px);
    box-shadow: 
        var(--shadow-md),
        inset 0 1px 0 rgba(255, 255, 255, 0.7),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
}

.metric-card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
}

.metric-icon {
    width: 24px;
    height: 24px;
    color: var(--primary-600);
    background: var(--primary-50);
    border-radius: var(--radius-lg);
    padding: var(--space-xs);
    flex-shrink: 0;
}

.metric-info {
    display: flex;
    flex-direction: column;
}

.metric-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #111827;
}

.metric-label {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
}

/* Buttons */
.btn {
    padding: var(--space-md) var(--space-lg);
    border-radius: var(--radius-xl);
    border: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    font-size: 0.875rem;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(12px);
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-button);
    color: white;
    box-shadow: 
        var(--shadow-md),
        0 0 15px rgba(79, 70, 229, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-primary:hover {
    background: var(--gradient-button-hover);
    transform: translateY(-1px);
    box-shadow: 
        var(--shadow-lg),
        0 0 25px rgba(79, 70, 229, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.btn-outline {
    background: transparent;
    color: var(--primary-600);
    border: 2px solid var(--primary-200);
    backdrop-filter: blur(10px);
}

.btn-outline:hover {
    background: var(--primary-50);
    border-color: var(--primary-300);
    transform: translateY(-1px);
}

.btn-large {
    padding: var(--space-lg) var(--space-2xl);
    font-size: 1rem;
    border-radius: var(--radius-2xl);
}

.btn-small {
    padding: var(--space-sm) var(--space-md);
    font-size: 0.75rem;
    border-radius: var(--radius-lg);
}

.btn-full {
    width: 100%;
    justify-content: center;
}

/* Hire page */
.hire-header {
    text-align: center;
    margin-bottom: 3rem;
}

.hire-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 0.5rem;
}

.hire-header p {
    color: #6b7280;
    font-size: 1.125rem;
}

.employee-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.employee-card {
    background: 
        linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%),
        linear-gradient(135deg, rgba(79, 70, 229, 0.02) 0%, rgba(167, 85, 247, 0.01) 100%);
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.6);
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 
        var(--shadow-lg),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    backdrop-filter: blur(12px);
}

.employee-card:hover {
    transform: translateY(-4px);
    box-shadow: 
        0 8px 25px rgba(0, 0, 0, 0.1),
        0 0 40px rgba(79, 70, 229, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        inset 0 -1px 0 rgba(0, 0, 0, 0.03);
}

.card-header {
    padding: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.director-card .card-header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
}

.specialist-card .card-header {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.employee-avatar.large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.employee-avatar.large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.employee-title h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.employee-level {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
}

.card-content {
    padding: 2rem;
}

.employee-intro {
    margin-bottom: 1.5rem;
    color: #6b7280;
    line-height: 1.6;
}

.capabilities {
    margin-bottom: 2rem;
}

.capabilities h4 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: #111827;
}

.capabilities ul {
    list-style: none;
    space-y: 0.5rem;
}

.capabilities li {
    padding: 0.5rem 0;
    color: #374151;
}

.market-selection {
    margin-bottom: 2rem;
}

.market-selection h4 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: #111827;
}

.market-dropdown {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    background: white;
}

.pricing {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f9fafb;
    border-radius: 0.5rem;
}

.price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.price-item:last-child {
    margin-bottom: 0;
}

.price-label {
    color: #6b7280;
}

.price-value {
    font-weight: 700;
    color: #111827;
    font-size: 1.125rem;
}

.hire-benefits {
    background: 
        linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.92) 100%),
        linear-gradient(135deg, rgba(79, 70, 229, 0.02) 0%, rgba(167, 85, 247, 0.01) 100%);
    padding: 3rem;
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.6);
    text-align: center;
    box-shadow: 
        var(--shadow-lg),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    backdrop-filter: blur(12px);
}

.hire-benefits h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: #111827;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.benefit-item {
    text-align: center;
}

.benefit-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.benefit-item h4 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #111827;
}

.benefit-item p {
    color: #6b7280;
    font-size: 0.875rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: 
        linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%),
        linear-gradient(135deg, rgba(79, 70, 229, 0.03) 0%, rgba(167, 85, 247, 0.02) 100%);
    border-radius: 1rem;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 
        var(--shadow-2xl),
        0 0 40px rgba(79, 70, 229, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        inset 0 -1px 0 rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(20px);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6b7280;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.hire-summary {
    margin-bottom: 2rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.summary-item.total {
    border-bottom: none;
    font-weight: 600;
    font-size: 1.125rem;
    color: #111827;
}

.payment-options h4 {
    margin-bottom: 1rem;
    color: #111827;
}

.payment-methods {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.payment-method {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: border-color 0.2s;
}

.payment-method:hover {
    border-color: #4f46e5;
}

.payment-method input[type="radio"] {
    margin: 0;
}

/* Workspace */
.workspace-header {
    margin-bottom: 2rem;
}

.employee-profile {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    background: 
        linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.92) 100%),
        linear-gradient(135deg, rgba(79, 70, 229, 0.02) 0%, rgba(167, 85, 247, 0.01) 100%);
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.6);
    box-shadow: 
        var(--shadow-lg),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    backdrop-filter: blur(12px);
}

.employee-details h2 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #111827;
}

.employee-desc {
    color: #6b7280;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.employee-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
}

.status-indicator.active {
    background: #10b981;
}

.workspace-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid #e5e7eb;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    background: none;
    border: none;
    color: #6b7280;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
}

.tab-btn.active {
    color: #4f46e5;
    border-bottom-color: #4f46e5;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Task assignment */
.task-assignment {
    background: 
        linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.92) 100%),
        linear-gradient(135deg, rgba(79, 70, 229, 0.02) 0%, rgba(167, 85, 247, 0.01) 100%);
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.6);
    box-shadow: 
        var(--shadow-lg),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    backdrop-filter: blur(12px);
}

.task-assignment h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #111827;
}

.task-assignment p {
    color: #6b7280;
    margin-bottom: 2rem;
}

.artifacts-checklist {
    margin-bottom: 2rem;
}

.artifact-category {
    margin-bottom: 2rem;
}

.artifact-category h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #111827;
}

.artifact-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.artifact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: border-color 0.2s;
}

.artifact-item:hover {
    border-color: #4f46e5;
}

.artifact-item input[type="checkbox"] {
    margin-top: 0.25rem;
}

.artifact-info {
    flex: 1;
}

.artifact-info h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #111827;
}

.artifact-info p {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.artifact-price {
    font-weight: 600;
    color: #4f46e5;
}

.task-summary {
    background: #f9fafb;
    padding: 1.5rem;
    border-radius: 0.5rem;
}

.summary-box h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #111827;
}

/* Kanban board */
.project-board {
    background: 
        linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.92) 100%),
        linear-gradient(135deg, rgba(79, 70, 229, 0.02) 0%, rgba(167, 85, 247, 0.01) 100%);
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.6);
    box-shadow: 
        var(--shadow-lg),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    backdrop-filter: blur(12px);
}

.project-board h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: #111827;
}

.kanban-board {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

.kanban-column {
    background: #f9fafb;
    padding: 1rem;
    border-radius: 0.5rem;
}

.kanban-column h4 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: #111827;
}

.task-card {
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
    margin-bottom: 1rem;
}

.task-card:last-child {
    margin-bottom: 0;
}

.task-card h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #111827;
}

.task-card p {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
}

.task-progress {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.progress-bar {
    flex: 1;
    height: 4px;
    background: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #10b981;
    transition: width 0.3s;
}

.task-meta {
    color: #6b7280;
    font-size: 0.75rem;
}

.task-date {
    color: #6b7280;
}

/* Report grid */
.deliverables {
    background: 
        linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.92) 100%),
        linear-gradient(135deg, rgba(79, 70, 229, 0.02) 0%, rgba(167, 85, 247, 0.01) 100%);
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.6);
    box-shadow: 
        var(--shadow-lg),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    backdrop-filter: blur(12px);
}

.deliverables h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: #111827;
}

.report-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.report-card {
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    overflow: hidden;
    transition: box-shadow 0.2s;
}

.report-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.report-thumbnail {
    height: 150px;
    overflow: hidden;
}

.report-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.report-info {
    padding: 1.5rem;
}

.report-info h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #111827;
}

.report-info p {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.report-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.report-type {
    background: #e0e7ff;
    color: #3730a3;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.report-date {
    color: #6b7280;
    font-size: 0.75rem;
}

.report-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn-icon {
    font-size: 0.875rem;
}

/* Alert system */
.alert-system {
    background: 
        linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.92) 100%),
        linear-gradient(135deg, rgba(79, 70, 229, 0.02) 0%, rgba(167, 85, 247, 0.01) 100%);
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.6);
    box-shadow: 
        var(--shadow-lg),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    backdrop-filter: blur(12px);
}

.alert-system h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #111827;
}

.alert-system p {
    color: #6b7280;
    margin-bottom: 2rem;
}

.alert-filters {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.5rem 1rem;
    background: #f3f4f6;
    color: #6b7280;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
}

.filter-btn.active {
    background: #4f46e5;
    color: white;
}

.alert-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.alert-item {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    border-radius: 0.75rem;
    border: 1px solid #e5e7eb;
    transition: box-shadow 0.2s;
}

.alert-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.alert-item.urgent {
    border-left: 4px solid #ef4444;
    background: #fef2f2;
}

.alert-item.important {
    border-left: 4px solid #f59e0b;
    background: #fffbeb;
}

.alert-item.normal {
    border-left: 4px solid #6b7280;
}

.alert-item.read {
    opacity: 0.7;
}

.alert-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.alert-content {
    flex: 1;
}

.alert-content h4 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #111827;
}

.alert-content p {
    color: #374151;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.alert-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.alert-time {
    color: #6b7280;
    font-size: 0.875rem;
}

.alert-category {
    background: #e5e7eb;
    color: #374151;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.alert-actions {
    display: flex;
    gap: 0.5rem;
}

/* Auth page */
.auth-page {
    background: 
        linear-gradient(135deg, #667eea 0%, #764ba2 100%),
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.auth-page::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: 
        radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.05) 0%, transparent 30%),
        radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.03) 0%, transparent 30%);
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
}

.auth-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    max-width: 1200px;
    width: 100%;
    align-items: center;
}

.auth-card {
    background: 
        linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%),
        linear-gradient(135deg, rgba(103, 126, 234, 0.03) 0%, rgba(118, 75, 162, 0.02) 100%);
    padding: 3rem;
    border-radius: 1rem;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 60px rgba(103, 126, 234, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(16px);
}

.auth-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.logo-large {
    text-align: center;
    flex: 1;
}

.app-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-600);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;
    border: 1px solid var(--primary-200);
    background: var(--primary-50);
}

.app-link:hover {
    background: var(--primary-100);
    border-color: var(--primary-300);
    transform: translateY(-1px);
}

.app-link-icon {
    transition: transform 0.2s ease;
}

.app-link:hover .app-link-icon {
    transform: translateX(2px);
}

.logo-large h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #4f46e5;
    margin-bottom: 0.5rem;
}

.logo-large p {
    color: #6b7280;
    font-size: 1.125rem;
}

.auth-tabs {
    display: flex;
    margin-bottom: 2rem;
    background: #f3f4f6;
    border-radius: 0.5rem;
    padding: 0.25rem;
}

.auth-tabs .tab-btn {
    flex: 1;
    padding: 0.75rem;
    background: transparent;
    border: none;
    border-radius: 0.375rem;
    color: #6b7280;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.auth-tabs .tab-btn.active {
    background: white;
    color: #4f46e5;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkmark {
    width: 16px;
    height: 16px;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    position: relative;
}

.forgot-link {
    color: #4f46e5;
    text-decoration: none;
    font-size: 0.875rem;
}

.forgot-link:hover {
    text-decoration: underline;
}

.social-login {
    margin-top: 2rem;
}

.divider {
    text-align: center;
    margin-bottom: 1.5rem;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e7eb;
}

.divider span {
    background: white;
    padding: 0 1rem;
    color: #6b7280;
    font-size: 0.875rem;
}

.social-buttons {
    display: flex;
    gap: 1rem;
}

.btn-social {
    flex: 1;
    background: #f9fafb;
    color: #374151;
    border: 1px solid #e5e7eb;
    justify-content: center;
}

.btn-social:hover {
    background: #f3f4f6;
}

.register-benefits {
    margin-top: 2rem;
    padding: 1.5rem;
    background: #f0f9ff;
    border-radius: 0.5rem;
}

.register-benefits h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #111827;
}

.register-benefits ul {
    list-style: none;
}

.register-benefits li {
    padding: 0.25rem 0;
    color: #374151;
    font-size: 0.875rem;
}

.auth-features {
    color: white;
}

.auth-features h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.feature-item {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.feature-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.feature-content h4 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.feature-content p {
    opacity: 0.9;
    font-size: 0.875rem;
    line-height: 1.6;
}

.link {
    color: #4f46e5;
    text-decoration: none;
}

.link:hover {
    text-decoration: underline;
}

/* Responsive design */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }
    
    .dashboard-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .team-stats {
        grid-template-columns: 1fr;
    }
    
    .members-grid {
        grid-template-columns: 1fr;
    }
    
    .employee-types {
        grid-template-columns: 1fr;
    }
    
    .kanban-board {
        grid-template-columns: 1fr;
    }
    
    .report-grid {
        grid-template-columns: 1fr;
    }
    
    .auth-container {
        grid-template-columns: 1fr;
    }
    
    .auth-card {
        padding: 2rem;
    }
    
    .employee-profile {
        flex-direction: column;
        text-align: center;
    }
    
    .workspace-tabs {
        flex-wrap: wrap;
    }
    
    .social-buttons {
        flex-direction: column;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
    }
    
    .alert-filters {
        justify-content: flex-start;
    }
    
    .report-actions {
        justify-content: flex-start;
    }
    
    .board-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .work-stats {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .analytics-container {
        grid-template-columns: 1fr;
    }
    
    .performance-metrics {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .responsibility-categories {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 0 1rem;
    }
    
    .header-container {
        height: 60px;
    }
    
    .logo h1 {
        font-size: 1.25rem;
    }
    
    .main-content {
        padding: 0.5rem;
    }
    
    .dashboard-header h2 {
        font-size: 1.5rem;
    }
    
    .auth-card {
        padding: 1.5rem;
    }
    
    .logo-large h1 {
        font-size: 1.5rem;
    }
    
    .hire-header h2 {
        font-size: 2rem;
    }
    
    .card-header {
        padding: 1.5rem;
    }
    
    .card-content {
        padding: 1.5rem;
    }
    
    .floating-cta {
        bottom: 1rem;
        right: 1rem;
    }
    
    .floating-hire-btn {
        width: 56px;
        height: 56px;
    }
    
    .cta-icon {
        font-size: 1.75rem;
    }
}
// Workspace functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeWorkspace();
});

function initializeWorkspace() {
    initializeTabs();
    initializeTaskAssignment();
    initializeAlertFilters();
    addWorkspaceEventListeners();
}

function initializeTabs() {
    const tabBtns = document.querySelectorAll('[data-tab]');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // Remove active class from all tabs and contents
            tabBtns.forEach(b => {
                b.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600', 'bg-blue-50');
                b.classList.add('text-gray-600');
            });
            tabContents.forEach(c => c.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            this.classList.remove('text-gray-600');
            this.classList.add('text-blue-600', 'border-b-2', 'border-blue-600', 'bg-blue-50');
            document.getElementById(targetTab).classList.add('active');
        });
    });
}

function initializeTaskAssignment() {
    const checkboxes = document.querySelectorAll('.artifact-item input[type="checkbox"]');
    const selectedTasks = document.getElementById('selectedTasks');
    const estimatedTime = document.getElementById('estimatedTime');
    const totalCost = document.getElementById('totalCost');
    const assignBtn = document.getElementById('assignTaskBtn');
    
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateTaskSummary);
    });
    
    function updateTaskSummary() {
        const checkedBoxes = document.querySelectorAll('.artifact-item input[type="checkbox"]:checked');
        let totalPrice = 0;
        let tasks = [];
        let timeEstimate = 0;
        
        checkedBoxes.forEach(checkbox => {
            const price = parseInt(checkbox.dataset.price);
            totalPrice += price;
            
            const artifactInfo = checkbox.closest('.artifact-item').querySelector('.artifact-info h5');
            tasks.push(artifactInfo.textContent);
            
            // Estimate time based on task complexity
            if (checkbox.value === 'A3' || checkbox.value === 'A4') {
                timeEstimate += 3; // 3 days for complex analysis
            } else if (checkbox.value === 'monitoring') {
                timeEstimate = 0; // Ongoing service
            } else {
                timeEstimate += 2; // 2 days for standard reports
            }
        });
        
        // Update UI
        if (tasks.length === 0) {
            selectedTasks.textContent = '请选择任务';
            estimatedTime.textContent = '-';
            totalCost.textContent = '¥0';
            assignBtn.disabled = true;
        } else {
            selectedTasks.textContent = `${tasks.length}个任务`;
            if (timeEstimate === 0) {
                estimatedTime.textContent = '持续服务';
            } else {
                estimatedTime.textContent = `${timeEstimate}个工作日`;
            }
            totalCost.textContent = `¥${totalPrice.toLocaleString()}`;
            assignBtn.disabled = false;
        }
    }
    
    // Assign task button
    if (assignBtn) {
        assignBtn.addEventListener('click', function() {
            const checkedBoxes = document.querySelectorAll('.artifact-item input[type="checkbox"]:checked');
            if (checkedBoxes.length === 0) return;
            
            showNotification('任务已成功委托给Emma！她将立即开始工作。', 'success');
            
            // Reset form
            checkedBoxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            updateTaskSummary();
            
            // Switch to projects tab to show progress
            setTimeout(() => {
                document.querySelector('[data-tab="projects"]').click();
            }, 1500);
        });
    }
}

function initializeAlertFilters() {
    const filterBtns = document.querySelectorAll('[data-filter]');
    const alertItems = document.querySelectorAll('#alerts .bg-white, #alerts .bg-gray-50');
    
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const filter = this.dataset.filter;
            
            // Update active filter
            filterBtns.forEach(b => {
                b.classList.remove('bg-blue-600', 'text-white');
                b.classList.add('bg-gray-100', 'text-gray-700');
            });
            this.classList.remove('bg-gray-100', 'text-gray-700');
            this.classList.add('bg-blue-600', 'text-white');
            
            // Show/hide alerts based on filter
            alertItems.forEach(item => {
                const categoryElement = item.querySelector('.rounded-full');
                if (!categoryElement) return;
                
                const category = categoryElement.textContent;
                
                if (filter === 'all') {
                    item.style.display = 'block';
                } else {
                    const shouldShow = (
                        (filter === 'price' && category === '价格预警') ||
                        (filter === 'product' && category === '新品预警') ||
                        (filter === 'competitor' && category === '竞品动态') ||
                        (filter === 'competitor' && category === '市场趋势')
                    );
                    item.style.display = shouldShow ? 'block' : 'none';
                }
            });
        });
    });
}

function addWorkspaceEventListeners() {
    // Report preview buttons
    const previewBtns = document.querySelectorAll('#reports button');
    previewBtns.forEach(btn => {
        if (btn.textContent.includes('预览')) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                showNotification('正在打开报告预览...', 'info');
            });
        }
    });
    
    // AI voice buttons
    const voiceBtns = document.querySelectorAll('#reports button');
    voiceBtns.forEach(btn => {
        if (btn.textContent.includes('AI语音讲解')) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                showNotification('AI语音讲解功能即将上线！', 'info');
            });
        }
    });
    
    // Download buttons
    const downloadBtns = document.querySelectorAll('#reports button');
    downloadBtns.forEach(btn => {
        if (btn.textContent.includes('下载')) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                showNotification('报告下载已开始...', 'success');
            });
        }
    });
    
    // Alert action buttons
    const alertBtns = document.querySelectorAll('#alerts button');
    alertBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const alertItem = this.closest('.bg-white, .bg-gray-50');
            
            if (this.textContent.includes('查看详情')) {
                showNotification('正在打开详细分析...', 'info');
            } else if (this.textContent.includes('标记已读')) {
                if (alertItem) {
                    alertItem.style.opacity = '0.6';
                }
                showNotification('已标记为已读', 'success');
            }
        });
    });
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    
    const colors = {
        info: '#4f46e5',
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b'
    };
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1001;
        animation: slideIn 0.3s ease-out;
        max-width: 300px;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}
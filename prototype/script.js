// Main dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard
    initializeDashboard();
    
    // Add event listeners
    addEventListeners();
});

function initializeDashboard() {
    console.log('Dashboard initialized');
}

function addEventListeners() {
    // User dropdown functionality
    initializeUserDropdown();
    
    // Floating hire button
    const floatingHireBtn = document.querySelector('.floating-hire-btn');
    if (floatingHireBtn) {
        floatingHireBtn.addEventListener('click', function() {
            window.location.href = 'hire.html';
        });
    }
    
    // Employee tabs in chat
    const employeeTabs = document.querySelectorAll('.employee-tab');
    employeeTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            employeeTabs.forEach(t => t.classList.remove('active'));
            // Add active class to clicked tab
            this.classList.add('active');
            
            // Hide unread dot when clicked
            const unreadDot = this.querySelector('.unread-dot');
            if (unreadDot) {
                unreadDot.style.display = 'none';
            }
        });
    });
    
    // Member card buttons
    const memberCards = document.querySelectorAll('.member-card .btn-primary');
    memberCards.forEach(button => {
        button.addEventListener('click', function() {
            window.location.href = 'workspace.html';
        });
    });
}

function showNotification(message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4f46e5;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        animation: slideIn 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

function initializeUserDropdown() {
    const userDropdownBtn = document.getElementById('userDropdownBtn');
    const userDropdown = document.querySelector('.user-dropdown');
    const userDropdownMenu = document.getElementById('userDropdownMenu');
    
    if (userDropdownBtn && userDropdown && userDropdownMenu) {
        userDropdownBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            userDropdown.classList.toggle('active');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!userDropdown.contains(e.target)) {
                userDropdown.classList.remove('active');
            }
        });
        
        // Close dropdown when pressing Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                userDropdown.classList.remove('active');
            }
        });
        
        // Handle dropdown item clicks
        const dropdownItems = userDropdownMenu.querySelectorAll('.dropdown-item');
        dropdownItems.forEach(item => {
            item.addEventListener('click', function(e) {
                if (this.href === '#') {
                    e.preventDefault();
                    const text = this.textContent.trim();
                    showNotification(`"${text}" 功能正在开发中`);
                }
                userDropdown.classList.remove('active');
            });
        });
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
/**
 * 统计数据统一管理
 * 确保首页、总监工作区、专员工作区的数据一致性
 */

// 全局统计数据配置
const STATS_CONFIG = {
    // 基础配置
    pointToRMB: 1, // 1点数 = 1元人民币
    
    // 总监数据
    director: {
        monthlyTasks: 120, // 调整为更合理的数值
        strategicRoles: 4,
        workDays: 78,
        equivalentExperts: 5,
        valueCreated: 156.8, // 万元
        pointsConsumed: 20000,
        costRMB: 2.0 // 万元
    },
    
    // 专员数据
    specialist: {
        monthlyTasks: 95, // 调整为更合理的数值
        workRoles: 8,
        workDays: 45,
        equivalentExperts: 3,
        valueCreated: 78.5, // 万元
        pointsConsumed: 12000,
        costRMB: 1.2 // 万元
    },
    
    // 预警和决策数据
    intelligence: {
        riskAlerts: 15,
        opportunitiesFound: 28,
        competitorUpdates: 156,
        alertAccuracy: 92, // 百分比
        suggestionAdoptionRate: 89, // 百分比
        opportunitiesSeized: 12,
        lossAvoided: 450, // 万元
        competitiveAdvantage: 3.2 // 倍数
    }
};

// 计算衍生数据
const CALCULATED_STATS = {
    // 团队总计
    totalTasks: STATS_CONFIG.director.monthlyTasks + STATS_CONFIG.specialist.monthlyTasks,
    totalValueCreated: STATS_CONFIG.director.valueCreated + STATS_CONFIG.specialist.valueCreated,
    totalCost: STATS_CONFIG.director.costRMB + STATS_CONFIG.specialist.costRMB,
    totalExperts: STATS_CONFIG.director.equivalentExperts + STATS_CONFIG.specialist.equivalentExperts,
    
    // ROI计算
    roi: Math.round((STATS_CONFIG.director.valueCreated + STATS_CONFIG.specialist.valueCreated) / 
                   (STATS_CONFIG.director.costRMB + STATS_CONFIG.specialist.costRMB) * 10) / 10
};

// 年度图表数据（模拟历史数据）
const CHART_DATA = {
    2024: {
        months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        team: {
            valueCreated: [85.2, 92.8, 105.6, 128.3, 145.7, 168.2, 189.5, 205.8, 218.6, 193.3, 213.5, 235.3],
            cost: [2.8, 2.9, 3.0, 3.0, 3.1, 3.1, 3.2, 3.2, 3.2, 3.2, 3.2, 3.2]
        },
        director: {
            valueCreated: [56.8, 61.2, 68.5, 82.1, 95.3, 110.6, 125.8, 132.4, 140.2, 128.5, 142.3, 156.8],
            cost: [1.8, 1.9, 1.9, 1.9, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0]
        },
        specialist: {
            valueCreated: [28.4, 31.6, 37.1, 46.2, 50.4, 57.6, 63.7, 73.4, 78.4, 64.8, 71.2, 78.5],
            cost: [1.0, 1.0, 1.1, 1.1, 1.1, 1.1, 1.2, 1.2, 1.2, 1.2, 1.2, 1.2]
        },
        'specialist-usa': {
            valueCreated: [0, 0, 0, 0, 0, 0, 0, 15.2, 22.8, 35.6, 42.3, 48.9],
            cost: [0, 0, 0, 0, 0, 0, 0, 0.8, 1.0, 1.0, 1.0, 1.0]
        }
    },
    2023: {
        months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        team: {
            valueCreated: [45.6, 52.3, 61.8, 68.9, 75.2, 82.6, 88.4, 94.7, 102.1, 108.5, 115.8, 125.3],
            cost: [2.5, 2.5, 2.6, 2.6, 2.7, 2.7, 2.8, 2.8, 2.8, 2.8, 2.8, 2.8]
        },
        director: {
            valueCreated: [32.4, 37.8, 44.2, 48.6, 52.9, 58.3, 62.1, 66.8, 71.5, 76.2, 81.4, 87.6],
            cost: [1.5, 1.5, 1.6, 1.6, 1.7, 1.7, 1.8, 1.8, 1.8, 1.8, 1.8, 1.8]
        },
        specialist: {
            valueCreated: [13.2, 14.5, 17.6, 20.3, 22.3, 24.3, 26.3, 27.9, 30.6, 32.3, 34.4, 37.7],
            cost: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
        },
        'specialist-usa': {
            valueCreated: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            cost: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        }
    }
};

// 月份数据（模拟历史数据）
const MONTHLY_DATA = {
    '2024-12': {
        director: { ...STATS_CONFIG.director },
        specialist: { ...STATS_CONFIG.specialist }
    },
    '2024-11': {
        director: {
            ...STATS_CONFIG.director,
            monthlyTasks: 110,
            valueCreated: 142.3,
            workDays: 72
        },
        specialist: {
            ...STATS_CONFIG.specialist,
            monthlyTasks: 88,
            valueCreated: 71.2,
            workDays: 41
        }
    },
    '2024-10': {
        director: {
            ...STATS_CONFIG.director,
            monthlyTasks: 95,
            valueCreated: 128.5,
            workDays: 65
        },
        specialist: {
            ...STATS_CONFIG.specialist,
            monthlyTasks: 76,
            valueCreated: 64.8,
            workDays: 38
        }
    }
};

// 数据获取函数
class StatsManager {
    // 获取当前月份数据
    static getCurrentMonthData() {
        const currentMonth = '2024-12'; // 可以动态获取当前月份
        return MONTHLY_DATA[currentMonth];
    }
    
    // 获取首页统计数据
    static getHomepageStats() {
        return {
            monthlyCost: CALCULATED_STATS.totalCost,
            totalValue: CALCULATED_STATS.totalValueCreated,
            roi: CALCULATED_STATS.roi,
            totalExperts: CALCULATED_STATS.totalExperts,
            riskAlerts: STATS_CONFIG.intelligence.riskAlerts,
            opportunities: STATS_CONFIG.intelligence.opportunitiesFound,
            competitorUpdates: STATS_CONFIG.intelligence.competitorUpdates,
            alertAccuracy: STATS_CONFIG.intelligence.alertAccuracy
        };
    }
    
    // 获取总监工作区数据
    static getDirectorStats(month = '2024-12') {
        const data = MONTHLY_DATA[month]?.director || STATS_CONFIG.director;
        return {
            monthlyTasks: data.monthlyTasks,
            strategicRoles: data.strategicRoles,
            workDays: data.workDays,
            equivalentExperts: data.equivalentExperts,
            valueCreated: data.valueCreated,
            pointsConsumed: data.pointsConsumed,
            costRMB: data.costRMB
        };
    }
    
    // 获取专员工作区数据
    static getSpecialistStats(month = '2024-12') {
        const data = MONTHLY_DATA[month]?.specialist || STATS_CONFIG.specialist;
        return {
            monthlyTasks: data.monthlyTasks,
            workRoles: data.workRoles,
            workDays: data.workDays,
            equivalentExperts: data.equivalentExperts,
            valueCreated: data.valueCreated,
            pointsConsumed: data.pointsConsumed,
            costRMB: data.costRMB
        };
    }
    
    // 获取决策支撑数据
    static getIntelligenceStats() {
        return STATS_CONFIG.intelligence;
    }
    
    // 获取可用月份列表
    static getAvailableMonths() {
        return Object.keys(MONTHLY_DATA).sort().reverse();
    }
    
    // 获取图表数据
    static getChartData(year = 2024, member = 'team') {
        const yearData = CHART_DATA[year];
        if (!yearData) return null;
        
        const memberData = yearData[member];
        if (!memberData) return null;
        
        return {
            months: yearData.months,
            valueCreated: memberData.valueCreated,
            cost: memberData.cost
        };
    }
    
    // 获取可用年份列表
    static getAvailableYears() {
        return Object.keys(CHART_DATA).map(year => parseInt(year)).sort().reverse();
    }
    
    // 更新页面统计数据
    static updatePageStats(pageType, month = '2024-12') {
        switch(pageType) {
            case 'homepage':
                this.updateHomepageStats();
                break;
            case 'director':
                this.updateDirectorStats(month);
                break;
            case 'specialist':
                this.updateSpecialistStats(month);
                break;
        }
    }
    
    // 更新首页数据
    static updateHomepageStats() {
        const stats = this.getHomepageStats();
        
        // 更新统计卡片数据
        this.updateElementText('monthly-cost', `¥${stats.monthlyCost}万元/月`);
        this.updateElementText('total-value', `¥${stats.totalValue}万元/月`);
        this.updateElementText('roi-value', `${stats.roi}倍 ROI`);
        this.updateElementText('total-experts', `${stats.totalExperts}名高级专家`);
        
        // 更新决策支撑数据
        this.updateElementText('risk-alerts', stats.riskAlerts);
        this.updateElementText('opportunities-found', stats.opportunities);
        this.updateElementText('competitor-updates', stats.competitorUpdates);
        this.updateElementText('alert-accuracy', `${stats.alertAccuracy}%`);
    }
    
    // 更新总监工作区数据
    static updateDirectorStats(month) {
        const stats = this.getDirectorStats(month);
        
        this.updateElementText('statusStats', `${stats.monthlyTasks}个`);
        this.updateElementText('workforceStats', `${stats.workDays} 人天`);
        this.updateElementText('valueCreationStats', `${stats.valueCreated}万元`);
    }
    
    // 更新专员工作区数据
    static updateSpecialistStats(month) {
        const stats = this.getSpecialistStats(month);
        
        this.updateElementText('statusStats', `${stats.monthlyTasks}个`);
        this.updateElementText('workforceStats', `${stats.workDays} 人天`);
        this.updateElementText('valueCreationStats', `${stats.valueCreated}万元`);
    }
    
    // 辅助函数：更新DOM元素文本
    static updateElementText(elementId, text) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = text;
        }
    }
}

// 价值创造趋势图表管理器
class ChartManager {
    static chart = null;
    
    // 初始化图表
    static initChart() {
        const canvas = document.getElementById('valueChart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        // 获取初始数据
        const chartData = StatsManager.getChartData(2024, 'team');
        if (!chartData) return;
        
        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: chartData.months,
                datasets: [{
                    label: '累计创造价值 (万元)',
                    data: chartData.valueCreated,
                    borderColor: 'rgb(34, 197, 94)',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }, {
                    label: 'AI团队月度投入成本 (万元)',
                    data: chartData.cost,
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: false
                    },
                    legend: {
                        display: false // 使用自定义图例
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '金额 (万元)',
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '月份',
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                hover: {
                    animationDuration: 0
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        });
        
        // 绑定选择器事件
        this.bindSelectors();
    }
    
    // 绑定选择器事件
    static bindSelectors() {
        const yearSelector = document.getElementById('yearSelector');
        const memberSelector = document.getElementById('memberSelector');
        
        if (yearSelector) {
            yearSelector.addEventListener('change', () => {
                this.updateChart();
            });
        }
        
        if (memberSelector) {
            memberSelector.addEventListener('change', () => {
                this.updateChart();
            });
        }
    }
    
    // 更新图表
    static updateChart() {
        if (!this.chart) return;
        
        const yearSelector = document.getElementById('yearSelector');
        const memberSelector = document.getElementById('memberSelector');
        
        if (!yearSelector || !memberSelector) return;
        
        const year = parseInt(yearSelector.value);
        const member = memberSelector.value;
        
        const chartData = StatsManager.getChartData(year, member);
        if (!chartData) return;
        
        // 更新图表数据
        this.chart.data.labels = chartData.months;
        this.chart.data.datasets[0].data = chartData.valueCreated;
        this.chart.data.datasets[1].data = chartData.cost;
        
        // 重新渲染
        this.chart.update('active');
    }
}

// 页面加载时自动更新数据
document.addEventListener('DOMContentLoaded', function() {
    // 根据页面类型自动更新数据
    const currentPath = window.location.pathname;
    
    if (currentPath.includes('index.html') || currentPath.endsWith('/')) {
        StatsManager.updatePageStats('homepage');
        
        // 如果页面有图表容器，初始化图表
        if (document.getElementById('valueChart')) {
            // 等待Chart.js加载完成
            if (typeof Chart !== 'undefined') {
                ChartManager.initChart();
            } else {
                // 如果Chart.js还未加载，监听window.onload
                window.addEventListener('load', () => {
                    if (typeof Chart !== 'undefined') {
                        ChartManager.initChart();
                    }
                });
            }
        }
    } else if (currentPath.includes('workspace-director.html')) {
        StatsManager.updatePageStats('director');
    } else if (currentPath.includes('workspace-new.html')) {
        StatsManager.updatePageStats('specialist');
    }
});

// 导出给其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { StatsManager, ChartManager, STATS_CONFIG, CALCULATED_STATS, CHART_DATA };
} else {
    window.StatsManager = StatsManager;
    window.ChartManager = ChartManager;
    window.STATS_CONFIG = STATS_CONFIG;
    window.CALCULATED_STATS = CALCULATED_STATS;
    window.CHART_DATA = CHART_DATA;
}
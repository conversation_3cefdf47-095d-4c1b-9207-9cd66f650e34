<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - Foxu AI Team</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/@tailwindcss/forms@0.5.3/dist/forms.min.css" rel="stylesheet">
    <style>
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .tab-button.active {
            color: #3B82F6;
            border-bottom: 2px solid #3B82F6;
        }
        .verification-input {
            width: 40px;
            height: 40px;
            text-align: center;
            margin: 0 4px;
            border: 1px solid #7F7D7C;
            border-radius: 8px;
            background-color: #F8FAFC;
        }
        .verification-input:focus {
            border-color: #3B82F6;
            outline: none;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }
        .phone-input {
            letter-spacing: 2px;
        }
        .custom-input {
            height: 44px;
            border: 1px solid #7F7D7C;
            background-color: #F8FAFC;
        }
        .custom-input:focus {
            border-color: #3B82F6;
            outline: none;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }
        .page-bg {
            background: linear-gradient(135deg, #E0F2FE 0%, #F5F3FF 50%, #FDF2F8 100%);
            background: linear-gradient(
                to right,
                #0080FE,
                #3199FE,
                #5FB2FE,
                #90CCFE,
                #C1E5FE,
                #FFFFFF
            );

        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center page-bg">
    <div class="max-w-md w-full mx-4">
        <!-- 登录表单卡片 -->
        <div class="bg-white/80 backdrop-blur-lg rounded-xl shadow-lg p-8 border border-white/20">
            <!-- Logo -->
            <div class="text-center mb-8">
                <!-- <img src="assets/icons/logo_long.svg" alt="Foxu AI Team" class="h-12 mx-auto"> -->
                <h1 class="mt-4 text-2xl font-bold text-gray-900">欢迎使用 Foxu AI Team</h1>
                <p class="mt-2 text-sm text-gray-600">登录以访问您的AI团队</p>
            </div>

            <!-- 登录方式切换 -->
            <div class="flex border-b border-gray-200 mb-6">
                <button class="tab-button active flex-1 py-2 text-sm font-medium" data-tab="email">
                    邮箱登录
                </button>
                <button class="tab-button flex-1 py-2 text-sm font-medium text-gray-500" data-tab="phone">
                    手机号登录
                </button>
            </div>

            <!-- 邮箱登录表单 -->
            <div id="emailTab" class="tab-content active">
                <form id="emailForm" class="space-y-6">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">邮箱地址</label>
                        <input type="email" id="email" name="email" 
                               class="mt-1 block w-full rounded-lg custom-input px-4"
                               placeholder="请输入邮箱地址">
                        <p class="mt-1 text-sm text-red-600 hidden" id="emailError"></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">验证码</label>
                        <div class="flex items-center gap-3">
                            <div class="flex-1 flex space-x-1">
                                <input type="text" maxlength="1" class="verification-input" pattern="[0-9]">
                                <input type="text" maxlength="1" class="verification-input" pattern="[0-9]">
                                <input type="text" maxlength="1" class="verification-input" pattern="[0-9]">
                                <span class="text-gray-400">-</span>
                                <input type="text" maxlength="1" class="verification-input" pattern="[0-9]">
                                <input type="text" maxlength="1" class="verification-input" pattern="[0-9]">
                                <input type="text" maxlength="1" class="verification-input" pattern="[0-9]">
                            </div>
                            <button type="button" id="sendEmailCode" 
                                    class="px-3 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors whitespace-nowrap">
                                获取验证码
                            </button>
                        </div>
                    </div>

                    <div>
                        <button type="submit" 
                                class="w-full py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all">
                            登录/注册
                        </button>
                    </div>
                </form>
            </div>

            <!-- 手机号登录表单 -->
            <div id="phoneTab" class="tab-content">
                <form id="phoneForm" class="space-y-6">
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700">手机号</label>
                        <input type="tel" id="phone" name="phone" 
                               class="mt-1 block w-full rounded-lg custom-input px-4 phone-input"
                               placeholder="请输入手机号" maxlength="13">
                        <p class="mt-1 text-sm text-red-600 hidden" id="phoneError"></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">验证码</label>
                        <div class="flex items-center gap-3">
                            <div class="flex-1 flex space-x-1">
                                <input type="text" maxlength="1" class="verification-input" pattern="[0-9]">
                                <input type="text" maxlength="1" class="verification-input" pattern="[0-9]">
                                <input type="text" maxlength="1" class="verification-input" pattern="[0-9]">
                                <span class="text-gray-400">-</span>
                                <input type="text" maxlength="1" class="verification-input" pattern="[0-9]">
                                <input type="text" maxlength="1" class="verification-input" pattern="[0-9]">
                                <input type="text" maxlength="1" class="verification-input" pattern="[0-9]">
                            </div>
                            <button type="button" id="sendPhoneCode" 
                                    class="px-3 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors whitespace-nowrap">
                                获取验证码
                            </button>
                        </div>
                    </div>

                    <div>
                        <button type="submit" 
                                class="w-full py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all">
                            登录/注册
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Tab切换
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.dataset.tab;
                
                // 更新按钮状态
                tabButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                
                // 更新内容显示
                tabContents.forEach(content => {
                    content.classList.remove('active');
                    if (content.id === `${tabId}Tab`) {
                        content.classList.add('active');
                    }
                });
            });
        });

        // 手机号格式化
        const phoneInput = document.getElementById('phone');
        phoneInput.addEventListener('input', (e) => {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 0) {
                if (value.length <= 3) {
                    value = value;
                } else if (value.length <= 7) {
                    value = value.slice(0, 3) + '-' + value.slice(3);
                } else {
                    value = value.slice(0, 3) + '-' + value.slice(3, 7) + '-' + value.slice(7, 11);
                }
            }
            e.target.value = value;
        });

        // 验证码输入框处理
        const verificationInputs = document.querySelectorAll('.verification-input');
        verificationInputs.forEach((input, index) => {
            input.addEventListener('input', (e) => {
                if (e.target.value.length === 1) {
                    if (index < verificationInputs.length - 1) {
                        verificationInputs[index + 1].focus();
                    }
                }
            });

            input.addEventListener('keydown', (e) => {
                if (e.key === 'Backspace' && !e.target.value && index > 0) {
                    verificationInputs[index - 1].focus();
                }
            });
        });

        // 验证码发送倒计时
        function startCountdown(button) {
            let countdown = 60;
            button.disabled = true;
            button.classList.add('opacity-50');
            
            const timer = setInterval(() => {
                button.textContent = `${countdown}秒后重试`;
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(timer);
                    button.disabled = false;
                    button.classList.remove('opacity-50');
                    button.textContent = '获取验证码';
                }
            }, 1000);
        }

        // 邮箱验证码发送
        document.getElementById('sendEmailCode').addEventListener('click', () => {
            const email = document.getElementById('email').value;
            const emailError = document.getElementById('emailError');
            
            if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                emailError.textContent = '请输入有效的邮箱地址';
                emailError.classList.remove('hidden');
                return;
            }
            
            emailError.classList.add('hidden');
            startCountdown(document.getElementById('sendEmailCode'));
        });

        // 手机号验证码发送
        document.getElementById('sendPhoneCode').addEventListener('click', () => {
            const phone = document.getElementById('phone').value;
            const phoneError = document.getElementById('phoneError');
            
            if (!phone || !/^\d{3}-\d{4}-\d{4}$/.test(phone)) {
                phoneError.textContent = '请输入有效的手机号';
                phoneError.classList.remove('hidden');
                return;
            }
            
            phoneError.classList.add('hidden');
            startCountdown(document.getElementById('sendPhoneCode'));
        });

        // 表单提交处理
        document.getElementById('emailForm').addEventListener('submit', (e) => {
            e.preventDefault();
            // TODO: 实现邮箱登录逻辑
            window.location.href = 'index.html';
        });

        document.getElementById('phoneForm').addEventListener('submit', (e) => {
            e.preventDefault();
            // TODO: 实现手机号登录逻辑
            window.location.href = 'index.html';
        });
    </script>
</body>
</html>
// Hire page functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeHirePage();
});

function initializeHirePage() {
    const marketSelect = document.getElementById('marketSelect');
    const specialistHireBtn = document.querySelector('.specialist-card .hire-btn');
    const directorHireBtn = document.querySelector('.director-card .hire-btn');
    const modal = document.getElementById('hireModal');
    const modalClose = document.querySelector('.modal-close');
    
    // Enable specialist hire button when market is selected
    if (marketSelect && specialistHireBtn) {
        marketSelect.addEventListener('change', function() {
            if (this.value) {
                specialistHireBtn.disabled = false;
            } else {
                specialistHireBtn.disabled = true;
            }
        });
    }
    
    // Hire button event listeners
    const hireBtns = document.querySelectorAll('.hire-btn[data-role]');
    hireBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const role = this.dataset.role;
            openHireModal(role);
        });
    });
    
    // Modal close functionality
    if (modalClose) {
        modalClose.addEventListener('click', closeModal);
    }
    
    // Close modal when clicking outside
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });
    }
    
    // ESC key to close modal
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });
}

function openHireModal(role) {
    const modal = document.getElementById('hireModal');
    const selectedRole = document.getElementById('selectedRole');
    const selectedMarket = document.getElementById('selectedMarket');
    const serviceType = document.getElementById('serviceType');
    const totalPrice = document.getElementById('totalPrice');
    
    let roleName, market, service, price;
    
    if (role === 'director') {
        roleName = '市场总监';
        market = '全球市场';
        service = '一次性激活 + 月度订阅';
        price = '¥2,999 + ¥1,999/月';
    } else if (role === 'specialist') {
        const marketSelect = document.getElementById('marketSelect');
        const selectedOption = marketSelect.options[marketSelect.selectedIndex];
        
        roleName = '目标市场专员';
        market = selectedOption.text;
        service = '一次性项目 + 月度监控';
        price = '¥1,999 + ¥899/月';
    }
    
    // Update modal content
    selectedRole.textContent = roleName;
    selectedMarket.textContent = market;
    serviceType.textContent = service;
    totalPrice.textContent = price;
    
    // Show modal
    modal.classList.add('active');
}

function closeModal() {
    const modal = document.getElementById('hireModal');
    modal.classList.remove('active');
}

function confirmHire() {
    // Get selected payment method
    const selectedPayment = document.querySelector('input[name="payment"]:checked');
    const paymentMethod = selectedPayment ? selectedPayment.value : 'alipay';
    
    // Show success message
    showNotification('雇佣成功！正在跳转到支付页面...', 'success');
    
    // Close modal
    closeModal();
    
    // Simulate payment redirect
    setTimeout(() => {
        showNotification('支付完成！AI员工正在激活中...', 'success');
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 2000);
    }, 2000);
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    
    const colors = {
        info: '#4f46e5',
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b'
    };
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1001;
        animation: slideIn 0.3s ease-out;
        max-width: 300px;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Global function for modal close button
window.closeModal = closeModal;
window.confirmHire = confirmHire;
/**
 * 团队数据管理器
 * 提供全球团队的数据和管理方法
 */

class TeamDataManager {
  constructor() {
    this.data = {
      overview: {
        activeEmployees: 3,
        vacantPositions: 2,
        plannedPositions: 1,
        coveragePercentage: 85,
        totalMarkets: 8,
        activeMarkets: 6
      },
      
      employees: [
        {
          id: 'alex-director',
          name: '<PERSON>',
          title: '市场总监',
          country: 'china',
          countryName: '中国',
          region: '亚太区',
          coordinates: [116.4074, 39.9042], // 北京
          status: 'active',
          experience: '8年战略分析经验',
          specializations: ['market_analysis', 'competitor_intel'],
          languages: ['中文', '英文'],
          workingHours: '北京时间 9:00-18:00',
          performance: {
            reportsCompleted: 156,
            accuracy: 94,
            responseTime: 2.5,
            clientSatisfaction: 4.8
          },
          recentActivity: [
            { date: '2024-01-15', action: '完成Q1全球市场分析报告' },
            { date: '2024-01-14', action: '识别东南亚新兴市场机会' },
            { date: '2024-01-13', action: '竞品定价策略分析' }
          ]
        },
        {
          id: 'emma-germany',
          name: '<PERSON>',
          title: '德国市场专员',
          country: 'germany',
          countryName: '德国',
          region: '欧洲',
          coordinates: [13.4050, 52.5200], // 柏林
          status: 'active',
          experience: '5年欧洲市场经验',
          specializations: ['consumer_research', 'trend_monitoring'],
          languages: ['德语', '英文'],
          workingHours: '柏林时间 8:00-17:00',
          performance: {
            reportsCompleted: 89,
            accuracy: 96,
            responseTime: 1.8,
            clientSatisfaction: 4.9
          },
          recentActivity: [
            { date: '2024-01-15', action: '监测到Bosch价格异动' },
            { date: '2024-01-14', action: '德国消费者趋势分析' },
            { date: '2024-01-13', action: '竞品促销活动跟踪' }
          ]
        },
        {
          id: 'michael-usa',
          name: 'Michael',
          title: '美国市场专员',
          country: 'usa',
          countryName: '美国',
          region: '北美',
          coordinates: [-74.0060, 40.7128], // 纽约
          status: 'idle',
          experience: '6年北美市场经验',
          specializations: ['digital_marketing', 'sales_strategy'],
          languages: ['英文', '西班牙语'],
          workingHours: '纽约时间 9:00-18:00',
          performance: {
            reportsCompleted: 67,
            accuracy: 91,
            responseTime: 3.2,
            clientSatisfaction: 4.6
          },
          recentActivity: [
            { date: '2024-01-12', action: '加入团队' },
            { date: '2024-01-13', action: '完成美国市场初步调研' },
            { date: '2024-01-14', action: '亚马逊平台政策分析' }
          ]
        }
      ],
      
      vacancies: [
        {
          id: 'uk-specialist',
          title: '英国市场专员',
          country: 'uk',
          countryName: '英国',
          region: '欧洲',
          coordinates: [-0.1276, 51.5074], // 伦敦
          priority: 'high',
          expectedStartDate: '2024-02-15',
          requirements: [
            '3年以上英国市场经验',
            '熟悉英国电商平台',
            '具备数据分析能力',
            '英语母语水平'
          ],
          marketPotential: {
            size: '中等规模市场',
            value: '$50M年市场潜力',
            growth: '15%年增长率',
            opportunities: [
              'Brexit后市场重构',
              '新兴品类增长',
              '数字化转型机会'
            ]
          },
          applicants: 8,
          recruitmentStatus: 'interviewing'
        },
        {
          id: 'japan-specialist',
          title: '日本市场专员',
          country: 'japan',
          countryName: '日本',
          region: '亚太',
          coordinates: [139.6917, 35.6895], // 东京
          priority: 'medium',
          expectedStartDate: '2024-03-01',
          requirements: [
            '了解日本消费文化',
            '日语商务水平',
            '电商平台运营经验',
            '品牌本地化能力'
          ],
          marketPotential: {
            size: '大规模市场',
            value: '$120M年市场潜力',
            growth: '8%年增长率',
            opportunities: [
              '高端消费群体',
              '品质导向市场',
              '创新产品接受度高'
            ]
          },
          applicants: 12,
          recruitmentStatus: 'sourcing_candidates'
        }
      ],
      
      planned: [
        {
          id: 'france-expansion',
          title: '法国市场专员',
          country: 'france',
          countryName: '法国',
          region: '欧洲',
          coordinates: [2.3522, 48.8566], // 巴黎
          plannedLaunchDate: '2024-Q2',
          priority: 'future',
          marketPotential: {
            size: '中大规模市场',
            value: '$80M年市场潜力',
            growth: '12%年增长率'
          },
          preparationTasks: [
            '市场调研和竞品分析',
            '法规和合规要求研究',
            '本地化策略制定',
            '合作伙伴识别'
          ],
          budget: {
            setup: 150000,
            annual: 800000
          }
        }
      ]
    };
    
    console.log('TeamDataManager initialized with data');
  }
  
  // 获取概览数据
  getOverview() {
    return this.data.overview;
  }
  
  // 获取所有员工
  getAllEmployees() {
    return this.data.employees;
  }
  
  // 获取活跃员工
  getActiveEmployees() {
    return this.data.employees.filter(emp => emp.status === 'active');
  }
  
  // 获取当前在线员工（模拟）
  getActiveEmployeesNow() {
    const now = new Date();
    const hour = now.getHours();
    
    return this.data.employees.filter(emp => {
      // 简单的时区模拟：假设在工作时间内就是在线
      if (emp.country === 'china') {
        return hour >= 9 && hour <= 18;
      } else if (emp.country === 'germany') {
        return (hour + 7) % 24 >= 8 && (hour + 7) % 24 <= 17;
      } else if (emp.country === 'usa') {
        return (hour - 13 + 24) % 24 >= 9 && (hour - 13 + 24) % 24 <= 18;
      }
      return false;
    });
  }
  
  // 获取职位空缺
  getVacancies() {
    return this.data.vacancies;
  }
  
  // 获取规划职位
  getPlannedPositions() {
    return this.data.planned;
  }
  
  // 获取市场覆盖情况
  getMarketCoverage() {
    const covered = this.data.employees.map(emp => emp.country);
    const recruiting = this.data.vacancies.map(vac => vac.country);
    const planned = this.data.planned.map(plan => plan.country);
    const future = ['australia', 'brazil', 'india'];
    
    return {
      covered,
      recruiting,
      planned,
      future,
      activeMarkets: covered.length,
      totalMarkets: covered.length + recruiting.length + planned.length + future.length,
      coveragePercentage: this.data.overview.coveragePercentage
    };
  }
  
  // 获取实时状态
  getRealtimeStatus() {
    return {
      timestamp: new Date().toISOString(),
      activeNow: this.getActiveEmployeesNow(),
      upcomingMeetings: [
        {
          title: '全球市场评估会议',
          time: '14:00 - 15:00',
          participants: 'Alex, Emma, Michael'
        },
        {
          title: '德国市场深度分析',
          time: '16:00 - 17:00',
          participants: 'Emma, David'
        }
      ],
      pendingTasks: [
        {
          task: '完成英国市场进入策略',
          deadline: '2024-01-20',
          assignee: 'Alex'
        },
        {
          task: '监控德国竞品动态',
          deadline: '每日',
          assignee: 'Emma'
        }
      ],
      recruitmentProgress: [
        {
          position: '英国市场专员',
          country: '英国',
          status: 'interviewing',
          applicants: 8
        },
        {
          position: '日本市场专员',
          country: '日本',
          status: 'sourcing_candidates',
          applicants: 12
        }
      ]
    };
  }
  
  // 获取即将到来的会议
  getUpcomingMeetings() {
    return [
      {
        title: '每周团队同步会议',
        time: '今天 14:00',
        participants: ['Alex', 'Emma', 'Michael'],
        type: 'recurring'
      },
      {
        title: '德国市场策略讨论',
        time: '明天 10:00',
        participants: ['Emma', 'David'],
        type: 'one-time'
      },
      {
        title: 'Q1业绩回顾',
        time: '本周五 15:00',
        participants: ['Alex', 'Emma', 'Michael', 'David'],
        type: 'quarterly'
      }
    ];
  }
  
  // 根据ID获取员工
  getEmployeeById(id) {
    return this.data.employees.find(emp => emp.id === id);
  }
  
  // 根据国家获取员工
  getEmployeeByCountry(country) {
    return this.data.employees.find(emp => emp.country === country);
  }
  
  // 根据ID获取职位空缺
  getVacancyById(id) {
    return this.data.vacancies.find(vac => vac.id === id);
  }
  
  // 更新员工状态
  updateEmployeeStatus(id, status) {
    const employee = this.getEmployeeById(id);
    if (employee) {
      employee.status = status;
      console.log(`Updated employee ${id} status to ${status}`);
    }
  }
  
  // 添加新员工
  addEmployee(employeeData) {
    this.data.employees.push(employeeData);
    this.data.overview.activeEmployees++;
    console.log('New employee added:', employeeData.name);
  }
  
  // 添加新的职位空缺
  addVacancy(vacancyData) {
    this.data.vacancies.push(vacancyData);
    this.data.overview.vacantPositions++;
    console.log('New vacancy added:', vacancyData.title);
  }
  
  // 获取统计数据
  getStatistics() {
    return {
      totalEmployees: this.data.employees.length,
      activeEmployees: this.getActiveEmployees().length,
      vacantPositions: this.data.vacancies.length,
      plannedPositions: this.data.planned.length,
      marketCoverage: this.data.overview.coveragePercentage
    };
  }
}

// 创建全局实例
const teamDataManager = new TeamDataManager();

// 导出到全局作用域
window.TeamDataManager = teamDataManager;

// 兼容CommonJS
if (typeof module !== 'undefined' && module.exports) {
  module.exports = TeamDataManager;
}

console.log('TeamDataManager loaded and available globally'); 
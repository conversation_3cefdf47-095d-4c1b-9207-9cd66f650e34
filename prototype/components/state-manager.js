/**
 * 全球团队Banner状态管理器
 * Task 5: 数据集成与状态管理
 */

class TeamStateManager {
  constructor() {
    this.state = {
      data: null,
      loading: false,
      error: null,
      lastUpdated: null,
      connectionStatus: 'connected',
      filters: {
        current: 'all',
        previous: null
      },
      ui: {
        activeModal: null,
        selectedCountry: null,
        mapZoom: 1,
        animationEnabled: true
      },
      cache: new Map(),
      subscribers: new Set(),
      config: {
        autoRefreshInterval: 60000, // 1分钟
        cacheExpireTime: 300000,    // 5分钟
        maxRetries: 3,
        retryDelay: 1000
      }
    };

    this.dataManager = null;
    this.refreshTimer = null;
    this.retryCount = 0;
    
    this.init();
  }

  init() {
    this.setupDataManager();
    this.setupEventListeners();
    this.startAutoRefresh();
    this.validateState();
    
    console.log('TeamStateManager initialized');
  }

  // ================== 数据管理器集成 ================== //

  setupDataManager() {
    if (typeof window.TeamDataManager !== 'undefined') {
      this.dataManager = window.TeamDataManager;
      this.loadInitialData();
    } else {
      this.setError('TeamDataManager not available');
      // 延迟重试
      setTimeout(() => this.setupDataManager(), 1000);
    }
  }

  async loadInitialData() {
    try {
      this.setLoading(true);
      
      const data = {
        overview: this.dataManager.getOverview(),
        employees: this.dataManager.getAllEmployees(),
        vacancies: this.dataManager.getVacancies(),
        planned: this.dataManager.getPlannedPositions(),
        marketCoverage: this.dataManager.getMarketCoverage(),
        realtimeStatus: this.dataManager.getRealtimeStatus()
      };

      this.setState({
        data,
        lastUpdated: new Date().toISOString(),
        error: null
      });

      this.retryCount = 0;
      console.log('Initial data loaded successfully');
      
    } catch (error) {
      this.handleError(error, 'loadInitialData');
    } finally {
      this.setLoading(false);
    }
  }

  // ================== 状态管理 ================== //

  setState(updates) {
    const prevState = { ...this.state };
    this.state = { ...this.state, ...updates };
    
    // 触发状态变化事件
    this.notifySubscribers(prevState, this.state);
    
    // 验证状态
    this.validateState();
    
    // 缓存重要状态
    this.cacheState();
  }

  getState() {
    return { ...this.state };
  }

  setLoading(loading) {
    this.setState({ loading });
  }

  setError(error) {
    const errorObj = typeof error === 'string' 
      ? new Error(error) 
      : error;
    
    this.setState({ 
      error: {
        message: errorObj.message,
        timestamp: new Date().toISOString(),
        stack: errorObj.stack
      }
    });
    
    console.error('TeamStateManager Error:', errorObj);
  }

  clearError() {
    this.setState({ error: null });
  }

  // ================== 数据缓存机制 ================== //

  cacheState() {
    const cacheKey = 'teamState';
    const cacheData = {
      data: this.state.data,
      lastUpdated: this.state.lastUpdated,
      timestamp: Date.now()
    };
    
    try {
      localStorage.setItem(cacheKey, JSON.stringify(cacheData));
      this.state.cache.set(cacheKey, cacheData);
    } catch (error) {
      console.warn('Failed to cache state:', error);
    }
  }

  loadCachedState() {
    const cacheKey = 'teamState';
    
    try {
      // 优先从内存缓存读取
      if (this.state.cache.has(cacheKey)) {
        const cached = this.state.cache.get(cacheKey);
        if (this.isCacheValid(cached)) {
          return cached;
        }
      }
      
      // 从localStorage读取
      const cached = localStorage.getItem(cacheKey);
      if (cached) {
        const parsed = JSON.parse(cached);
        if (this.isCacheValid(parsed)) {
          this.state.cache.set(cacheKey, parsed);
          return parsed;
        }
      }
    } catch (error) {
      console.warn('Failed to load cached state:', error);
    }
    
    return null;
  }

  isCacheValid(cacheData) {
    if (!cacheData || !cacheData.timestamp) return false;
    
    const age = Date.now() - cacheData.timestamp;
    return age < this.state.config.cacheExpireTime;
  }

  clearCache() {
    this.state.cache.clear();
    try {
      localStorage.removeItem('teamState');
    } catch (error) {
      console.warn('Failed to clear cache:', error);
    }
  }

  // ================== 数据同步 ================== //

  async syncData() {
    if (this.state.loading) return;
    
    try {
      this.setLoading(true);
      
      // 检查缓存
      const cached = this.loadCachedState();
      if (cached && !this.needsRefresh()) {
        this.setState({
          data: cached.data,
          lastUpdated: cached.lastUpdated
        });
        return;
      }
      
      // 获取最新数据
      await this.loadInitialData();
      
    } catch (error) {
      this.handleError(error, 'syncData');
    }
  }

  needsRefresh() {
    if (!this.state.lastUpdated) return true;
    
    const lastUpdate = new Date(this.state.lastUpdated);
    const now = new Date();
    const timeDiff = now - lastUpdate;
    
    return timeDiff > this.state.config.autoRefreshInterval;
  }

  async forceRefresh() {
    this.clearCache();
    await this.syncData();
  }

  // ================== 自动刷新 ================== //

  startAutoRefresh() {
    this.stopAutoRefresh();
    
    this.refreshTimer = setInterval(() => {
      if (document.visibilityState === 'visible') {
        this.syncData();
      }
    }, this.state.config.autoRefreshInterval);
  }

  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  // ================== 错误处理 ================== //

  async handleError(error, context) {
    console.error(`Error in ${context}:`, error);
    
    this.setError(error);
    
    // 如果是网络错误，尝试重试
    if (this.isNetworkError(error) && this.retryCount < this.state.config.maxRetries) {
      this.retryCount++;
      console.log(`Retrying ${context} (attempt ${this.retryCount})`);
      
      setTimeout(() => {
        this[context]?.();
      }, this.state.config.retryDelay * this.retryCount);
    } else {
      // 达到最大重试次数，使用缓存数据
      const cached = this.loadCachedState();
      if (cached) {
        console.log('Using cached data due to persistent errors');
        this.setState({
          data: cached.data,
          lastUpdated: cached.lastUpdated,
          connectionStatus: 'offline'
        });
      }
    }
  }

  isNetworkError(error) {
    return error.message.includes('fetch') || 
           error.message.includes('network') ||
           error.message.includes('timeout');
  }

  // ================== 数据验证 ================== //

  validateState() {
    const errors = [];
    
    // 验证数据结构
    if (this.state.data) {
      if (!this.validateDataStructure(this.state.data)) {
        errors.push('Invalid data structure');
      }
    }
    
    // 验证UI状态
    if (this.state.ui.mapZoom < 0.5 || this.state.ui.mapZoom > 3) {
      errors.push('Invalid map zoom level');
      this.setState({ ui: { ...this.state.ui, mapZoom: 1 } });
    }
    
    // 验证筛选器
    const validFilters = ['all', 'employees', 'vacancies', 'planned'];
    if (!validFilters.includes(this.state.filters.current)) {
      errors.push('Invalid filter');
      this.setState({ filters: { ...this.state.filters, current: 'all' } });
    }
    
    if (errors.length > 0) {
      console.warn('State validation errors:', errors);
    }
    
    return errors.length === 0;
  }

  validateDataStructure(data) {
    const requiredFields = ['overview', 'employees', 'vacancies', 'planned'];
    return requiredFields.every(field => data.hasOwnProperty(field));
  }

  // ================== 事件监听 ================== //

  setupEventListeners() {
    // 页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        this.syncData();
      } else {
        this.stopAutoRefresh();
      }
    });

    // 网络状态变化
    window.addEventListener('online', () => {
      this.setState({ connectionStatus: 'connected' });
      this.syncData();
    });

    window.addEventListener('offline', () => {
      this.setState({ connectionStatus: 'offline' });
    });

    // 页面卸载时清理
    window.addEventListener('beforeunload', () => {
      this.destroy();
    });
  }

  // ================== 订阅者模式 ================== //

  subscribe(callback) {
    this.state.subscribers.add(callback);
    
    // 返回取消订阅函数
    return () => {
      this.state.subscribers.delete(callback);
    };
  }

  notifySubscribers(prevState, newState) {
    this.state.subscribers.forEach(callback => {
      try {
        callback(newState, prevState);
      } catch (error) {
        console.error('Error in subscriber callback:', error);
      }
    });
  }

  // ================== UI状态管理 ================== //

  setFilter(filter) {
    if (filter !== this.state.filters.current) {
      this.setState({
        filters: {
          previous: this.state.filters.current,
          current: filter
        }
      });
    }
  }

  setActiveModal(modal) {
    this.setState({
      ui: { ...this.state.ui, activeModal: modal }
    });
  }

  setSelectedCountry(country) {
    this.setState({
      ui: { ...this.state.ui, selectedCountry: country }
    });
  }

  setMapZoom(zoom) {
    const clampedZoom = Math.max(0.5, Math.min(3, zoom));
    this.setState({
      ui: { ...this.state.ui, mapZoom: clampedZoom }
    });
  }

  toggleAnimation() {
    this.setState({
      ui: { 
        ...this.state.ui, 
        animationEnabled: !this.state.ui.animationEnabled 
      }
    });
  }

  // ================== 数据查询辅助方法 ================== //

  getEmployeeByCountry(country) {
    if (!this.state.data?.employees) return null;
    return this.state.data.employees.find(emp => emp.country === country);
  }

  getVacancyByCountry(country) {
    if (!this.state.data?.vacancies) return null;
    return this.state.data.vacancies.find(vac => vac.country === country);
  }

  getPlannedByCountry(country) {
    if (!this.state.data?.planned) return null;
    return this.state.data.planned.find(plan => plan.country === country);
  }

  getCountryDetails(country) {
    const employee = this.getEmployeeByCountry(country);
    if (employee) return { type: 'employee', data: employee };

    const vacancy = this.getVacancyByCountry(country);
    if (vacancy) return { type: 'vacancy', data: vacancy };

    const planned = this.getPlannedByCountry(country);
    if (planned) return { type: 'planned', data: planned };

    return null;
  }

  // ================== 性能优化 ================== //

  debounce(func, delay) {
    let timeoutId;
    return (...args) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  }

  throttle(func, limit) {
    let inThrottle;
    return (...args) => {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  // ================== 配置管理 ================== //

  updateConfig(newConfig) {
    this.setState({
      config: { ...this.state.config, ...newConfig }
    });

    // 重新启动自动刷新
    if (newConfig.autoRefreshInterval) {
      this.startAutoRefresh();
    }
  }

  getConfig() {
    return { ...this.state.config };
  }

  // ================== 调试和监控 ================== //

  getDebugInfo() {
    return {
      state: this.getState(),
      cacheSize: this.state.cache.size,
      subscribersCount: this.state.subscribers.size,
      retryCount: this.retryCount,
      lastError: this.state.error,
      memoryUsage: this.getMemoryUsage()
    };
  }

  getMemoryUsage() {
    if (performance.memory) {
      return {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + ' MB',
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + ' MB',
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + ' MB'
      };
    }
    return null;
  }

  // ================== 数据导出/导入 ================== //

  exportState() {
    return {
      data: this.state.data,
      lastUpdated: this.state.lastUpdated,
      filters: this.state.filters,
      ui: this.state.ui,
      config: this.state.config
    };
  }

  importState(stateData) {
    try {
      this.setState(stateData);
      console.log('State imported successfully');
    } catch (error) {
      console.error('Failed to import state:', error);
    }
  }

  // ================== 清理和销毁 ================== //

  destroy() {
    this.stopAutoRefresh();
    this.state.subscribers.clear();
    this.clearCache();
    
    console.log('TeamStateManager destroyed');
  }
}

// 单例模式
let stateManagerInstance = null;

function getStateManager() {
  if (!stateManagerInstance) {
    stateManagerInstance = new TeamStateManager();
  }
  return stateManagerInstance;
}

// 导出
window.TeamStateManager = TeamStateManager;
window.getStateManager = getStateManager;

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
  window.teamStateManager = getStateManager();
  console.log('Global TeamStateManager instance created');
});

// 如果在Node.js环境中
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { TeamStateManager, getStateManager };
} 
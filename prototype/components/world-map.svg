<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 500">
  <!-- 定义 -->
  <defs>
    <style>
      .country {
        fill: rgba(255, 255, 255, 0.3);
        stroke: rgba(255, 255, 255, 0.5);
        stroke-width: 0.5;
        cursor: pointer;
        transition: all 0.3s ease;
      }
      .country:hover {
        fill: rgba(255, 255, 255, 0.5);
        stroke: rgba(255, 255, 255, 0.8);
      }
      .has-employee {
        fill: rgba(34, 197, 94, 0.7) !important;
        stroke: rgba(34, 197, 94, 1) !important;
        stroke-width: 1.5 !important;
      }
      .has-vacancy {
        fill: rgba(249, 115, 22, 0.7) !important;
        stroke: rgba(249, 115, 22, 1) !important;
        stroke-width: 1.5 !important;
      }
      .has-planned {
        fill: rgba(168, 85, 247, 0.7) !important;
        stroke: rgba(168, 85, 247, 1) !important;
        stroke-width: 1.5 !important;
      }
    </style>
  </defs>

  <!-- 中国 -->
  <path id="china" class="country has-employee" d="M580 150 L650 140 L680 160 L700 180 L720 190 L730 210 L720 230 L700 240 L680 250 L650 245 L620 240 L600 230 L580 220 L570 200 L575 180 Z"/>
  
  <!-- 美国 -->
  <path id="usa" class="country has-employee" d="M150 180 L280 170 L290 200 L285 230 L270 250 L250 260 L220 265 L190 260 L160 250 L140 230 L135 200 Z"/>
  
  <!-- 德国 -->
  <path id="germany" class="country has-employee" d="M480 140 L510 135 L520 150 L515 165 L505 170 L485 168 L475 155 Z"/>
  
  <!-- 英国 -->
  <path id="uk" class="country has-vacancy" d="M440 120 L460 115 L465 130 L460 145 L450 150 L435 145 L430 130 Z"/>
  
  <!-- 日本 -->
  <path id="japan" class="country has-vacancy" d="M750 180 L770 175 L780 190 L775 205 L765 210 L755 205 L745 190 Z"/>
  
  <!-- 法国 -->
  <path id="france" class="country has-planned" d="M450 160 L475 155 L485 170 L480 185 L465 190 L445 185 L440 170 Z"/>
  
  <!-- 澳大利亚 -->
  <path id="australia" class="country" d="M700 350 L780 345 L800 365 L795 385 L775 395 L750 390 L720 385 L695 375 Z"/>
  
  <!-- 巴西 -->
  <path id="brazil" class="country" d="M250 280 L320 275 L340 300 L335 330 L315 350 L285 355 L260 345 L240 320 Z"/>
  
  <!-- 印度 -->
  <path id="india" class="country" d="M590 220 L630 215 L650 240 L645 260 L620 270 L595 265 L580 245 Z"/>
  
  <!-- 俄罗斯 -->
  <path id="russia" class="country" d="M500 80 L750 70 L780 90 L770 110 L740 120 L700 115 L650 120 L600 125 L550 120 L520 110 L490 95 Z"/>
  
  <!-- 加拿大 -->
  <path id="canada" class="country" d="M120 100 L300 90 L320 110 L310 130 L280 140 L250 135 L220 130 L190 125 L160 120 L130 115 Z"/>
  
  <!-- 墨西哥 -->
  <path id="mexico" class="country" d="M150 250 L220 245 L240 265 L235 280 L215 290 L185 285 L160 275 L145 260 Z"/>

  <!-- 地图标记 -->
  <!-- 中国总部标记 -->
  <g class="map-marker marker-headquarters marker-pulse" data-country="china" data-type="headquarters">
    <circle cx="620" cy="190" r="8" />
    <circle cx="620" cy="190" r="4" fill="white" opacity="0.8"/>
    <text x="620" y="195" text-anchor="middle" fill="white" font-size="8" font-weight="bold">HQ</text>
  </g>
  
  <!-- 德国员工标记 -->
  <g class="map-marker marker-employee marker-pulse" data-country="germany" data-type="employee">
    <circle cx="495" cy="152" r="6" />
    <circle cx="495" cy="152" r="3" fill="white" opacity="0.8"/>
  </g>
  
  <!-- 美国员工标记 -->
  <g class="map-marker marker-employee" data-country="usa" data-type="employee">
    <circle cx="215" cy="215" r="6" />
    <circle cx="215" cy="215" r="3" fill="white" opacity="0.8"/>
  </g>
  
  <!-- 英国职位空缺标记 -->
  <g class="map-marker marker-vacancy marker-pulse" data-country="uk" data-type="vacancy">
    <rect x="445" y="125" width="10" height="10" rx="2" />
    <rect x="447" y="127" width="6" height="6" rx="1" fill="white" opacity="0.8"/>
  </g>
  
  <!-- 日本职位空缺标记 -->
  <g class="map-marker marker-vacancy" data-country="japan" data-type="vacancy">
    <rect x="757" y="185" width="10" height="10" rx="2" />
    <rect x="759" y="187" width="6" height="6" rx="1" fill="white" opacity="0.8"/>
  </g>
  
  <!-- 法国规划职位标记 -->
  <g class="map-marker marker-planned" data-country="france" data-type="planned">
    <polygon points="460,170 467,175 460,180 453,175" />
    <polygon points="458,172 462,175 458,178 454,175" fill="white" opacity="0.8"/>
  </g>

  <!-- 图例 -->
  <g class="map-legend" transform="translate(20, 400)">
    <rect x="0" y="0" width="200" height="80" fill="rgba(0,0,0,0.7)" rx="8"/>
    
    <!-- 总部 -->
    <circle cx="15" cy="20" r="6" fill="#3b82f6" stroke="#2563eb" stroke-width="2"/>
    <text x="30" y="25" fill="white" font-size="12">总部</text>
    
    <!-- 员工 -->
    <circle cx="15" cy="40" r="6" fill="#22c55e" stroke="#16a34a" stroke-width="2"/>
    <text x="30" y="45" fill="white" font-size="12">在职员工</text>
    
    <!-- 职位空缺 -->
    <rect x="9" y="54" width="12" height="12" rx="2" fill="#f97316" stroke="#ea580c" stroke-width="2"/>
    <text x="30" y="65" fill="white" font-size="12">职位空缺</text>
    
    <!-- 规划职位 -->
    <polygon points="15,75 22,80 15,85 8,80" fill="#a855f7" stroke="#9333ea" stroke-width="2"/>
    <text x="105" y="25" fill="white" font-size="12">规划职位</text>
  </g>
</svg> 
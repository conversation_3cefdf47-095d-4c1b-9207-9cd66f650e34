/**
 * 全球团队Banner组件控制器
 * 负责地图加载、数据渲染、交互处理等功能
 */

class GlobalTeamBanner {
  constructor(containerId) {
    this.container = document.getElementById(containerId);
    this.mapLoaded = false;
    this.currentFilter = 'all';
    this.updateInterval = null;
    this.stateUnsubscribe = null;
    
    // 使用状态管理器（优先）或直接数据管理器（降级）
    this.setupDataSource();
    this.renderBannerHTML();
    this.init();
  }

  // 渲染Banner基础HTML结构
  renderBannerHTML() {
    const html = `
      <div class="team-banner-container">
        <!-- Banner Header -->
        <div class="banner-header">
          <div class="header-left">
            <h3 class="banner-title">全球AI团队布局</h3>
            <p class="banner-subtitle">实时查看您的AI团队在全球的分布情况</p>
          </div>
          <div class="header-right">
            <div class="team-stats">
              <div class="stat-item">
                <span class="stat-number" id="activeEmployeesCount">3</span>
                <span class="stat-label">活跃员工</span>
              </div>
              <div class="stat-item">
                <span class="stat-number" id="vacantPositionsCount">3</span>
                <span class="stat-label">空缺职位</span>
              </div>
              <div class="stat-item">
                <span class="stat-number" id="marketCoveragePercent">60</span>
                <span class="stat-label">市场覆盖率(%)</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Map Container -->
        <div class="map-container">
          <div class="map-controls">
            <button class="control-btn active" data-filter="all">全部显示</button>
            <button class="control-btn" data-filter="employees">现有员工</button>
            <button class="control-btn" data-filter="vacancies">招聘职位</button>
            <button class="control-btn" data-filter="planned">规划职位</button>
          </div>
          
          <div class="world-map-wrapper" id="mapWrapper">
            <div class="map-loading">
              <div class="loading-spinner"></div>
              <p>正在加载世界地图...</p>
            </div>
          </div>
        </div>

        <!-- Team Overview -->
        <div class="team-overview">
          <div class="overview-section">
            <h4>团队成员</h4>
            <div class="employees-list" id="employeesList">
              <!-- 员工列表将在这里渲染 -->
            </div>
          </div>
          
          <div class="overview-section">
            <h4>招聘职位</h4>
            <div class="vacancies-list" id="vacanciesList">
              <!-- 职位列表将在这里渲染 -->
            </div>
          </div>
          
          <div class="overview-section">
            <h4>快速操作</h4>
            <div class="quick-actions">
              <button class="action-btn primary" id="hireNewEmployeeBtn">
                <span class="btn-icon">➕</span>
                <span>雇佣新员工</span>
                <span class="btn-badge">3个职位</span>
              </button>
              <button class="action-btn secondary" id="viewAllMarketsBtn">
                <span class="btn-icon">🌍</span>
                <span>查看全部市场</span>
              </button>
              <button class="action-btn secondary" id="teamPerformanceBtn">
                <span class="btn-icon">📊</span>
                <span>团队报告</span>
              </button>
              <button class="action-btn secondary" id="scheduleCallBtn">
                <span class="btn-icon">📅</span>
                <span>安排会议</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
          <div class="status-left">
            <div class="connection-status connected">在线</div>
            <div class="team-status" id="teamStatusBadge">3名员工在线</div>
          </div>
          <div class="status-right">
            <span class="last-update">最后更新: 刚刚</span>
          </div>
        </div>

        <!-- Detail Modal -->
        <div class="detail-modal" id="detailModal" style="display: none;">
          <div class="modal-backdrop" id="modalBackdrop"></div>
          <div class="modal-content">
            <div class="modal-header">
              <h4 id="modalTitle">详细信息</h4>
              <button class="modal-close" id="modalCloseBtn">×</button>
            </div>
            <div class="modal-body" id="modalBody">
              <!-- 弹窗内容将在这里渲染 -->
            </div>
            <div class="modal-footer">
              <button class="modal-btn secondary" id="modalCancelBtn">取消</button>
              <button class="modal-btn primary" id="modalActionBtn">确认</button>
            </div>
          </div>
        </div>
      </div>
    `;
    
    this.container.innerHTML = html;
  }

  init() {
    console.log('Initializing Global Team Banner...');
    this.loadWorldMap();
    this.subscribeToStateChanges();
    this.renderTeamData();
    this.bindEvents();
    this.startRealTimeUpdate();
    
    // 初始化动画控制器
    this.initAnimations();
  }

  // 设置数据源
  setupDataSource() {
    // 优先使用状态管理器
    if (typeof window.getStateManager === 'function') {
      this.stateManager = window.getStateManager();
      this.useStateManager = true;
      console.log('Using TeamStateManager for data management');
    } 
    // 降级到直接数据管理器
    else if (typeof window.TeamDataManager !== 'undefined') {
      this.dataManager = window.TeamDataManager;
      this.useStateManager = false;
      console.log('Using direct TeamDataManager (fallback mode)');
    } 
    // 都不可用
    else {
      console.error('No data source available. Please load team-data.js and state-manager.js');
      this.showDataError();
      return;
    }
  }

  // 订阅状态变化
  subscribeToStateChanges() {
    if (this.useStateManager && this.stateManager) {
      this.stateUnsubscribe = this.stateManager.subscribe((newState, prevState) => {
        this.handleStateChange(newState, prevState);
      });
      console.log('Subscribed to state changes');
    }
  }

  // 处理状态变化
  handleStateChange(newState, prevState) {
    // 数据更新
    if (newState.data !== prevState.data) {
      this.renderTeamData();
    }

    // 筛选器变化
    if (newState.filters.current !== prevState.filters.current) {
      this.updateFilterUI(newState.filters.current);
      this.showMarkers(newState.filters.current);
    }

    // 错误状态变化
    if (newState.error !== prevState.error) {
      this.handleErrorState(newState.error);
    }

    // 加载状态变化
    if (newState.loading !== prevState.loading) {
      this.handleLoadingState(newState.loading);
    }

    // 连接状态变化
    if (newState.connectionStatus !== prevState.connectionStatus) {
      this.handleConnectionStatus(newState.connectionStatus);
    }
  }

  // 加载世界地图
  async loadWorldMap() {
    try {
      const mapWrapper = this.container.querySelector('#mapWrapper');
      const response = await fetch('assets/world-map.svg');
      const svgContent = await response.text();
      
      // 清除加载动画
      mapWrapper.innerHTML = svgContent;
      
      // 标记地图已加载
      this.mapLoaded = true;
      
      // 初始化地图交互
      this.initMapInteractions();
      
      // 显示所有标记
      this.showMarkers('all');
      
      console.log('World map loaded successfully');
    } catch (error) {
      console.error('Failed to load world map:', error);
      this.showMapError();
    }
  }

  // 显示地图加载错误
  showMapError() {
    const mapWrapper = this.container.querySelector('#mapWrapper');
    mapWrapper.innerHTML = `
      <div class="map-error">
        <div class="error-icon">❌</div>
        <p>地图加载失败</p>
        <button onclick="window.globalTeamBanner.loadWorldMap()" class="retry-btn">重试</button>
      </div>
    `;
  }

  // 初始化地图交互
  initMapInteractions() {
    const svg = this.container.querySelector('#mapWrapper svg');
    if (!svg) return;

    // 为国家添加点击事件
    const countries = svg.querySelectorAll('.country');
    countries.forEach(country => {
      country.addEventListener('click', (e) => {
        const countryId = e.target.id;
        this.handleCountryClick(countryId);
      });
      
      country.addEventListener('mouseenter', (e) => {
        this.handleCountryHover(e.target.id, true);
      });
      
      country.addEventListener('mouseleave', (e) => {
        this.handleCountryHover(e.target.id, false);
      });
    });

    // 为员工标记添加点击事件
    const markers = svg.querySelectorAll('.marker');
    markers.forEach(marker => {
      marker.addEventListener('click', (e) => {
        e.stopPropagation();
        const markerId = e.target.id;
        this.handleMarkerClick(markerId);
      });
    });
  }

  // 处理国家点击
  handleCountryClick(countryId) {
    const countryDetails = this.dataManager.getCountryDetails(countryId);
    if (countryDetails) {
      this.showDetailModal(countryDetails);
    } else {
      console.log(`No data available for country: ${countryId}`);
    }
  }

  // 处理国家悬浮
  handleCountryHover(countryId, isEnter) {
    const country = this.container.querySelector(`#${countryId}`);
    if (!country) return;

    if (isEnter) {
      country.style.fill = '#e0f2fe';
      country.style.stroke = '#0369a1';
      country.style.strokeWidth = '2';
      country.style.cursor = 'pointer';
    } else {
      country.style.fill = '';
      country.style.stroke = '';
      country.style.strokeWidth = '';
      country.style.cursor = '';
    }
  }

  // 处理标记点击
  handleMarkerClick(markerId) {
    const country = markerId.replace('marker-', '');
    this.handleCountryClick(country);
  }

  // 显示标记
  showMarkers(filter) {
    const svg = this.container.querySelector('#mapWrapper svg');
    if (!svg) return;

    const allMarkers = svg.querySelectorAll('.marker');
    allMarkers.forEach(marker => {
      marker.style.display = 'none';
    });

    switch (filter) {
      case 'all':
        allMarkers.forEach(marker => {
          marker.style.display = 'block';
        });
        break;
      case 'employees':
        const employeeMarkers = svg.querySelectorAll('.marker-headquarters, .marker-employee');
        employeeMarkers.forEach(marker => {
          marker.style.display = 'block';
        });
        break;
      case 'vacancies':
        const vacantMarkers = svg.querySelectorAll('.marker-vacant');
        vacantMarkers.forEach(marker => {
          marker.style.display = 'block';
        });
        break;
      case 'planned':
        const plannedMarkers = svg.querySelectorAll('.marker-planned');
        plannedMarkers.forEach(marker => {
          marker.style.display = 'block';
        });
        break;
    }

    this.currentFilter = filter;
  }

  // 渲染团队数据
  renderTeamData() {
    const data = this.getData();
    if (!data) {
      this.showDataError();
      return;
    }

    this.renderEmployeesList();
    this.renderVacanciesList();
    this.updateStatistics();
  }

  // 获取数据（统一接口）
  getData() {
    if (this.useStateManager && this.stateManager) {
      const state = this.stateManager.getState();
      return state.data;
    } else if (this.dataManager) {
      return {
        overview: this.dataManager.getOverview(),
        employees: this.dataManager.getAllEmployees(),
        vacancies: this.dataManager.getVacancies(),
        planned: this.dataManager.getPlannedPositions(),
        marketCoverage: this.dataManager.getMarketCoverage(),
        realtimeStatus: this.dataManager.getRealtimeStatus()
      };
    }
    return null;
  }

  // 错误状态处理
  handleErrorState(error) {
    if (error) {
      this.showErrorMessage(error.message);
    } else {
      this.hideErrorMessage();
    }
  }

  // 加载状态处理
  handleLoadingState(loading) {
    if (loading) {
      this.showLoadingIndicator();
    } else {
      this.hideLoadingIndicator();
    }
  }

  // 连接状态处理
  handleConnectionStatus(status) {
    const statusElement = this.container.querySelector('.connection-status');
    if (statusElement) {
      statusElement.className = `connection-status ${status}`;
      statusElement.textContent = status === 'connected' ? '在线' : '离线';
      statusElement.style.display = status === 'offline' ? 'block' : 'none';
    }
  }

  // 显示数据错误
  showDataError() {
    const errorHTML = `
      <div class="data-error">
        <i class="icon-alert"></i>
        <h3>数据加载失败</h3>
        <p>无法加载团队数据，请检查网络连接或刷新页面重试。</p>
        <button onclick="location.reload()" class="retry-btn">重新加载</button>
      </div>
    `;
    this.container.innerHTML = errorHTML;
  }

  // 显示错误消息
  showErrorMessage(message) {
    let errorElement = this.container.querySelector('.error-message');
    if (!errorElement) {
      errorElement = document.createElement('div');
      errorElement.className = 'error-message';
      this.container.insertBefore(errorElement, this.container.firstChild);
    }
    errorElement.innerHTML = `
      <i class="icon-warning"></i>
      <span>${message}</span>
      <button onclick="this.parentElement.style.display='none'">×</button>
    `;
    errorElement.style.display = 'block';
  }

  // 隐藏错误消息
  hideErrorMessage() {
    const errorElement = this.container.querySelector('.error-message');
    if (errorElement) {
      errorElement.style.display = 'none';
    }
  }

  // 显示加载指示器
  showLoadingIndicator() {
    let loadingElement = this.container.querySelector('.loading-indicator');
    if (!loadingElement) {
      loadingElement = document.createElement('div');
      loadingElement.className = 'loading-indicator';
      loadingElement.innerHTML = `
        <div class="loading-spinner"></div>
        <span>加载中...</span>
      `;
      this.container.appendChild(loadingElement);
    }
    loadingElement.style.display = 'flex';
  }

  // 隐藏加载指示器
  hideLoadingIndicator() {
    const loadingElement = this.container.querySelector('.loading-indicator');
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }
  }

  // 更新筛选器UI
  updateFilterUI(filter) {
    const buttons = this.container.querySelectorAll('.control-btn');
    buttons.forEach(btn => {
      btn.classList.toggle('active', btn.dataset.filter === filter);
    });
    this.currentFilter = filter;
  }

  // 渲染员工列表
  renderEmployeesList() {
    const employeesList = this.container.querySelector('#employeesList');
    const data = this.getData();
    if (!data || !data.employees) return;
    
    const employees = data.employees;

    employeesList.innerHTML = employees.map(employee => `
      <div class="employee-card" data-employee-id="${employee.id}">
        <div class="employee-avatar">
          ${this.getInitials(employee.name)}
        </div>
        <div class="employee-info">
          <div class="employee-name">${employee.name}</div>
          <div class="employee-role">${employee.title}</div>
          <div class="employee-location">${employee.countryName} • ${employee.workingHours}</div>
        </div>
        <div class="employee-status"></div>
      </div>
    `).join('');

    // 为员工卡片添加点击事件
    employeesList.querySelectorAll('.employee-card').forEach(card => {
      card.addEventListener('click', (e) => {
        const employeeId = e.currentTarget.dataset.employeeId;
        const employee = employees.find(emp => emp.id === employeeId);
        if (employee) {
          this.showDetailModal({ type: 'employee', data: employee });
        }
      });
    });
  }

  // 渲染职位空缺列表
  renderVacanciesList() {
    const vacanciesList = this.container.querySelector('#vacanciesList');
    const data = this.getData();
    if (!data || !data.vacancies) return;
    
    const vacancies = data.vacancies;

    vacanciesList.innerHTML = vacancies.map(vacancy => `
      <div class="vacancy-card" data-vacancy-id="${vacancy.id}">
        <div class="vacancy-title">${vacancy.title}</div>
        <div class="vacancy-location">${vacancy.countryName} • ${vacancy.region}</div>
        <span class="vacancy-priority ${vacancy.priority}">${this.getPriorityText(vacancy.priority)}</span>
      </div>
    `).join('');

    // 为职位卡片添加点击事件
    vacanciesList.querySelectorAll('.vacancy-card').forEach(card => {
      card.addEventListener('click', (e) => {
        const vacancyId = e.currentTarget.dataset.vacancyId;
        const vacancy = vacancies.find(v => v.id === vacancyId);
        if (vacancy) {
          this.showDetailModal({ type: 'vacancy', data: vacancy });
        }
      });
    });
  }

  // 更新统计数据
  updateStatistics() {
    const data = this.getData();
    if (!data || !data.overview) return;
    
    const overview = data.overview;
    
    // 更新头部统计
    const activeEmployeesEl = this.container.querySelector('#activeEmployeesCount');
    const vacantPositionsEl = this.container.querySelector('#vacantPositionsCount');
    const marketCoverageEl = this.container.querySelector('#marketCoveragePercent');
    
    if (activeEmployeesEl) activeEmployeesEl.textContent = overview.activeEmployees;
    if (vacantPositionsEl) vacantPositionsEl.textContent = overview.vacantPositions;
    if (marketCoverageEl) marketCoverageEl.textContent = overview.coveragePercentage + '%';

    // 更新状态徽章
    if (data.realtimeStatus && data.realtimeStatus.activeNow) {
      const activeNow = data.realtimeStatus.activeNow;
      const statusBadge = this.container.querySelector('#teamStatusBadge');
      if (statusBadge) {
        statusBadge.textContent = `${activeNow.length}名员工在线`;
      }
    }

    // 更新雇佣按钮徽章
    const hireBtn = this.container.querySelector('#hireNewEmployeeBtn .btn-badge');
    if (hireBtn) {
      hireBtn.textContent = `${overview.vacantPositions}个职位`;
    }
  }

  // 绑定事件
  bindEvents() {
    // 地图控制按钮
    const controlBtns = this.container.querySelectorAll('.control-btn');
    controlBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const filter = e.currentTarget.dataset.filter;
        this.handleFilterChange(filter, e.currentTarget);
      });
    });

    // 快速操作按钮
    this.container.querySelector('#hireNewEmployeeBtn').addEventListener('click', () => {
      this.handleHireAction();
    });

    this.container.querySelector('#viewAllMarketsBtn').addEventListener('click', () => {
      this.handleViewAllMarkets();
    });

    this.container.querySelector('#teamPerformanceBtn').addEventListener('click', () => {
      this.handleTeamPerformance();
    });

    this.container.querySelector('#scheduleCallBtn').addEventListener('click', () => {
      this.handleScheduleCall();
    });

    // 弹窗事件
    const modal = this.container.querySelector('#detailModal');
    const closeBtn = modal.querySelector('#modalCloseBtn');
    const cancelBtn = modal.querySelector('#modalCancelBtn');
    const backdrop = modal.querySelector('#modalBackdrop');

    [closeBtn, cancelBtn, backdrop].forEach(element => {
      element.addEventListener('click', () => {
        this.hideDetailModal();
      });
    });

    // ESC键关闭弹窗
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && modal.style.display !== 'none') {
        this.hideDetailModal();
      }
    });
  }

  // 处理筛选器变化
  handleFilterChange(filter, button) {
    // 使用动画版本（如果可用）
    if (this.animationController) {
      this.handleFilterChangeAnimated(filter, button);
    } else {
      // 降级到原版本
      this.container.querySelectorAll('.control-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      button.classList.add('active');
      this.showMarkers(filter);
    }
  }

  // 处理雇佣操作
  handleHireAction() {
    // 跳转到雇佣页面
    window.location.href = '/hire.html';
  }

  // 处理查看所有市场
  handleViewAllMarkets() {
    const marketData = this.dataManager.getMarketCoverage();
    this.showDetailModal({
      type: 'markets',
      data: marketData
    });
  }

  // 处理团队报告
  handleTeamPerformance() {
    const realtimeStatus = this.dataManager.getRealtimeStatus();
    this.showDetailModal({
      type: 'performance',
      data: realtimeStatus
    });
  }

  // 处理安排会议
  handleScheduleCall() {
    const meetings = this.dataManager.getUpcomingMeetings();
    this.showDetailModal({
      type: 'meetings',
      data: meetings
    });
  }

  // 显示详情弹窗
  showDetailModal(details) {
    // 使用动画版本（如果可用）
    if (this.animationController) {
      this.showDetailModalAnimated(details);
    } else {
      // 降级到原版本
      const modal = this.container.querySelector('#detailModal');
      const title = modal.querySelector('#modalTitle');
      const body = modal.querySelector('#modalBody');
      const actionBtn = modal.querySelector('#modalActionBtn');

      let modalContent = '';
      let modalTitle = '';
      let actionText = '查看详情';

      switch (details.type) {
        case 'employee':
          modalTitle = `${details.data.name} - ${details.data.title}`;
          modalContent = this.generateEmployeeModalContent(details.data);
          actionText = '发送消息';
          break;
        case 'vacancy':
          modalTitle = `${details.data.title} - ${details.data.countryName}`;
          modalContent = this.generateVacancyModalContent(details.data);
          actionText = '立即申请';
          break;
        case 'planned':
          modalTitle = `${details.data.title} - 规划中`;
          modalContent = this.generatePlannedModalContent(details.data);
          actionText = '了解更多';
          break;
        case 'markets':
          modalTitle = '全球市场覆盖';
          modalContent = this.generateMarketsModalContent(details.data);
          actionText = '扩展市场';
          break;
        case 'performance':
          modalTitle = '团队实时状态';
          modalContent = this.generatePerformanceModalContent(details.data);
          actionText = '详细报告';
          break;
        case 'meetings':
          modalTitle = '即将到来的会议';
          modalContent = this.generateMeetingsModalContent(details.data);
          actionText = '加入会议';
          break;
      }

      title.textContent = modalTitle;
      body.innerHTML = modalContent;
      actionBtn.textContent = actionText;
      
      modal.style.display = 'block';
      setTimeout(() => modal.classList.add('show'), 10);
    }
  }

  // 隐藏详情弹窗
  hideDetailModal() {
    // 使用动画版本（如果可用）
    if (this.animationController) {
      this.hideDetailModalAnimated();
    } else {
      // 降级到原版本
      const modal = this.container.querySelector('#detailModal');
      modal.classList.remove('show');
      setTimeout(() => modal.style.display = 'none', 200);
    }
  }

  // 生成员工详情内容
  generateEmployeeModalContent(employee) {
    return `
      <div class="employee-details">
        <div class="employee-header">
          <div class="employee-avatar-large">
            ${this.getInitials(employee.name)}
          </div>
          <div class="employee-meta">
            <h4>${employee.name}</h4>
            <p>${employee.title}</p>
            <p>${employee.countryName} • ${employee.region}</p>
          </div>
        </div>
        
        <div class="employee-info-grid">
          <div class="info-item">
            <strong>工作经验</strong>
            <p>${employee.experience}</p>
          </div>
          <div class="info-item">
            <strong>专业领域</strong>
            <p>${employee.specializations.map(s => this.getSpecializationText(s)).join(', ')}</p>
          </div>
          <div class="info-item">
            <strong>语言能力</strong>
            <p>${employee.languages.join(', ')}</p>
          </div>
          <div class="info-item">
            <strong>工作时间</strong>
            <p>${employee.workingHours}</p>
          </div>
        </div>

        <div class="performance-summary">
          <h5>工作表现</h5>
          <div class="performance-grid">
            <div class="perf-item">
              <span class="perf-number">${employee.performance.reportsCompleted}</span>
              <span class="perf-label">完成报告</span>
            </div>
            <div class="perf-item">
              <span class="perf-number">${employee.performance.accuracy}%</span>
              <span class="perf-label">准确率</span>
            </div>
            <div class="perf-item">
              <span class="perf-number">${employee.performance.responseTime}h</span>
              <span class="perf-label">响应时间</span>
            </div>
            <div class="perf-item">
              <span class="perf-number">${employee.performance.clientSatisfaction}</span>
              <span class="perf-label">客户满意度</span>
            </div>
          </div>
        </div>

        <div class="recent-activity">
          <h5>最近活动</h5>
          <ul>
            ${employee.recentActivity.map(activity => `
              <li>
                <span class="activity-date">${activity.date}</span>
                <span class="activity-action">${activity.action}</span>
              </li>
            `).join('')}
          </ul>
        </div>
      </div>
    `;
  }

  // 生成职位详情内容
  generateVacancyModalContent(vacancy) {
    return `
      <div class="vacancy-details">
        <div class="vacancy-header">
          <h4>${vacancy.title}</h4>
          <span class="vacancy-priority ${vacancy.priority}">${this.getPriorityText(vacancy.priority)}</span>
        </div>

        <div class="market-potential">
          <h5>市场潜力</h5>
          <div class="market-stats">
            <div class="market-stat">
              <strong>市场规模:</strong> ${vacancy.marketPotential.size}
            </div>
            <div class="market-stat">
              <strong>经济价值:</strong> ${vacancy.marketPotential.value}
            </div>
            <div class="market-stat">
              <strong>增长率:</strong> ${vacancy.marketPotential.growth}
            </div>
          </div>
        </div>

        <div class="opportunities">
          <h5>市场机会</h5>
          <ul>
            ${vacancy.marketPotential.opportunities.map(opp => `<li>${opp}</li>`).join('')}
          </ul>
        </div>

        <div class="requirements">
          <h5>职位要求</h5>
          <ul>
            ${vacancy.requirements.map(req => `<li>${req}</li>`).join('')}
          </ul>
        </div>

        <div class="recruitment-info">
          <div class="info-row">
            <strong>期望入职时间:</strong> ${vacancy.expectedStartDate}
          </div>
          <div class="info-row">
            <strong>申请人数:</strong> ${vacancy.applicants}人
          </div>
          <div class="info-row">
            <strong>招聘状态:</strong> ${this.getRecruitmentStatusText(vacancy.recruitmentStatus)}
          </div>
        </div>
      </div>
    `;
  }

  // 生成规划职位详情内容
  generatePlannedModalContent(planned) {
    return `
      <div class="planned-details">
        <div class="planned-header">
          <h4>${planned.title}</h4>
          <span class="planned-date">预计启动: ${planned.plannedLaunchDate}</span>
        </div>

        <div class="market-potential">
          <h5>市场潜力分析</h5>
          <div class="market-stats">
            <div class="market-stat">
              <strong>市场规模:</strong> ${planned.marketPotential.size}
            </div>
            <div class="market-stat">
              <strong>经济价值:</strong> ${planned.marketPotential.value}
            </div>
            <div class="market-stat">
              <strong>增长率:</strong> ${planned.marketPotential.growth}
            </div>
          </div>
        </div>

        <div class="preparation-tasks">
          <h5>筹备任务</h5>
          <ul>
            ${planned.preparationTasks.map(task => `<li>${task}</li>`).join('')}
          </ul>
        </div>

        <div class="budget-info">
          <h5>预算规划</h5>
          <div class="budget-grid">
            <div class="budget-item">
              <strong>启动成本:</strong> $${planned.budget.setup.toLocaleString()}
            </div>
            <div class="budget-item">
              <strong>年度预算:</strong> $${planned.budget.annual.toLocaleString()}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  // 生成市场覆盖详情内容
  generateMarketsModalContent(markets) {
    return `
      <div class="markets-overview">
        <div class="coverage-stats">
          <div class="stat">
            <span class="number">${markets.activeMarkets}</span>
            <span class="label">活跃市场</span>
          </div>
          <div class="stat">
            <span class="number">${markets.totalMarkets}</span>
            <span class="label">目标市场</span>
          </div>
          <div class="stat">
            <span class="number">${markets.coveragePercentage}%</span>
            <span class="label">覆盖率</span>
          </div>
        </div>

        <div class="markets-breakdown">
          <div class="market-category">
            <h5>✅ 已覆盖市场</h5>
            <ul>
              ${markets.covered.map(country => `<li>${this.getCountryName(country)}</li>`).join('')}
            </ul>
          </div>
          
          <div class="market-category">
            <h5>📋 招聘中市场</h5>
            <ul>
              ${markets.recruiting.map(country => `<li>${this.getCountryName(country)}</li>`).join('')}
            </ul>
          </div>
          
          <div class="market-category">
            <h5>📅 规划中市场</h5>
            <ul>
              ${markets.planned.map(country => `<li>${this.getCountryName(country)}</li>`).join('')}
            </ul>
          </div>
          
          <div class="market-category">
            <h5>🔮 未来市场</h5>
            <ul>
              ${markets.future.map(country => `<li>${this.getCountryName(country)}</li>`).join('')}
            </ul>
          </div>
        </div>
      </div>
    `;
  }

  // 生成团队表现详情内容
  generatePerformanceModalContent(status) {
    return `
      <div class="performance-overview">
        <div class="current-status">
          <h5>当前状态 (${new Date(status.timestamp).toLocaleTimeString()})</h5>
          <p><strong>在线员工:</strong> ${status.activeNow.map(emp => emp.name).join(', ')}</p>
        </div>

        <div class="upcoming-meetings">
          <h5>即将到来的会议</h5>
          <ul>
            ${status.upcomingMeetings.map(meeting => `
              <li>
                <strong>${meeting.title}</strong><br>
                <span>${meeting.time} • ${meeting.participants}</span>
              </li>
            `).join('')}
          </ul>
        </div>

        <div class="pending-tasks">
          <h5>待处理任务</h5>
          <ul>
            ${status.pendingTasks.map(task => `
              <li>
                <strong>${task.task}</strong><br>
                <span>截止: ${task.deadline} • 负责人: ${task.assignee}</span>
              </li>
            `).join('')}
          </ul>
        </div>

        <div class="recruitment-progress">
          <h5>招聘进度</h5>
          <ul>
            ${status.recruitmentProgress.map(progress => `
              <li>
                <strong>${progress.position} (${progress.country})</strong><br>
                <span>状态: ${this.getRecruitmentStatusText(progress.status)} • 申请人: ${progress.applicants}人</span>
              </li>
            `).join('')}
          </ul>
        </div>
      </div>
    `;
  }

  // 生成会议详情内容
  generateMeetingsModalContent(meetings) {
    return `
      <div class="meetings-list">
        ${meetings.map(meeting => `
          <div class="meeting-item">
            <h5>${meeting.title}</h5>
            <div class="meeting-details">
              <p><strong>时间:</strong> ${meeting.time}</p>
              <p><strong>参与者:</strong> ${Array.isArray(meeting.participants) ? meeting.participants.join(', ') : meeting.participants}</p>
              <p><strong>类型:</strong> ${meeting.type === 'recurring' ? '定期会议' : '专项会议'}</p>
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  // 启动实时更新
  startRealTimeUpdate() {
    // 每分钟更新一次状态
    this.updateInterval = setInterval(() => {
      this.updateRealTimeStatus();
    }, 60000);

    // 立即更新一次
    this.updateRealTimeStatus();
  }

  // 更新实时状态
  updateRealTimeStatus() {
    // 使用动画版本（如果可用）
    if (this.animationController) {
      this.updateRealTimeStatusAnimated();
    } else {
      // 降级到原版本
      const now = new Date();
      const lastUpdated = this.container.querySelector('#lastUpdated');
      lastUpdated.textContent = `更新于：${now.toLocaleTimeString()}`;
      this.updateStatistics();
    }
  }

  // 工具方法
  getInitials(name) {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  }

  getPriorityText(priority) {
    const priorities = {
      'high': '高优先级',
      'medium': '中等优先级',
      'low': '低优先级',
      'future': '未来规划'
    };
    return priorities[priority] || priority;
  }

  getSpecializationText(spec) {
    const specializations = {
      'market_analysis': '市场分析',
      'competitor_intel': '竞品情报',
      'consumer_research': '消费者研究',
      'trend_monitoring': '趋势监控',
      'digital_marketing': '数字营销',
      'sales_strategy': '销售策略'
    };
    return specializations[spec] || spec;
  }

  getRecruitmentStatusText(status) {
    const statuses = {
      'open_applications': '接受申请中',
      'sourcing_candidates': '寻找候选人',
      'interviewing': '面试阶段',
      'final_review': '最终评估'
    };
    return statuses[status] || status;
  }

  getCountryName(countryCode) {
    const countries = {
      'china': '中国',
      'usa': '美国',
      'germany': '德国',
      'uk': '英国',
      'france': '法国',
      'japan': '日本',
      'australia': '澳大利亚',
      'brazil': '巴西',
      'india': '印度'
    };
    return countries[countryCode] || countryCode;
  }

  // 初始化动画
  initAnimations() {
    // 如果动画控制器类可用，初始化动画
    if (typeof window.BannerAnimationController !== 'undefined') {
      this.animationController = new window.BannerAnimationController(this.container);
      console.log('动画控制器已集成到Banner组件');
    } else {
      console.warn('动画控制器不可用，跳过动画初始化');
    }
  }

  // 处理筛选器变化（增强版支持动画）
  handleFilterChangeAnimated(filter, button) {
    // 更新按钮状态
    this.container.querySelectorAll('.control-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    button.classList.add('active');

    // 如果有动画控制器，使用动画显示标记
    if (this.animationController) {
      this.animationController.animateMarkersSequence(filter);
    } else {
      // 降级到普通显示
      this.showMarkers(filter);
    }

    this.currentFilter = filter;
  }

  // 更新统计数据（增强版支持动画）
  updateStatisticsAnimated() {
    const overview = this.dataManager.getOverview();
    
    // 使用动画更新统计数字
    if (this.animationController) {
      this.animateStatUpdate('#activeEmployeesCount', overview.activeEmployees);
      this.animateStatUpdate('#vacantPositionsCount', overview.vacantPositions);
      this.animateStatUpdate('#marketCoveragePercent', overview.coveragePercentage + '%');
    } else {
      // 降级到普通更新
      this.container.querySelector('#activeEmployeesCount').textContent = overview.activeEmployees;
      this.container.querySelector('#vacantPositionsCount').textContent = overview.vacantPositions;
      this.container.querySelector('#marketCoveragePercent').textContent = overview.coveragePercentage + '%';
    }

    // 更新状态徽章
    const activeNow = this.dataManager.getActiveEmployeesNow();
    const statusBadge = this.container.querySelector('#teamStatusBadge');
    const newText = `${activeNow.length}名员工在线`;
    
    if (this.animationController) {
      this.animationController.animateBadgeUpdate(statusBadge, newText);
    } else {
      statusBadge.textContent = newText;
    }

    // 更新雇佣按钮徽章
    const hireBtn = this.container.querySelector('#hireNewEmployeeBtn .btn-badge');
    if (hireBtn) {
      const newBadgeText = `${overview.vacantPositions}个职位`;
      if (this.animationController) {
        this.animationController.animateBadgeUpdate(hireBtn, newBadgeText);
      } else {
        hireBtn.textContent = newBadgeText;
      }
    }
  }

  // 动画更新单个统计数字
  animateStatUpdate(selector, newValue) {
    const element = this.container.querySelector(selector);
    if (!element) return;

    const isPercentage = typeof newValue === 'string' && newValue.includes('%');
    const numericValue = parseInt(newValue.toString().replace(/[^\d]/g, ''));
    const currentValue = parseInt(element.textContent.replace(/[^\d]/g, '')) || 0;

    if (numericValue !== currentValue) {
      this.animationController.countUp(element, currentValue, numericValue, 1000, isPercentage);
    }
  }

  // 显示详情弹窗（增强版支持动画）
  showDetailModalAnimated(details) {
    const modal = this.container.querySelector('#detailModal');
    const title = modal.querySelector('#modalTitle');
    const body = modal.querySelector('#modalBody');
    const actionBtn = modal.querySelector('#modalActionBtn');

    // 生成内容（使用原有方法）
    let modalContent = '';
    let modalTitle = '';
    let actionText = '查看详情';

    switch (details.type) {
      case 'employee':
        modalTitle = `${details.data.name} - ${details.data.title}`;
        modalContent = this.generateEmployeeModalContent(details.data);
        actionText = '发送消息';
        break;
      case 'vacancy':
        modalTitle = `${details.data.title} - ${details.data.countryName}`;
        modalContent = this.generateVacancyModalContent(details.data);
        actionText = '立即申请';
        break;
      case 'planned':
        modalTitle = `${details.data.title} - 规划中`;
        modalContent = this.generatePlannedModalContent(details.data);
        actionText = '了解更多';
        break;
      case 'markets':
        modalTitle = '全球市场覆盖';
        modalContent = this.generateMarketsModalContent(details.data);
        actionText = '扩展市场';
        break;
      case 'performance':
        modalTitle = '团队实时状态';
        modalContent = this.generatePerformanceModalContent(details.data);
        actionText = '详细报告';
        break;
      case 'meetings':
        modalTitle = '即将到来的会议';
        modalContent = this.generateMeetingsModalContent(details.data);
        actionText = '加入会议';
        break;
    }

    title.textContent = modalTitle;
    body.innerHTML = modalContent;
    actionBtn.textContent = actionText;
    
    // 使用动画显示弹窗
    if (this.animationController && modal._originalShow) {
      modal._originalShow();
    } else {
      modal.style.display = 'block';
      setTimeout(() => modal.classList.add('show'), 10);
    }
  }

  // 隐藏详情弹窗（增强版支持动画）
  hideDetailModalAnimated() {
    const modal = this.container.querySelector('#detailModal');
    
    if (this.animationController && modal._originalHide) {
      modal._originalHide();
    } else {
      modal.classList.remove('show');
      setTimeout(() => modal.style.display = 'none', 200);
    }
  }

  // 更新实时状态（增强版支持动画）
  updateRealTimeStatusAnimated() {
    const now = new Date();
    const lastUpdated = this.container.querySelector('#lastUpdated');
    lastUpdated.textContent = `更新于：${now.toLocaleTimeString()}`;

    // 触发状态更新动画
    if (this.animationController) {
      this.animationController.animateStatusUpdate();
    }

    // 更新在线状态
    this.updateStatisticsAnimated();
  }

  // 销毁组件
  destroy() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
    
    if (this.animationController) {
      this.animationController.destroy();
    }

    // 取消状态管理器订阅
    if (this.stateUnsubscribe) {
      this.stateUnsubscribe();
      this.stateUnsubscribe = null;
    }
    
    console.log('Global Team Banner destroyed');
  }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
  // 确保数据文件已加载
  if (typeof window.TeamDataManager !== 'undefined') {
    window.globalTeamBanner = new GlobalTeamBanner('globalTeamBanner');
  } else {
    console.warn('TeamDataManager not available. Banner will not initialize.');
  }
});

// 导出类
window.GlobalTeamBanner = GlobalTeamBanner; 
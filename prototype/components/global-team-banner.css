/**
 * 全球团队Banner样式
 * Task 3: Banner组件开发 - 主样式文件
 */

/* ================== 基础容器样式 ================== */
.team-banner-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 32px;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
  margin-bottom: 2rem;
}

.team-banner-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.05"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.5;
  pointer-events: none;
}

/* ================== 头部区域 ================== */
.banner-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  position: relative;
  z-index: 2;
}

.banner-title {
  flex: 1;
}

.banner-title h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.banner-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.banner-stats {
  display: flex;
  gap: 24px;
  align-items: center;
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 16px 20px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 4px;
  color: #ffffff;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* ================== 控制按钮区域 ================== */
.map-controls {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: wrap;
  position: relative;
  z-index: 2;
}

.control-btn {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.control-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.control-btn:hover::before {
  left: 100%;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.control-btn.active {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  border-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
}

/* ================== 世界地图容器 ================== */
.world-map-container {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 400px;
  overflow: hidden;
}

.world-map-container svg {
  width: 100%;
  height: auto;
  display: block;
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1));
}

/* ================== 地图路径样式 ================== */
.world-map-container svg path {
  fill: rgba(255, 255, 255, 0.3);
  stroke: rgba(255, 255, 255, 0.5);
  stroke-width: 0.5;
  transition: all 0.3s ease;
  cursor: pointer;
}

.world-map-container svg path:hover {
  fill: rgba(255, 255, 255, 0.5);
  stroke: rgba(255, 255, 255, 0.8);
  stroke-width: 1;
}

.world-map-container svg path.has-employee {
  fill: rgba(34, 197, 94, 0.7);
  stroke: rgba(34, 197, 94, 1);
  stroke-width: 1.5;
}

.world-map-container svg path.has-vacancy {
  fill: rgba(249, 115, 22, 0.7);
  stroke: rgba(249, 115, 22, 1);
  stroke-width: 1.5;
}

.world-map-container svg path.has-planned {
  fill: rgba(168, 85, 247, 0.7);
  stroke: rgba(168, 85, 247, 1);
  stroke-width: 1.5;
}

/* ================== 地图标记 ================== */
.map-marker {
  cursor: pointer;
  transition: all 0.3s ease;
  transform-origin: center;
}

.map-marker:hover {
  transform: scale(1.2);
}

.marker-employee {
  fill: #22c55e;
  stroke: #16a34a;
  stroke-width: 2;
}

.marker-vacancy {
  fill: #f97316;
  stroke: #ea580c;
  stroke-width: 2;
}

.marker-planned {
  fill: #a855f7;
  stroke: #9333ea;
  stroke-width: 2;
}

.marker-headquarters {
  fill: #3b82f6;
  stroke: #2563eb;
  stroke-width: 3;
}

/* 标记脉冲动画 */
.marker-pulse {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* ================== 团队列表区域 ================== */
.team-lists {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
  position: relative;
  z-index: 2;
}

.team-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.team-section h3 {
  margin: 0 0 16px 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* ================== 员工卡片 ================== */
.employee-card, .vacancy-card, .planned-card {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  position: relative;
  overflow: hidden;
}

.employee-card::before, .vacancy-card::before, .planned-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.employee-card:hover::before, .vacancy-card:hover::before, .planned-card:hover::before {
  left: 100%;
}

.employee-card:hover, .vacancy-card:hover, .planned-card:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.employee-card {
  border-left: 4px solid #22c55e;
}

.vacancy-card {
  border-left: 4px solid #f97316;
}

.planned-card {
  border-left: 4px solid #a855f7;
}

.employee-header, .vacancy-header, .planned-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.employee-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.employee-info h4, .vacancy-info h4, .planned-info h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: white;
}

.employee-meta, .vacancy-meta, .planned-meta {
  font-size: 0.875rem;
  opacity: 0.8;
  margin: 4px 0;
}

.employee-status {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-active::before {
  content: '';
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #22c55e;
  animation: pulse 2s ease-in-out infinite;
}

.status-idle::before {
  content: '';
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #f59e0b;
}

/* ================== 快速操作按钮 ================== */
.quick-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  position: relative;
  z-index: 2;
}

.action-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.5s ease;
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn:hover {
  background: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.btn-badge {
  background: #ef4444;
  color: white;
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 600;
  margin-left: 4px;
}

/* ================== 模态框样式 ================== */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal.show {
  opacity: 1;
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 12px;
  padding: 0;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
  color: #374151;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.modal-btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 0.875rem;
}

.modal-btn-primary {
  background: #667eea;
  color: white;
}

.modal-btn-primary:hover {
  background: #5a67d8;
}

.modal-btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.modal-btn-secondary:hover {
  background: #e5e7eb;
}

/* ================== 响应式设计 ================== */
@media (max-width: 768px) {
  .team-banner-container {
    padding: 20px;
  }
  
  .banner-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .banner-title h2 {
    font-size: 2rem;
  }
  
  .banner-stats {
    gap: 12px;
  }
  
  .stat-item {
    padding: 12px 16px;
  }
  
  .stat-number {
    font-size: 1.5rem;
  }
  
  .map-controls {
    justify-content: center;
  }
  
  .control-btn {
    padding: 8px 16px;
    font-size: 0.8rem;
  }
  
  .team-lists {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .quick-actions {
    justify-content: center;
  }
  
  .action-btn {
    padding: 10px 20px;
    font-size: 0.8rem;
  }
  
  .modal-content {
    width: 95%;
    max-height: 90vh;
  }
}

/* ================== 加载动画 ================== */
.loading-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 
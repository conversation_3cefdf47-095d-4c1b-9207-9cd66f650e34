/**
 * 全球团队Banner动画控制器
 * Task 4: 动画效果实现
 */

class BannerAnimationController {
  constructor(bannerContainer) {
    this.container = bannerContainer;
    this.isInitialized = false;
    this.animationQueue = [];
    this.observers = new Map();
    
    this.init();
  }

  init() {
    this.setupIntersectionObserver();
    this.initializeAnimations();
    this.bindAnimationEvents();
    this.isInitialized = true;
    
    console.log('Banner动画控制器已初始化');
  }

  // ================== 交叉观察器设置 ================== //

  setupIntersectionObserver() {
    // 观察器配置
    const observerOptions = {
      root: null,
      rootMargin: '50px',
      threshold: 0.1
    };

    // 创建观察器
    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.triggerElementAnimation(entry.target);
        }
      });
    }, observerOptions);

    // 观察需要动画的元素
    this.observeAnimationElements();
  }

  observeAnimationElements() {
    const elements = [
      '.info-section',
      '.employee-card',
      '.vacancy-card',
      '.action-btn',
      '.stat-number'
    ];

    elements.forEach(selector => {
      const elements = this.container.querySelectorAll(selector);
      elements.forEach(element => {
        this.observer.observe(element);
      });
    });
  }

  // ================== 动画触发管理 ================== //

  triggerElementAnimation(element) {
    if (element.classList.contains('animate-in')) return;

    // 根据元素类型触发相应动画
    if (element.classList.contains('info-section')) {
      this.animateInfoSection(element);
    } else if (element.classList.contains('employee-card')) {
      this.animateEmployeeCard(element);
    } else if (element.classList.contains('vacancy-card')) {
      this.animateVacancyCard(element);
    } else if (element.classList.contains('action-btn')) {
      this.animateActionButton(element);
    } else if (element.classList.contains('stat-number')) {
      this.animateStatNumber(element);
    }
  }

  // ================== 初始化动画序列 ================== //

  initializeAnimations() {
    // 延迟执行整体动画序列
    setTimeout(() => {
      this.playBannerEntranceSequence();
    }, 300);
  }

  playBannerEntranceSequence() {
    const sequence = [
      { delay: 0, action: () => this.animateBanner() },
      { delay: 500, action: () => this.animateHeader() },
      { delay: 800, action: () => this.animateQuickStats() },
      { delay: 1200, action: () => this.animateMapContainer() },
      { delay: 1500, action: () => this.animateMapMarkers() },
      { delay: 2000, action: () => this.animateInfoPanels() }
    ];

    sequence.forEach(({ delay, action }) => {
      setTimeout(action, delay);
    });
  }

  // ================== 具体动画方法 ================== //

  animateBanner() {
    const banner = this.container.querySelector('.global-team-banner');
    if (banner) {
      banner.style.animation = 'bannerFadeIn 1s ease-out forwards';
    }
  }

  animateHeader() {
    const header = this.container.querySelector('.banner-header');
    if (header) {
      header.classList.add('animate-in');
    }
  }

  animateQuickStats() {
    const stats = this.container.querySelector('.quick-stats');
    if (stats) {
      stats.classList.add('animate-in');
      
      // 触发统计数字动画
      setTimeout(() => {
        this.animateAllStatNumbers();
      }, 300);
    }
  }

  animateMapContainer() {
    const mapContainer = this.container.querySelector('.world-map-container');
    if (mapContainer) {
      mapContainer.style.opacity = '0';
      mapContainer.style.transform = 'scale(0.95)';
      mapContainer.style.transition = 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
      
      setTimeout(() => {
        mapContainer.style.opacity = '1';
        mapContainer.style.transform = 'scale(1)';
      }, 100);
    }
  }

  animateMapMarkers() {
    const markers = this.container.querySelectorAll('.marker');
    markers.forEach((marker, index) => {
      setTimeout(() => {
        marker.classList.add('animate-in');
      }, index * 200);
    });
  }

  animateInfoPanels() {
    const sections = this.container.querySelectorAll('.info-section');
    sections.forEach((section, index) => {
      setTimeout(() => {
        this.animateInfoSection(section);
      }, index * 200);
    });
  }

  // ================== 数字滚动动画 ================== //

  animateAllStatNumbers() {
    const statNumbers = this.container.querySelectorAll('.stat-number');
    statNumbers.forEach(element => {
      this.animateStatNumber(element);
    });
  }

  animateStatNumber(element) {
    if (element.dataset.animated === 'true') return;
    
    const finalValue = element.textContent.replace(/[^\d]/g, '');
    const isPercentage = element.textContent.includes('%');
    
    if (!finalValue) return;
    
    element.dataset.animated = 'true';
    element.classList.add('counting');
    
    this.countUp(element, 0, parseInt(finalValue), 2000, isPercentage);
  }

  countUp(element, start, end, duration, isPercentage = false) {
    const range = end - start;
    const startTime = Date.now();
    
    const timer = setInterval(() => {
      const now = Date.now();
      const elapsed = now - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // 使用缓动函数
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const current = Math.floor(start + (range * easeOutQuart));
      
      element.textContent = current + (isPercentage ? '%' : '');
      
      if (progress >= 1) {
        clearInterval(timer);
        element.textContent = end + (isPercentage ? '%' : '');
        element.classList.remove('counting');
        
        // 添加市场覆盖率进度条
        if (isPercentage) {
          this.addCoverageProgressBar(element, end);
        }
      }
    }, 16); // 60 FPS
  }

  addCoverageProgressBar(element, percentage) {
    const progressBar = document.createElement('div');
    progressBar.className = 'coverage-progress';
    progressBar.style.setProperty('--progress-width', percentage + '%');
    
    element.parentNode.appendChild(progressBar);
    
    setTimeout(() => {
      progressBar.classList.add('animate-in');
    }, 100);
  }

  // ================== 元素动画方法 ================== //

  animateInfoSection(section) {
    section.classList.add('animate-in');
    
    // 动画其子元素
    const children = section.querySelectorAll('.employee-card, .vacancy-card, .action-btn');
    children.forEach((child, index) => {
      setTimeout(() => {
        child.classList.add('animate-in');
      }, index * 100);
    });
  }

  animateEmployeeCard(card) {
    if (card.classList.contains('animate-in')) return;
    
    card.classList.add('animate-in');
    
    // 添加状态点脉冲动画
    const statusDot = card.querySelector('.employee-status');
    if (statusDot) {
      setTimeout(() => {
        statusDot.classList.add('pulse');
      }, 500);
    }
  }

  animateVacancyCard(card) {
    card.classList.add('animate-in');
    
    // 添加虚线流动效果
    setTimeout(() => {
      card.style.background = 'linear-gradient(45deg, #f8fafc 25%, transparent 25%)';
      card.style.backgroundSize = '20px 20px';
      card.style.animation = 'vacantFlow 4s linear infinite';
    }, 300);
  }

  animateActionButton(button) {
    button.classList.add('animate-in');
    
    // 为主要按钮添加特殊效果
    if (button.classList.contains('primary')) {
      setTimeout(() => {
        this.addButtonShine(button);
      }, 500);
    }
  }

  addButtonShine(button) {
    const shine = document.createElement('div');
    shine.className = 'button-shine';
    shine.style.cssText = `
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      animation: buttonShine 2s ease-out;
    `;
    
    button.style.position = 'relative';
    button.style.overflow = 'hidden';
    button.appendChild(shine);
    
    // 定义动画
    const styleSheet = document.createElement('style');
    styleSheet.textContent = `
      @keyframes buttonShine {
        0% { left: -100%; }
        100% { left: 100%; }
      }
    `;
    document.head.appendChild(styleSheet);
    
    setTimeout(() => {
      button.removeChild(shine);
    }, 2000);
  }

  // ================== 交互动画 ================== //

  bindAnimationEvents() {
    // 地图控制按钮点击动画
    const controlBtns = this.container.querySelectorAll('.control-btn');
    controlBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.triggerRippleEffect(e.target, e);
      });
    });

    // 国家点击动画
    this.bindCountryClickAnimations();

    // 弹窗动画
    this.bindModalAnimations();

    // 员工卡片悬浮动画
    this.bindCardHoverAnimations();
  }

  triggerRippleEffect(element, event) {
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    const ripple = document.createElement('div');
    ripple.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      left: ${x}px;
      top: ${y}px;
      background: rgba(255, 255, 255, 0.5);
      border-radius: 50%;
      transform: scale(0);
      animation: ripple 0.6s ease-out;
      pointer-events: none;
    `;
    
    element.style.position = 'relative';
    element.style.overflow = 'hidden';
    element.appendChild(ripple);
    
    setTimeout(() => {
      element.removeChild(ripple);
    }, 600);
  }

  bindCountryClickAnimations() {
    // 需要等待地图加载完成
    setTimeout(() => {
      const countries = this.container.querySelectorAll('.country');
      countries.forEach(country => {
        country.addEventListener('click', () => {
          this.animateCountryClick(country);
        });
      });
    }, 2000);
  }

  animateCountryClick(country) {
    country.classList.add('clicked');
    setTimeout(() => {
      country.classList.remove('clicked');
    }, 600);
  }

  bindModalAnimations() {
    const modal = this.container.querySelector('#detailModal');
    if (!modal) return;

    // 重写显示弹窗方法
    const originalShow = () => {
      modal.style.display = 'block';
      setTimeout(() => {
        modal.classList.add('show');
      }, 10);
    };

    // 重写隐藏弹窗方法
    const originalHide = () => {
      modal.classList.add('hide');
      modal.classList.remove('show');
      setTimeout(() => {
        modal.style.display = 'none';
        modal.classList.remove('hide');
      }, 300);
    };

    // 存储原始方法
    modal._originalShow = originalShow;
    modal._originalHide = originalHide;
  }

  bindCardHoverAnimations() {
    const cards = this.container.querySelectorAll('.employee-card, .vacancy-card');
    cards.forEach(card => {
      card.addEventListener('mouseenter', () => {
        this.animateCardHover(card, true);
      });
      
      card.addEventListener('mouseleave', () => {
        this.animateCardHover(card, false);
      });
    });
  }

  animateCardHover(card, isEnter) {
    if (isEnter) {
      card.style.transform = 'translateY(-2px) scale(1.02)';
      card.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
    } else {
      card.style.transform = '';
      card.style.boxShadow = '';
    }
  }

  // ================== 实时状态动画 ================== //

  animateStatusUpdate() {
    const statusDot = this.container.querySelector('.status-dot');
    const lastUpdated = this.container.querySelector('.last-updated');
    
    if (statusDot) {
      statusDot.classList.add('updating');
      setTimeout(() => {
        statusDot.classList.remove('updating');
      }, 1000);
    }
    
    if (lastUpdated) {
      lastUpdated.classList.add('updating');
      setTimeout(() => {
        lastUpdated.classList.remove('updating');
      }, 500);
    }
  }

  // ================== 地图标记动画管理 ================== //

  animateMarkersSequence(filter = 'all') {
    const markers = this.getFilteredMarkers(filter);
    
    // 先隐藏所有标记
    this.container.querySelectorAll('.marker').forEach(marker => {
      marker.style.display = 'none';
      marker.classList.remove('animate-in');
    });
    
    // 按顺序显示标记
    markers.forEach((marker, index) => {
      setTimeout(() => {
        marker.style.display = 'block';
        setTimeout(() => {
          marker.classList.add('animate-in');
        }, 50);
      }, index * 150);
    });
  }

  getFilteredMarkers(filter) {
    const allMarkers = this.container.querySelectorAll('.marker');
    
    switch (filter) {
      case 'employees':
        return this.container.querySelectorAll('.marker-headquarters, .marker-employee');
      case 'vacancies':
        return this.container.querySelectorAll('.marker-vacant');
      case 'planned':
        return this.container.querySelectorAll('.marker-planned');
      default:
        return allMarkers;
    }
  }

  // ================== 徽章动画 ================== //

  animateBadgeUpdate(badge, newText) {
    badge.style.transform = 'scale(1.1)';
    badge.style.transition = 'transform 0.3s ease-out';
    
    setTimeout(() => {
      badge.textContent = newText;
      badge.style.transform = 'scale(1)';
    }, 150);
  }

  // ================== 销毁和清理 ================== //

  destroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
    
    // 清理动画队列
    this.animationQueue.forEach(timeout => clearTimeout(timeout));
    this.animationQueue = [];
    
    console.log('Banner动画控制器已销毁');
  }

  // ================== 性能监控 ================== //

  checkPerformance() {
    const now = performance.now();
    if (this.lastFrameTime) {
      const delta = now - this.lastFrameTime;
      if (delta > 33) { // 如果帧率低于30fps
        console.warn('动画性能警告: 帧率过低', delta + 'ms');
        this.optimizeForLowPerformance();
      }
    }
    this.lastFrameTime = now;
  }

  optimizeForLowPerformance() {
    // 禁用复杂动画
    const complexAnimations = this.container.querySelectorAll('.marker-headquarters, .marker-employee');
    complexAnimations.forEach(element => {
      element.style.animation = 'none';
    });
    
    console.log('已启用低性能模式');
  }
}

// 动画工具函数
const AnimationUtils = {
  // 缓动函数
  easing: {
    linear: t => t,
    easeInQuad: t => t * t,
    easeOutQuad: t => t * (2 - t),
    easeInOutQuad: t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
    easeInCubic: t => t * t * t,
    easeOutCubic: t => (--t) * t * t + 1,
    easeInOutCubic: t => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
  },

  // 创建自定义动画
  animate(element, properties, duration = 300, easing = 'easeOutQuad') {
    const start = performance.now();
    const startValues = {};
    const endValues = {};
    
    // 记录起始值和目标值
    Object.keys(properties).forEach(prop => {
      const currentValue = parseFloat(getComputedStyle(element)[prop]) || 0;
      startValues[prop] = currentValue;
      endValues[prop] = properties[prop] - currentValue;
    });
    
    const animate = (currentTime) => {
      const elapsed = currentTime - start;
      const progress = Math.min(elapsed / duration, 1);
      const easedProgress = AnimationUtils.easing[easing](progress);
      
      Object.keys(properties).forEach(prop => {
        const currentValue = startValues[prop] + (endValues[prop] * easedProgress);
        element.style[prop] = currentValue + (prop.includes('translate') ? 'px' : '');
      });
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    
    requestAnimationFrame(animate);
  },

  // 检查是否支持动画
  supportsAnimations() {
    const prefixes = ['', '-webkit-', '-moz-', '-ms-', '-o-'];
    const testElement = document.createElement('div');
    
    return prefixes.some(prefix => {
      testElement.style.cssText = `${prefix}animation: test 1s;`;
      return testElement.style.length > 0;
    });
  }
};

// 导出类和工具
window.BannerAnimationController = BannerAnimationController;
window.AnimationUtils = AnimationUtils;

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
  const banner = document.getElementById('globalTeamBanner');
  if (banner && AnimationUtils.supportsAnimations()) {
    window.bannerAnimationController = new BannerAnimationController(banner);
    console.log('Banner动画控制器自动初始化完成');
  }
}); 
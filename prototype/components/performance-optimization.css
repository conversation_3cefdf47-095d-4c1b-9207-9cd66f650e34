/**
 * 性能优化样式
 * Task 6: 样式优化与测试 - 性能优化
 */

/* ================== GPU加速优化 ================== */
.world-map-container,
.map-marker,
.control-btn,
.employee-card,
.vacancy-card,
.modal {
  transform: translateZ(0);
  will-change: transform;
}

.map-marker.pulse {
  will-change: transform, opacity;
}

.loading-spinner {
  will-change: transform;
}

/* ================== 关键CSS优化 ================== */
/* 首屏内容优先级最高 */
.team-banner-container {
  contain: layout style;
}

.world-map-container {
  contain: layout;
}

.map-controls {
  contain: layout style;
}

/* ================== 字体加载优化 ================== */
@font-face {
  font-family: 'System Font';
  src: local('-apple-system'), local('BlinkMacSystemFont'), local('Segoe UI');
  font-display: swap;
}

body {
  font-family: 'System Font', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* ================== 图像优化 ================== */
.world-map-container svg {
  shape-rendering: optimizeSpeed;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

.employee-avatar,
.map-marker {
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ================== 动画性能优化 ================== */
.marker-pulse {
  animation: pulse 2s ease-in-out infinite;
  transform: translate3d(0, 0, 0);
}

@keyframes pulse {
  0%, 100% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate3d(0, 0, 0) scale(1.1);
    opacity: 0.8;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out forwards;
  transform: translate3d(0, 0, 0);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate3d(0, 10px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* ================== 滚动性能优化 ================== */
.team-lists,
.modal-body {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* ================== 重绘优化 ================== */
.control-btn:hover,
.employee-card:hover,
.vacancy-card:hover {
  transform: translate3d(0, -2px, 0);
  will-change: transform, box-shadow;
}

/* ================== 内存优化 ================== */
.map-marker:not(.visible) {
  display: none;
}

.employee-card:not(.in-viewport),
.vacancy-card:not(.in-viewport) {
  content-visibility: auto;
  contain-intrinsic-size: 120px;
}

/* ================== 网络优化 ================== */
.world-map-container svg {
  loading: lazy;
}

/* ================== 低性能设备优化 ================== */
@media (max-resolution: 1dppx) {
  .marker-pulse {
    animation-duration: 3s;
  }
  
  .fade-in {
    animation: none;
    opacity: 1;
    transform: none;
  }
}

/* ================== 电池优化 ================== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01s !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01s !important;
  }
}

/* ================== CPU优化 ================== */
.team-stats {
  contain: style layout;
}

.employee-list,
.vacancy-list {
  contain: style layout;
}

/* ================== 渲染优化 ================== */
.modal-backdrop {
  backdrop-filter: blur(4px);
  will-change: opacity;
}

.loading-indicator {
  contain: strict;
  will-change: opacity, transform;
}

/* ================== 缓存优化 ================== */
.world-map-container svg path {
  vector-effect: non-scaling-stroke;
}

/* ================== 响应式图片优化 ================== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .employee-avatar {
    image-rendering: -webkit-optimize-contrast;
  }
} 
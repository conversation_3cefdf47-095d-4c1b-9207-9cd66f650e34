/**
 * 响应式设计优化
 * Task 6: 样式优化与测试
 */

/* 基础断点 */
:root {
  --breakpoint-xs: 480px;
  --breakpoint-sm: 768px;
  --breakpoint-md: 1024px;
  --breakpoint-lg: 1200px;
}

/* 容器响应式 */
.team-banner-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 16px;
}

@media (min-width: 768px) {
  .team-banner-container {
    padding: 24px;
  }
}

@media (min-width: 1024px) {
  .team-banner-container {
    max-width: 1200px;
    padding: 32px;
  }
}

/* 地图响应式 */
.world-map-container svg {
  width: 100%;
  height: auto;
  min-height: 300px;
}

@media (max-width: 480px) {
  .world-map-container svg {
    min-height: 250px;
  }
  
  .map-marker {
    transform: scale(0.8);
  }
}

/* 控制按钮响应式 */
.map-controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

@media (max-width: 480px) {
  .map-controls {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 4px;
  }
  
  .control-btn {
    font-size: 12px;
    padding: 6px 8px;
  }
}

/* 统计区域响应式 */
.team-stats {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
}

@media (max-width: 480px) {
  .team-stats {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}

/* ================== 世界地图响应式 ================== */
.world-map-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 12px;
}

.world-map-container svg {
  width: 100%;
  height: auto;
  min-height: 300px;
  max-height: 80vh;
}

/* 超小屏幕 (手机竖屏) */
@media (max-width: 480px) {
  .world-map-container {
    border-radius: 8px;
  }
  
  .world-map-container svg {
    min-height: 250px;
    max-height: 60vh;
  }
  
  /* 地图标记适配 */
  .map-marker {
    transform: scale(0.8);
  }
  
  .map-marker .marker-pulse {
    animation-duration: 3s; /* 减慢动画减少性能消耗 */
  }
}

/* 小屏幕 (手机横屏/小平板) */
@media (min-width: 481px) and (max-width: 768px) {
  .world-map-container svg {
    min-height: 350px;
    max-height: 70vh;
  }
  
  .map-marker {
    transform: scale(0.9);
  }
}

/* 中等屏幕 (平板) */
@media (min-width: 769px) and (max-width: 1024px) {
  .world-map-container svg {
    min-height: 400px;
    max-height: 75vh;
  }
}

/* 大屏幕 (桌面) */
@media (min-width: 1025px) {
  .world-map-container svg {
    min-height: 500px;
    max-height: 80vh;
  }
}

/* ================== 控制栏响应式 ================== */
.control-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-sm);
  border-radius: 6px;
  white-space: nowrap;
  min-width: 80px;
}

/* 超小屏幕控制栏 */
@media (max-width: 480px) {
  .map-controls {
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
  }
  
  .control-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-xs);
    min-width: 60px;
    flex: 1;
  }
  
  .map-controls {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xs);
  }
}

/* 小屏幕控制栏 */
@media (min-width: 481px) and (max-width: 768px) {
  .control-btn {
    font-size: var(--font-sm);
    min-width: 70px;
  }
  
  .map-controls {
    justify-content: space-around;
  }
}

/* ================== 统计区域响应式 ================== */
.team-stats {
  display: grid;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

/* 超小屏幕统计 */
@media (max-width: 480px) {
  .team-stats {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
  }
  
  .stat-card {
    padding: var(--spacing-sm);
    text-align: center;
  }
  
  .stat-number {
    font-size: var(--font-xl);
  }
  
  .stat-label {
    font-size: var(--font-xs);
  }
}

/* 小屏幕统计 */
@media (min-width: 481px) and (max-width: 768px) {
  .team-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
  }
  
  .stat-number {
    font-size: var(--font-2xl);
  }
}

/* 中等屏幕统计 */
@media (min-width: 769px) and (max-width: 1024px) {
  .team-stats {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 大屏幕统计 */
@media (min-width: 1025px) {
  .team-stats {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* ================== 团队列表响应式 ================== */
.team-lists {
  display: grid;
  gap: var(--spacing-lg);
  grid-template-columns: 1fr;
}

/* 小屏幕团队列表 */
@media (max-width: 768px) {
  .team-lists {
    gap: var(--spacing-md);
  }
  
  .team-section {
    margin-bottom: var(--spacing-md);
  }
  
  .team-section h3 {
    font-size: var(--font-lg);
    margin-bottom: var(--spacing-sm);
  }
  
  .employee-card,
  .vacancy-card {
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
  }
  
  .employee-avatar {
    width: 32px;
    height: 32px;
    font-size: var(--font-xs);
  }
  
  .employee-name {
    font-size: var(--font-sm);
  }
  
  .employee-role,
  .employee-location {
    font-size: var(--font-xs);
  }
}

/* 中等屏幕团队列表 */
@media (min-width: 769px) and (max-width: 1024px) {
  .team-lists {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
  }
}

/* 大屏幕团队列表 */
@media (min-width: 1025px) {
  .team-lists {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);
  }
}

/* ================== 模态框响应式 ================== */
.modal {
  padding: var(--spacing-md);
}

.modal-content {
  width: 90%;
  max-width: 600px;
  margin: 0 auto;
  max-height: 90vh;
  overflow-y: auto;
}

/* 超小屏幕模态框 */
@media (max-width: 480px) {
  .modal {
    padding: var(--spacing-sm);
  }
  
  .modal-content {
    width: 95%;
    max-height: 85vh;
    border-radius: 8px;
  }
  
  .modal-header {
    padding: var(--spacing-sm);
  }
  
  .modal-title {
    font-size: var(--font-lg);
  }
  
  .modal-body {
    padding: var(--spacing-sm);
    font-size: var(--font-sm);
  }
  
  .modal-footer {
    padding: var(--spacing-sm);
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .modal-footer .btn {
    width: 100%;
  }
}

/* 小屏幕模态框 */
@media (min-width: 481px) and (max-width: 768px) {
  .modal-content {
    width: 85%;
    max-height: 90vh;
  }
}

/* ================== 快速操作按钮响应式 ================== */
.quick-actions {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
  justify-content: center;
  margin-top: var(--spacing-lg);
}

.action-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-sm);
  border-radius: 8px;
  position: relative;
}

/* 超小屏幕快速操作 */
@media (max-width: 480px) {
  .quick-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-md);
  }
  
  .action-btn {
    width: 100%;
    padding: var(--spacing-sm);
    font-size: var(--font-sm);
    justify-content: center;
  }
  
  .btn-badge {
    position: static;
    margin-left: var(--spacing-xs);
  }
}

/* 小屏幕快速操作 */
@media (min-width: 481px) and (max-width: 768px) {
  .quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
  }
  
  .action-btn {
    width: 100%;
    justify-content: center;
  }
}

/* ================== 触摸优化 ================== */
@media (hover: none) and (pointer: coarse) {
  /* 触摸设备优化 */
  .control-btn,
  .action-btn,
  .employee-card,
  .vacancy-card {
    min-height: 44px; /* 苹果推荐的最小触摸目标 */
  }
  
  .map-marker {
    transform: scale(1.2); /* 增大触摸目标 */
  }
  
  .modal-close-btn {
    min-width: 44px;
    min-height: 44px;
  }
}

/* ================== 高分辨率屏幕优化 ================== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .world-map-container svg {
    shape-rendering: geometricPrecision;
  }
  
  .map-marker {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }
  
  .stat-number {
    font-weight: 500; /* 高分辨率下减少字重 */
  }
}

/* ================== 横屏模式优化 ================== */
@media (orientation: landscape) and (max-height: 600px) {
  .team-banner-container {
    padding: var(--spacing-sm);
  }
  
  .world-map-container svg {
    max-height: 50vh;
  }
  
  .modal-content {
    max-height: 80vh;
  }
  
  .team-stats {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-sm);
  }
}

/* ================== 打印样式 ================== */
@media print {
  .team-banner-container {
    max-width: none;
    padding: 0;
  }
  
  .map-controls,
  .quick-actions,
  .modal {
    display: none !important;
  }
  
  .world-map-container svg {
    max-height: none;
    height: 400px;
  }
  
  .team-stats {
    grid-template-columns: repeat(4, 1fr);
    page-break-inside: avoid;
  }
  
  .team-section {
    page-break-inside: avoid;
  }
  
  .employee-card,
  .vacancy-card {
    border: 1px solid #ccc;
    page-break-inside: avoid;
  }
}

/* ================== 减少动画设置 ================== */
@media (prefers-reduced-motion: reduce) {
  .map-marker .marker-pulse,
  .loading-spinner,
  .sync-animation {
    animation: none;
  }
  
  .control-btn,
  .action-btn,
  .employee-card,
  .vacancy-card {
    transition: none;
  }
  
  .modal {
    transition: none;
  }
}

/* ================== 高对比度模式 ================== */
@media (prefers-contrast: high) {
  .control-btn,
  .action-btn {
    border: 2px solid currentColor;
  }
  
  .employee-card,
  .vacancy-card,
  .stat-card {
    border: 1px solid #000;
  }
  
  .map-marker {
    stroke: #000;
    stroke-width: 2;
  }
}

/* ================== 暗色模式适配 ================== */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --border-color: #404040;
  }
  
  .team-banner-container {
    background: var(--bg-primary);
    color: var(--text-primary);
  }
  
  .world-map-container {
    background: var(--bg-secondary);
  }
  
  .control-btn {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-color);
  }
  
  .control-btn.active {
    background: #3b82f6;
    color: white;
  }
  
  .employee-card,
  .vacancy-card,
  .stat-card {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-color);
  }
  
  .modal-content {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-color: var(--border-color);
  }
}

/* ================== 特殊设备适配 ================== */

/* iPad Pro 横屏 */
@media (min-width: 1024px) and (max-width: 1366px) and (orientation: landscape) {
  .team-lists {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .world-map-container svg {
    max-height: 70vh;
  }
}

/* 小尺寸iPhone (SE等) */
@media (max-width: 375px) {
  .team-banner-container {
    padding: var(--spacing-sm);
  }
  
  .world-map-container svg {
    min-height: 200px;
  }
  
  .stat-number {
    font-size: var(--font-lg);
  }
  
  .control-btn {
    font-size: var(--font-xs);
    padding: 6px 8px;
  }
}

/* 超宽屏幕 */
@media (min-width: 1600px) {
  .team-banner-container {
    max-width: 1600px;
  }
  
  .team-lists {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .team-stats {
    grid-template-columns: repeat(6, 1fr);
  }
} 
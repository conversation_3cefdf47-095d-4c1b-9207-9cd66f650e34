class NotificationCenter extends HTMLElement {
    constructor() {
        super();
        this.attachShadow({ mode: 'open' });
    }

    connectedCallback() {
        this.render();
        this.initializeEventListeners();
    }

    render() {
        this.shadowRoot.innerHTML = `
            <style>
                :host {
                    display: block;
                }

                .notification-center {
                    position: fixed;
                    inset: 0;
                    background: rgba(0, 0, 0, 0.5);
                    z-index: 50;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s;
                }

                .notification-center.active {
                    opacity: 1;
                    visibility: visible;
                }

                .modal-content {
                    position: relative;
                    width: 100%;
                    max-width: 72rem;
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
                    transform: scale(0.95);
                    opacity: 0;
                    transition: all 0.3s;
                    margin: 2rem auto;
                    height: 90vh;
                    display: flex;
                    flex-direction: column;
                }

                .notification-center.active .modal-content {
                    transform: scale(1);
                    opacity: 1;
                }

                .modal-header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 1.5rem;
                    border-bottom: 1px solid #e5e7eb;
                }

                .modal-title {
                    font-size: 1.5rem;
                    font-weight: bold;
                    color: #1f2937;
                    margin: 10px 0;
                }

                .modal-subtitle {
                    font-size: 0.875rem;
                    color: #6b7280;
                }

                .close-button {
                    width: 2.5rem;
                    height: 2.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #9ca3af;
                    border-radius: 9999px;
                    transition: all 0.2s;
                    border: none;
                    cursor: pointer;
                    background: none ;
                    font-size: 1.5rem;
                }

                .close-button:hover {
                    background: #f3f4f6;
                    color: #4b5563;
                }

                .modal-body {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    min-height: 0;
                }

                .employee-scroll {
                    position: relative;
                    border-bottom: 1px solid #e5e7eb;
                    padding: 0 3rem;
                }

                .scroll-button {
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 2rem;
                    height: 2rem;
                    background: white;
                    border-radius: 9999px;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #4b5563;
                    transition: all 0.2s;
                    border: none;
                }

                .scroll-button:hover {
                    cursor: pointer;
                    background: #f9fafb;
                    color: #1f2937;
                }

                .scroll-left {
                    left: 1rem;
                }

                .scroll-right {
                    right: 1rem;
                }

                .employee-list {
                    display: flex;
                    overflow-x: auto;
                    padding: 0.75rem 0;
                    gap: 0.5rem;
                    scrollbar-width: none;
                }

                .employee-list::-webkit-scrollbar {
                    display: none;
                }

                .employee-card {
                    flex-shrink: 0;
                    width: 14rem;
                    margin: 0 0.5rem;
                    background: linear-gradient(to bottom right, #f0f9ff, white);
                    padding: 0.75rem;
                    border-radius: 0.5rem;
                    border: 2px solid #bfdbfe;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    transition: all 0.3s;
                    cursor: pointer;
                }

                .employee-card:hover {
                    transform: translateY(-0.25rem);
                    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
                }

                .employee-card.selected {
                    border-color: #3b82f6;
                    background: linear-gradient(to bottom right, #eff6ff, #f8fafc);
                    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
                }

                .employee-info {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .employee-avatar {
                    width: 2.5rem;
                    height: 2.5rem;
                    border-radius: 9999px;
                    border: 2px solid #bfdbfe;
                }

                .employee-details {
                    flex: 1;
                    min-width: 0;
                }

                .employee-name {
                    font-size: 0.875rem;
                    font-weight: 600;
                    color: #1f2937;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .employee-role {
                    font-size: 0.75rem;
                    color: #6b7280;
                }

                .unread-dot {
                    width: 0.75rem;
                    height: 0.75rem;
                    background: #ef4444;
                    border-radius: 9999px;
                    border: 2px solid white;
                }

                .chat-messages {
                    flex: 1;
                    overflow-y: auto;
                    padding: 1.5rem;
                    display: flex;
                    flex-direction: column;
                    gap: 1.5rem;
                }

                .message {
                    display: flex;
                    gap: 1rem;
                    width: 100%;
                }

                .message.user {
                    flex-direction: row;
                }

                .message-avatar {
                    flex-shrink: 0;
                }

                .message-content {
                    flex: 1;
                    min-width: 0;
                }

                .message.user .message-content {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-end;
                }

                .message-header {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin-bottom: 0.5rem;
                }

                .message.user .message-header {
                    flex-direction: row-reverse;
                }

                .message-name {
                    font-weight: 600;
                    color: #1f2937;
                }

                .message-time {
                    font-size: 0.75rem;
                    color: #6b7280;
                }

                .message-bubble {
                    padding: 1rem;
                    border-radius: 0.75rem;
                    border: 1px solid #bfdbfe;
                    width: fit-content;
                    max-width: 100%;
                }

                .message.user .message-bubble {
                    background: linear-gradient(to right, #f5f3ff, #ede9fe);
                    border-color: #c4b5fd;
                }

                .message-bubble.report {
                    background: linear-gradient(to right, #eff6ff, #dbeafe);
                    border-color: #bfdbfe;
                }

                .message-bubble.alert {
                    background: linear-gradient(to right, #fef2f2, #fee2e2);
                    border-color: #fecaca;
                }

                .message-bubble.chat {
                    background: linear-gradient(to right, #f9fafb, #f3f4f6);
                    border-color: #e5e7eb;
                }

                .message-text {
                    color: #1f2937;
                    line-height: 1.5;
                    margin: 4px 0;
                }

                .message-actions {
                    display: flex;
                    gap: 0.5rem;
                    margin-top: 0.75rem;
                }

                .action-button {
                    padding: 0.25rem 0.75rem;
                    border-radius: 0.5rem;
                    font-size: 0.75rem;
                    transition: all 0.2s;
                    border: none;
                }

                .action-button.primary.report {
                    background: #2563eb;
                    color: white;
                }

                .action-button.primary.report:hover {
                    background: #1d4ed8;
                }

                .action-button.secondary.report {
                    background: white;
                    color: #2563eb;
                    border: 1px solid #bfdbfe;
                }

                .action-button.secondary.report:hover {
                    background: #eff6ff;
                }

                .action-button.primary.alert {
                    background: #dc2626;
                    color: white;
                }

                .action-button.primary.alert:hover {
                    background: #b91c1c;
                }

                .action-button.secondary.alert {
                    background: white;
                    color: #dc2626;
                    border: 1px solid #fecaca;
                }

                .action-button.secondary.alert:hover {
                    background: #fef2f2;
                }

                .action-button.primary.chat {
                    background: #4b5563;
                    color: white;
                }

                .action-button.primary.chat:hover {
                    background: #374151;
                }

                .action-button.secondary.chat {
                    background: white;
                    color: #4b5563;
                    border: 1px solid #e5e7eb;
                }

                .action-button.secondary.chat:hover {
                    background: #f9fafb;
                }

                .chat-input {
                    border-top: 1px solid #e5e7eb;
                    padding: 1.5rem;
                }

                .input-group {
                    display: flex;
                    gap: 0.75rem;
                    margin-bottom: 1rem;
                }

                .message-input {
                    flex: 1;
                    padding: 0.75rem 1rem;
                    border: 1px solid #d1d5db;
                    border-radius: 0.75rem;
                    outline: none;
                    transition: all 0.2s;
                }

                .message-input:focus {
                    border-color: transparent;
                    box-shadow: 0 0 0 2px #3b82f6;
                }

                .send-button {
                    padding: 0.75rem 1.5rem;
                    background: #3b82f6;
                    color: white;
                    border-radius: 0.75rem;
                    font-weight: 500;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    transition: all 0.2s;
                    border: none;
                    cursor: pointer;
                }

                .send-button:hover {
                    background: #2563eb;
                }

                .quick-actions {
                    display: flex;
                    gap: 0.5rem;
                    flex-wrap: wrap;
                }

                .quick-action {
                    padding: 0.5rem 1rem;
                    background: #f3f4f6;
                    color: #374151;
                    border-radius: 0.75rem;
                    font-size: 0.875rem;
                    transition: all 0.2s;
                    border: none;
                    cursor: pointer;
                }

                .quick-action:hover {
                    background: #e5e7eb;
                }

                .message-tag {
                    padding: 0.25rem 0.75rem;
                    border-radius: 9999px;
                    font-size: 0.75rem;
                    font-weight: 500;
                }

                .message-tag.report {
                    background: #dbeafe;
                    color: #1e40af;
                }

                .message-tag.alert {
                    background: #fee2e2;
                    color: #991b1b;
                }

                .message-tag.chat {
                    background: #f3f4f6;
                    color: #1f2937;
                }
            </style>

            <div class="notification-center">
                <div class="modal-content">
                    <div class="modal-header">
                        <div>
                            <h3 class="modal-title">AI团队消息中心</h3>
                            <span class="modal-subtitle">与您的数字员工实时沟通</span>
                        </div>
                        <button class="close-button" id="closeButton">&times;</button>
                    </div>

                    <div class="modal-body">
                        <div class="employee-scroll">
                            <button class="scroll-button scroll-left" id="scrollLeft">
                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                                </svg>
                            </button>
                            
                            <div class="employee-list" id="employeeList">
                                <!-- 员工卡片将通过 JavaScript 动态添加 -->
                            </div>

                            <button class="scroll-button scroll-right" id="scrollRight">
                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                </svg>
                            </button>
                        </div>

                        <div class="chat-messages" id="chatMessages">
                            <!-- 消息内容将通过 JavaScript 动态添加 -->
                        </div>

                        <div class="chat-input">
                            <div class="input-group">
                                <input type="text" class="message-input" id="messageInput" placeholder="向AI团队提问或发布指令...">
                                <button class="send-button" id="sendButton">
                                    <span>📤</span>
                                    发送
                                </button>
                            </div>
                            <div class="quick-actions">
                                <button class="quick-action">📊 最新报告</button>
                                <button class="quick-action">⚠️ 重要预警</button>
                                <button class="quick-action">📈 市场趋势</button>
                                <button class="quick-action">🎯 制定策略</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    initializeEventListeners() {
        const notificationCenter = this.shadowRoot.querySelector('.notification-center');
        const closeButton = this.shadowRoot.querySelector('#closeButton');
        const employeeList = this.shadowRoot.querySelector('#employeeList');
        const scrollLeft = this.shadowRoot.querySelector('#scrollLeft');
        const scrollRight = this.shadowRoot.querySelector('#scrollRight');
        const messageInput = this.shadowRoot.querySelector('#messageInput');
        const sendButton = this.shadowRoot.querySelector('#sendButton');

        // 打开通知中心
        this.open = () => {
            notificationCenter.classList.add('active');
            document.body.style.overflow = 'hidden';
        };

        // 关闭通知中心
        this.close = () => {
            notificationCenter.classList.remove('active');
            document.body.style.overflow = '';
        };

        // 关闭按钮事件
        closeButton.addEventListener('click', () => this.close());

        // 点击外部关闭
        notificationCenter.addEventListener('click', (e) => {
            if (e.target === notificationCenter) {
                this.close();
            }
        });

        // ESC 键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && notificationCenter.classList.contains('active')) {
                this.close();
            }
        });

        // 员工列表滚动
        if (employeeList && scrollLeft && scrollRight) {
            const scrollStep = 400;
            
            scrollLeft.addEventListener('click', () => {
                employeeList.scrollBy({
                    left: -scrollStep,
                    behavior: 'smooth'
                });
            });

            scrollRight.addEventListener('click', () => {
                employeeList.scrollBy({
                    left: scrollStep,
                    behavior: 'smooth'
                });
            });

            employeeList.addEventListener('scroll', () => {
                scrollLeft.style.opacity = employeeList.scrollLeft > 0 ? '1' : '0';
                scrollRight.style.opacity = 
                    employeeList.scrollLeft < (employeeList.scrollWidth - employeeList.clientWidth) ? '1' : '0';
            });

            scrollLeft.style.opacity = '0';
        }

        // 发送消息
        const sendMessage = () => {
            const message = messageInput.value.trim();
            if (message) {
                this.addMessage({
                    type: 'user',
                    text: message,
                    time: '刚刚'
                });
                messageInput.value = '';
            }
        };

        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // 初始化员工列表
        this.initializeEmployees();
    }

    initializeEmployees() {
        const employees = [
            {
                id: 'alex',
                name: 'Alex - 市场总监',
                role: '战略分析专家',
                avatar: 'assets/avatars/alex-director.svg',
                hasUnread: true
            },
            {
                id: 'emma',
                name: 'Emma - 德国专员',
                role: '德国市场专家',
                avatar: 'assets/avatars/emma-germany.svg',
                hasUnread: true
            },
            {
                id: 'michael',
                name: 'Michael - 美国专员',
                role: '美国市场专家',
                avatar: 'assets/avatars/michael-usa.svg',
                hasUnread: false
            },
            {
                id: 'sarah',
                name: 'Sarah - 英国专员',
                role: '英国市场专家',
                avatar: 'assets/avatars/michael-usa.svg',
                hasUnread: false
            },
            {
                id: 'pierre',
                name: 'Pierre - 法国专员',
                role: '法国市场专家',
                avatar: 'assets/avatars/michael-usa.svg',
                hasUnread: false
            },
            {
                id: 'yuki',
                name: 'Yuki - 日本专员',
                role: '日本市场专家',
                avatar: 'assets/avatars/michael-usa.svg',
                hasUnread: false
            }
        ];

        const employeeList = this.shadowRoot.querySelector('#employeeList');
        employeeList.innerHTML = employees.map(employee => `
            <div class="employee-card" data-employee="${employee.id}">
                <div class="employee-info">
                    <img src="${employee.avatar}" alt="${employee.name}" class="employee-avatar">
                    <div class="employee-details">
                        <div class="employee-name">${employee.name}</div>
                        <div class="employee-role">${employee.role}</div>
                    </div>
                    ${employee.hasUnread ? '<div class="unread-dot"></div>' : ''}
                </div>
            </div>
        `).join('');

        // 添加员工卡片点击事件
        employeeList.querySelectorAll('.employee-card').forEach(card => {
            card.addEventListener('click', () => {
                // 移除所有卡片的选中状态
                employeeList.querySelectorAll('.employee-card').forEach(c => {
                    c.classList.remove('selected');
                });
                // 添加当前卡片的选中状态
                card.classList.add('selected');
                
                const employeeId = card.dataset.employee;
                this.loadEmployeeMessages(employeeId);
                card.querySelector('.unread-dot')?.remove();
            });
        });

        // 默认选择第一个专员
        const firstCard = employeeList.querySelector('.employee-card');
        if (firstCard) {
            firstCard.classList.add('selected');
            this.loadEmployeeMessages('alex');
            firstCard.querySelector('.unread-dot')?.remove();
        }
    }

    loadEmployeeMessages(employeeId) {
        const messages = {
            alex: [
                {
                    type: 'ai',
                    name: 'Alex - 市场总监',
                    avatar: 'assets/avatars/alex-director.svg',
                    text: '📊 David，我刚完成了Q1全球市场分析。发现了几个有趣的趋势，特别是东南亚市场的增长潜力。需要我为您准备详细的进入策略吗？',
                    time: '2小时前',
                    tag: 'report',
                    actions: [
                        { text: '查看报告', type: 'primary' },
                        { text: '制定策略', type: 'secondary' }
                    ]
                }
            ],
            emma: [
                {
                    type: 'ai',
                    name: 'Emma - 德国专员',
                    avatar: 'assets/avatars/emma-germany.svg',
                    text: '🚨 紧急通知：Bosch在Amazon.de大幅降价，影响可能很大。我已经准备了应对建议，需要立即查看吗？',
                    time: '1小时前',
                    tag: 'alert',
                    actions: [
                        { text: '立即查看', type: 'primary' },
                        { text: '制定对策', type: 'secondary' }
                    ]
                }
            ],
            michael: [
                {
                    type: 'ai',
                    name: 'Michael - 美国专员',
                    avatar: 'assets/avatars/michael-usa.svg',
                    text: '👋 你好David！关于美国市场的情况，我想和您讨论一下最近亚马逊平台的新政策变化。这些变化可能会影响我们的产品上架策略。您有时间详细聊聊吗？',
                    time: '30分钟前',
                    tag: 'chat',
                    actions: [
                        { text: '立即回复', type: 'primary' },
                        { text: '稍后处理', type: 'secondary' }
                    ]
                }
            ]
        };

        const chatMessages = this.shadowRoot.querySelector('#chatMessages');
        chatMessages.innerHTML = (messages[employeeId] || []).map(msg => this.createMessageElement(msg)).join('');
    }

    createMessageElement(message) {
        if (message.type === 'user') {
            return `
                <div class="message user">
                    <div class="message-content">
                        <div class="message-header">
                            <strong class="message-name">您</strong>
                            <span class="message-time">${message.time}</span>
                        </div>
                        <div class="message-bubble">
                            <p class="message-text">${message.text}</p>
                        </div>
                    </div>
                    <div class="message-avatar">
                        <img src="assets/avatars/david-chen.svg" alt="David" class="employee-avatar">
                    </div>
                </div>
            `;
        } else {
            return `
                <div class="message">
                    <div class="message-avatar">
                        <img src="${message.avatar}" alt="${message.name}" class="employee-avatar">
                    </div>
                    <div class="message-content">
                        <div class="message-header">
                            <strong class="message-name">${message.name}</strong>
                            <span class="message-tag ${message.tag}">${this.getTagText(message.tag)}</span>
                            <span class="message-time">${message.time}</span>
                        </div>
                        <div class="message-bubble ${message.tag}">
                            <p class="message-text">${message.text}</p>
                            ${message.actions ? `
                                <div class="message-actions">
                                    ${message.actions.map(action => `
                                        <button class="action-button ${action.type} ${message.tag}">${action.text}</button>
                                    `).join('')}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        }
    }

    getTagText(tag) {
        const tags = {
            report: '📊 报告交付',
            alert: '🚨 智能预警',
            chat: '💬 普通对话'
        };
        return tags[tag] || '';
    }

    addMessage(message) {
        const chatMessages = this.shadowRoot.querySelector('#chatMessages');
        chatMessages.insertAdjacentHTML('beforeend', this.createMessageElement(message));
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

// 注册自定义元素
customElements.define('notification-center', NotificationCenter); 
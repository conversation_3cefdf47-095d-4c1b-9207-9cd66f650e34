/**
 * 全球团队Banner组件 - 动画效果增强
 * Task 4: 动画效果实现
 */

/* ================== 地图标记动画 ================== */

/* 标记淡入动画 */
.marker {
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.marker.animate-in {
  opacity: 1;
  transform: scale(1);
}

/* 总部标记特殊动画 - 更大的脉冲效果 */
.marker-headquarters {
  animation: headquartersPulse 3s ease-in-out infinite;
  box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
}

@keyframes headquartersPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* 员工标记跳跃动画 */
.marker-employee {
  animation: employeeBounce 2s ease-in-out infinite;
}

@keyframes employeeBounce {
  0%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
}

/* 空缺职位流动动画 */
.marker-vacant {
  animation: vacantFlow 4s linear infinite;
  stroke-dasharray: 8, 4;
}

@keyframes vacantFlow {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: 24;
  }
}

/* 规划中职位渐变动画 */
.marker-planned {
  animation: plannedPulse 6s ease-in-out infinite;
  stroke-dasharray: 10, 5;
}

@keyframes plannedPulse {
  0%, 100% {
    opacity: 0.5;
    stroke-dashoffset: 0;
  }
  50% {
    opacity: 1;
    stroke-dashoffset: 15;
  }
}

/* ================== 卡片进入动画 ================== */

/* 员工卡片错位进入动画 */
.employee-card {
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.5s ease-out;
}

.employee-card.animate-in {
  opacity: 1;
  transform: translateX(0);
}

/* 为每个卡片添加延迟 */
.employee-card:nth-child(1) { transition-delay: 0.1s; }
.employee-card:nth-child(2) { transition-delay: 0.2s; }
.employee-card:nth-child(3) { transition-delay: 0.3s; }

/* 职位卡片上升进入动画 */
.vacancy-card {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.vacancy-card.animate-in {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.vacancy-card:nth-child(1) { transition-delay: 0.1s; }
.vacancy-card:nth-child(2) { transition-delay: 0.2s; }

/* 快速操作按钮波浪式进入 */
.action-btn {
  opacity: 0;
  transform: scale(0.8) rotate(-2deg);
  transition: all 0.7s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.action-btn.animate-in {
  opacity: 1;
  transform: scale(1) rotate(0deg);
}

.action-btn:nth-child(1) { transition-delay: 0.1s; }
.action-btn:nth-child(2) { transition-delay: 0.2s; }
.action-btn:nth-child(3) { transition-delay: 0.3s; }
.action-btn:nth-child(4) { transition-delay: 0.4s; }

/* ================== 数字滚动动画 ================== */

/* 统计数字滚动效果 */
.stat-number {
  position: relative;
  overflow: hidden;
}

.stat-number.counting {
  animation: numberGlow 2s ease-out;
}

@keyframes numberGlow {
  0% {
    text-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    transform: scale(1.1);
  }
  100% {
    text-shadow: none;
    transform: scale(1);
  }
}

/* 进度条动画（用于市场覆盖率） */
.coverage-progress {
  position: relative;
  background: #e2e8f0;
  border-radius: 10px;
  height: 4px;
  margin-top: 4px;
  overflow: hidden;
}

.coverage-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #10b981);
  border-radius: 10px;
  width: 0%;
  transition: width 2s ease-out;
  animation: progressShine 3s infinite;
}

.coverage-progress.animate-in::after {
  width: var(--progress-width, 33%);
}

@keyframes progressShine {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.6);
  }
}

/* ================== 地图交互动画 ================== */

/* 国家悬浮高亮动画 */
.country {
  transition: all 0.3s ease-out;
  cursor: pointer;
}

.country:hover {
  filter: brightness(1.1);
  transform: scale(1.02);
  transform-origin: center;
}

/* 国家点击涟漪效果 */
.country.clicked {
  animation: countryRipple 0.6s ease-out;
}

@keyframes countryRipple {
  0% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.05);
    filter: brightness(1.3);
  }
  100% {
    transform: scale(1);
    filter: brightness(1);
  }
}

/* 地图加载动画 */
.map-loading {
  animation: mapLoadingPulse 2s ease-in-out infinite;
}

@keyframes mapLoadingPulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 0.4;
  }
}

/* ================== 弹窗动画 ================== */

/* 弹窗进入动画 */
.detail-modal {
  transition: all 0.3s ease-out;
}

.detail-modal.show {
  animation: modalSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes modalSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-50px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 弹窗退出动画 */
.detail-modal.hide {
  animation: modalSlideOut 0.3s ease-in forwards;
}

@keyframes modalSlideOut {
  0% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  100% {
    opacity: 0;
    transform: scale(0.9) translateY(30px);
  }
}

/* 模态框背景动画 */
.modal-backdrop {
  transition: backdrop-filter 0.3s ease-out, opacity 0.3s ease-out;
}

.detail-modal.show .modal-backdrop {
  animation: backdropFadeIn 0.3s ease-out;
}

@keyframes backdropFadeIn {
  0% {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  100% {
    opacity: 1;
    backdrop-filter: blur(4px);
  }
}

/* ================== 实时状态动画 ================== */

/* 状态指示器更新动画 */
.status-dot {
  position: relative;
}

.status-dot.updating {
  animation: statusUpdate 1s ease-out;
}

@keyframes statusUpdate {
  0% {
    transform: scale(1);
    background: #10b981;
  }
  50% {
    transform: scale(1.3);
    background: #3b82f6;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.6);
  }
  100% {
    transform: scale(1);
    background: #10b981;
  }
}

/* 实时更新文本动画 */
.last-updated {
  transition: color 0.3s ease-out;
}

.last-updated.updating {
  animation: textFlash 0.5s ease-out;
}

@keyframes textFlash {
  0%, 100% {
    color: #64748b;
  }
  50% {
    color: #3b82f6;
  }
}

/* ================== 徽章动画 ================== */

/* 团队状态徽章动画 */
.team-status-badge {
  position: relative;
  overflow: hidden;
}

.team-status-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: badgeShine 3s infinite;
}

@keyframes badgeShine {
  0% {
    left: -100%;
  }
  50%, 100% {
    left: 100%;
  }
}

/* 按钮徽章弹跳动画 */
.btn-badge {
  animation: badgeBounce 2s ease-in-out infinite;
}

@keyframes badgeBounce {
  0%, 80%, 100% {
    transform: scale(1);
  }
  40% {
    transform: scale(1.1);
  }
}

/* ================== 信息区块动画 ================== */

/* 信息区块渐入动画 */
.info-section {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease-out;
}

.info-section.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.info-section:nth-child(1) { transition-delay: 0.1s; }
.info-section:nth-child(2) { transition-delay: 0.2s; }
.info-section:nth-child(3) { transition-delay: 0.3s; }

/* 区块标题动画 */
.section-title {
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #10b981);
  transition: width 0.8s ease-out;
}

.info-section.animate-in .section-title::after {
  width: 60px;
}

/* ================== 控制按钮动画 ================== */

/* 地图控制按钮激活动画 */
.control-btn {
  position: relative;
  overflow: hidden;
}

.control-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(59, 130, 246, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.5s ease-out;
}

.control-btn.active::before {
  width: 100%;
  height: 100%;
}

/* 按钮点击涟漪效果 */
.control-btn.ripple::before {
  animation: buttonRipple 0.6s ease-out;
}

@keyframes buttonRipple {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 200%;
    height: 200%;
    opacity: 0;
  }
}

/* ================== 性能优化动画 ================== */

/* 减少动画在低性能设备上的影响 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* GPU加速优化 */
.marker,
.employee-card,
.vacancy-card,
.action-btn,
.control-btn,
.modal-content {
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* ================== loading动画增强 ================== */

/* 加载spinner增强 */
.loading-spinner {
  position: relative;
}

.loading-spinner::after {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 2px solid transparent;
  border-top: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  animation: spinnerGlow 2s linear infinite;
}

@keyframes spinnerGlow {
  0% {
    transform: rotate(0deg);
    border-top-color: rgba(59, 130, 246, 0.3);
  }
  50% {
    border-top-color: rgba(59, 130, 246, 0.8);
  }
  100% {
    transform: rotate(360deg);
    border-top-color: rgba(59, 130, 246, 0.3);
  }
}

/* ================== 响应式动画 ================== */

/* 移动端动画优化 */
@media (max-width: 768px) {
  /* 减少复杂动画 */
  .marker-headquarters {
    animation: none;
  }
  
  .marker-employee {
    animation: none;
  }
  
  /* 简化过渡效果 */
  .employee-card,
  .vacancy-card,
  .action-btn {
    transition-duration: 0.3s;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .country:hover {
    transform: none;
    filter: none;
  }
  
  .employee-card:hover {
    transform: none;
  }
}

/* ================== 主题动画 ================== */

/* 整体Banner进入动画 */
.global-team-banner {
  opacity: 0;
  transform: translateY(30px);
  animation: bannerFadeIn 1s ease-out forwards;
}

@keyframes bannerFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Banner头部动画 */
.banner-header {
  opacity: 0;
  animation: headerSlideIn 0.8s ease-out 0.2s forwards;
}

@keyframes headerSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 快速统计动画 */
.quick-stats {
  opacity: 0;
  transform: scale(0.9);
  animation: statsPopIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) 0.4s forwards;
}

@keyframes statsPopIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
} 
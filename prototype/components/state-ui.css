/**
 * 状态管理器UI样式
 * Task 5: 数据集成与状态管理 - UI组件
 */

/* ================== 错误消息样式 ================== */
.error-message {
  display: none;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border: 1px solid #fca5a5;
  border-radius: 8px;
  color: #dc2626;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(220, 38, 38, 0.1);
  animation: slideInError 0.3s ease-out;
}

.error-message .icon-warning {
  margin-right: 8px;
  font-size: 16px;
  color: #dc2626;
}

.error-message button {
  margin-left: auto;
  background: none;
  border: none;
  color: #dc2626;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.error-message button:hover {
  background-color: rgba(220, 38, 38, 0.1);
}

@keyframes slideInError {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ================== 加载指示器样式 ================== */
.loading-indicator {
  display: none;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12px;
}

.loading-indicator span {
  color: #374151;
  font-size: 14px;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ================== 连接状态样式 ================== */
.connection-status {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  color: white;
  z-index: 1001;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.connection-status.connected {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  animation: connectedPulse 2s ease-in-out infinite;
}

.connection-status.offline {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  animation: offlineShake 0.5s ease-in-out;
}

@keyframes connectedPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

@keyframes offlineShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* ================== 数据错误页面样式 ================== */
.data-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  min-height: 400px;
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border-radius: 12px;
  border: 2px dashed #d1d5db;
}

.data-error .icon-alert {
  font-size: 48px;
  color: #f59e0b;
  margin-bottom: 16px;
}

.data-error h3 {
  color: #374151;
  margin-bottom: 8px;
  font-size: 20px;
  font-weight: 600;
}

.data-error p {
  color: #6b7280;
  margin-bottom: 24px;
  font-size: 14px;
  line-height: 1.6;
  max-width: 300px;
}

.retry-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.retry-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.retry-btn:active {
  transform: translateY(0);
}

/* ================== 性能监控样式 ================== */
.performance-monitor {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  min-width: 200px;
  z-index: 1000;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.performance-monitor.hidden {
  display: none;
}

.performance-monitor h4 {
  margin: 0 0 8px 0;
  color: #3b82f6;
  font-size: 14px;
}

.performance-monitor .metric {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  padding: 2px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.performance-monitor .metric:last-child {
  border-bottom: none;
}

.performance-monitor .metric-label {
  color: #9ca3af;
}

.performance-monitor .metric-value {
  color: #10b981;
  font-weight: bold;
}

.performance-monitor .metric-value.warning {
  color: #f59e0b;
}

.performance-monitor .metric-value.error {
  color: #ef4444;
}

/* ================== 状态徽章样式 ================== */
.state-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.state-badge.loading {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
}

.state-badge.error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.state-badge.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.state-badge.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.state-badge.offline {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
}

/* ================== 缓存状态指示器 ================== */
.cache-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
}

.cache-indicator.expired {
  background: #f59e0b;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3);
}

.cache-indicator.invalid {
  background: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3);
}

/* ================== 数据同步动画 ================== */
.sync-animation {
  animation: syncPulse 1.5s ease-in-out infinite;
}

@keyframes syncPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.02);
  }
}

/* ================== 响应式设计 ================== */
@media (max-width: 768px) {
  .connection-status {
    top: 10px;
    right: 10px;
    font-size: 11px;
    padding: 6px 10px;
  }

  .performance-monitor {
    bottom: 10px;
    left: 10px;
    font-size: 11px;
    min-width: 150px;
  }

  .loading-indicator {
    padding: 16px;
  }

  .data-error {
    padding: 40px 16px;
    min-height: 300px;
  }

  .data-error .icon-alert {
    font-size: 36px;
  }

  .data-error h3 {
    font-size: 18px;
  }
}

/* ================== 主题变量 ================== */
:root {
  --state-error-color: #dc2626;
  --state-warning-color: #f59e0b;
  --state-success-color: #10b981;
  --state-info-color: #3b82f6;
  --state-loading-color: #6b7280;
  
  --state-error-bg: #fee2e2;
  --state-warning-bg: #fef3c7;
  --state-success-bg: #d1fae5;
  --state-info-bg: #dbeafe;
  --state-loading-bg: #f3f4f6;
}

/* ================== 暗色主题 ================== */
@media (prefers-color-scheme: dark) {
  .error-message {
    background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
    border-color: #dc2626;
    color: #fecaca;
  }

  .loading-indicator {
    background: rgba(31, 41, 55, 0.95);
    color: #f9fafb;
  }

  .data-error {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border-color: #374151;
    color: #f9fafb;
  }

  .data-error h3 {
    color: #f9fafb;
  }

  .data-error p {
    color: #d1d5db;
  }
}

/* ================== 可访问性支持 ================== */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    animation: none;
  }

  .connection-status.connected {
    animation: none;
  }

  .sync-animation {
    animation: none;
  }

  .error-message {
    animation: none;
  }
}

/* ================== 打印样式 ================== */
@media print {
  .connection-status,
  .performance-monitor,
  .loading-indicator,
  .error-message {
    display: none !important;
  }
} 
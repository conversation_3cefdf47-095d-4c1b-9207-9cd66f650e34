<svg viewBox="0 0 1200 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="oceanGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E6F3FF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B3D9FF;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="landGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F8FAFC;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E2E8F0;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影效果 -->
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#00000020"/>
    </filter>
  </defs>
  
  <!-- 海洋背景 -->
  <rect width="100%" height="100%" fill="url(#oceanGradient)" class="ocean"/>
  
  <!-- 更真实的世界地图轮廓 -->
  
  <!-- 北美洲 -->
  <g id="north-america" class="continent">
    <!-- 格陵兰 -->
    <path id="greenland" class="country" 
          d="M320,50 L380,45 L420,60 L440,90 L435,130 L415,150 L380,155 L340,145 L315,120 L305,85 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 加拿大 -->
    <path id="canada" class="country" 
          d="M80,120 L140,110 L200,105 L260,100 L320,95 L380,100 L420,110 L450,125 L470,150 L460,175 L440,190 L400,200 L360,205 L320,200 L280,195 L240,185 L200,175 L160,165 L120,155 L90,145 L70,130 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 美国本土 -->
    <path id="usa" class="country country-usa" 
          d="M120,220 L180,215 L240,210 L300,208 L360,210 L420,215 L470,220 L500,235 L520,250 L530,270 L525,290 L510,310 L485,325 L450,330 L410,325 L370,320 L330,315 L290,310 L250,305 L210,295 L170,285 L140,270 L115,250 L105,235 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 墨西哥 -->
    <path id="mexico" class="country" 
          d="M120,320 L180,315 L240,312 L300,315 L350,320 L380,335 L390,355 L380,375 L350,385 L310,380 L270,375 L230,370 L190,365 L150,355 L125,340 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
  </g>
  
  <!-- 南美洲 -->
  <g id="south-america" class="continent">
    <path id="south-america-continent" class="country" 
          d="M300,380 L340,375 L380,380 L410,395 L430,420 L445,450 L450,480 L445,510 L435,540 L420,565 L400,580 L375,585 L350,580 L325,570 L305,555 L290,535 L280,510 L275,485 L280,460 L290,435 L305,410 L315,390 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
  </g>
  
  <!-- 欧洲 -->
  <g id="europe" class="continent">
    <!-- 挪威 -->
    <path id="norway" class="country" 
          d="M580,90 L590,85 L600,95 L595,110 L590,125 L585,140 L580,155 L575,140 L570,125 L575,110 L580,95 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 瑞典 -->
    <path id="sweden" class="country" 
          d="M590,140 L600,135 L610,145 L615,160 L610,175 L600,185 L590,180 L585,165 L590,150 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 英国 -->
    <path id="uk" class="country country-uk" 
          d="M520,180 L535,175 L545,180 L550,190 L548,200 L543,210 L535,215 L525,212 L518,205 L515,195 L518,185 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 爱尔兰 -->
    <path id="ireland" class="country" 
          d="M500,185 L510,183 L515,188 L512,195 L508,200 L502,198 L498,193 L500,188 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 法国 -->
    <path id="france" class="country country-france" 
          d="M540,220 L565,215 L580,225 L585,240 L580,255 L570,265 L555,270 L540,265 L530,250 L535,235 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 西班牙 -->
    <path id="spain" class="country" 
          d="M520,260 L560,255 L580,265 L590,280 L585,295 L570,305 L540,310 L520,305 L510,290 L515,275 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 德国 -->
    <path id="germany" class="country country-germany" 
          d="M580,200 L605,195 L620,205 L625,220 L620,235 L610,245 L595,250 L580,245 L570,230 L575,215 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 意大利 -->
    <path id="italy" class="country" 
          d="M590,260 L605,255 L615,270 L620,285 L615,300 L605,315 L595,325 L585,315 L580,300 L585,285 L590,270 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 波兰 -->
    <path id="poland" class="country" 
          d="M630,200 L655,195 L670,205 L675,220 L670,235 L655,240 L640,235 L630,220 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 其他欧洲地区 -->
    <path id="europe-other" class="country" 
          d="M560,150 L680,145 L720,160 L740,180 L750,200 L745,220 L730,240 L710,250 L680,255 L650,250 L620,245 L590,240 L570,225 L555,205 L550,185 L555,165 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
  </g>
  
  <!-- 非洲 -->
  <g id="africa" class="continent">
    <path id="africa-continent" class="country" 
          d="M550,320 L590,315 L630,320 L670,330 L700,350 L715,380 L720,410 L715,440 L705,470 L690,500 L670,525 L645,545 L615,555 L585,550 L555,540 L530,525 L515,500 L510,470 L515,440 L525,410 L540,380 L550,350 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
  </g>
  
  <!-- 亚洲 -->
  <g id="asia" class="continent">
    <!-- 俄罗斯 -->
    <path id="russia" class="country" 
          d="M680,80 L750,75 L820,70 L890,75 L960,80 L1030,85 L1100,90 L1150,100 L1180,120 L1175,140 L1160,155 L1140,165 L1110,170 L1080,165 L1050,160 L1020,155 L990,150 L960,145 L930,140 L900,135 L870,130 L840,125 L810,120 L780,115 L750,110 L720,105 L690,100 L680,90 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 中国 -->
    <path id="china" class="country country-china" 
          d="M800,180 L850,175 L900,180 L940,190 L970,205 L990,225 L1000,250 L995,275 L985,295 L970,310 L950,320 L925,325 L900,320 L875,315 L850,310 L825,300 L805,285 L790,265 L785,240 L790,215 L800,195 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 日本 -->
    <path id="japan" class="country country-japan" 
          d="M1020,240 L1035,235 L1045,245 L1050,260 L1048,275 L1043,285 L1035,290 L1025,285 L1018,270 L1015,255 L1020,245 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 朝鲜半岛 -->
    <path id="korea" class="country" 
          d="M1000,220 L1010,215 L1015,225 L1012,235 L1005,240 L998,235 L995,225 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 印度 -->
    <path id="india" class="country" 
          d="M760,280 L790,275 L815,285 L835,300 L845,320 L840,340 L830,360 L815,375 L795,385 L775,380 L755,370 L740,355 L735,335 L740,315 L750,295 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 东南亚 -->
    <path id="southeast-asia" class="country" 
          d="M850,330 L890,325 L925,335 L950,350 L965,370 L960,385 L945,395 L925,400 L905,395 L885,385 L870,370 L860,350 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 中东 -->
    <path id="middle-east" class="country" 
          d="M700,260 L730,255 L755,265 L770,280 L765,295 L750,305 L730,310 L710,305 L695,290 L690,275 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
  </g>
  
  <!-- 大洋洲 -->
  <g id="oceania" class="continent">
    <!-- 澳大利亚 -->
    <path id="australia" class="country" 
          d="M920,450 L980,445 L1040,450 L1090,460 L1130,475 L1155,495 L1160,515 L1150,535 L1130,545 L1100,550 L1060,545 L1020,535 L980,525 L945,510 L920,490 L910,470 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
    
    <!-- 新西兰 -->
    <path id="new-zealand" class="country" 
          d="M1180,520 L1190,515 L1195,525 L1192,535 L1185,540 L1178,535 L1175,525 Z"
          fill="url(#landGradient)" stroke="#CBD5E1" stroke-width="1"/>
  </g>
  
  <!-- 员工位置标记点 - 将通过JavaScript动态添加 -->
  <g id="employee-markers" class="markers">
    <!-- 中国 - Alex (总部) -->
    <circle id="marker-china" class="marker marker-headquarters" 
            cx="900" cy="250" r="8" 
            fill="#3B82F6" stroke="#FFFFFF" stroke-width="2" 
            style="display: none;"/>
    
    <!-- 德国 - Emma -->
    <circle id="marker-germany" class="marker marker-employee" 
            cx="600" cy="220" r="6" 
            fill="#10B981" stroke="#FFFFFF" stroke-width="2" 
            style="display: none;"/>
    
    <!-- 美国 - Michael -->
    <circle id="marker-usa" class="marker marker-employee" 
            cx="300" cy="270" r="6" 
            fill="#F59E0B" stroke="#FFFFFF" stroke-width="2" 
            style="display: none;"/>
    
    <!-- 英国 - 待雇佣 -->
    <circle id="marker-uk" class="marker marker-vacant" 
            cx="535" cy="195" r="5" 
            fill="none" stroke="#9CA3AF" stroke-width="2" stroke-dasharray="3,3" 
            style="display: none;"/>
    
    <!-- 法国 - 待雇佣 -->
    <circle id="marker-france" class="marker marker-vacant" 
            cx="560" cy="240" r="5" 
            fill="none" stroke="#9CA3AF" stroke-width="2" stroke-dasharray="3,3" 
            style="display: none;"/>
    
    <!-- 日本 - 规划中 -->
    <circle id="marker-japan" class="marker marker-planned" 
            cx="1035" cy="260" r="5" 
            fill="none" stroke="#D1D5DB" stroke-width="2" stroke-dasharray="5,5" 
            style="display: none;"/>
  </g>
  
  <!-- 样式定义 -->
  <style>
    .country {
      transition: all 0.3s ease;
      cursor: pointer;
    }
    
    .country:hover {
      filter: brightness(1.1);
      stroke-width: 2;
      stroke: #3B82F6;
    }
    
    .country-china:hover {
      fill: #DBEAFE;
      stroke: #3B82F6;
    }
    
    .country-usa:hover {
      fill: #FEF3C7;
      stroke: #F59E0B;
    }
    
    .country-germany:hover {
      fill: #D1FAE5;
      stroke: #10B981;
    }
    
    .country-uk:hover {
      fill: #F3E8FF;
      stroke: #8B5CF6;
    }
    
    .country-france:hover {
      fill: #FEE2E2;
      stroke: #EF4444;
    }
    
    .country-japan:hover {
      fill: #F0F9FF;
      stroke: #0EA5E9;
    }
    
    .marker {
      transition: all 0.3s ease;
      cursor: pointer;
    }
    
    .marker:hover {
      transform: scale(1.3);
    }
    
    .marker-headquarters {
      animation: pulse 2s infinite;
    }
    
    .marker-vacant {
      animation: dash 2s linear infinite;
    }
    
    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }
    
    @keyframes dash {
      to { stroke-dashoffset: -6; }
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
      .marker {
        r: 4;
      }
      .marker-headquarters {
        r: 6;
      }
    }
  </style>
</svg> 
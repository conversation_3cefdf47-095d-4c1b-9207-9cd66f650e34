/**
 * 全球团队数据模型
 * Foxu AI Team - 海外市场团队状态管理
 */

// 员工状态枚举
const EmployeeStatus = {
  ACTIVE: 'active',        // 在职员工
  VACANT: 'vacant',        // 待雇佣职位
  PLANNED: 'planned',      // 规划中职位
  RECRUITING: 'recruiting' // 招聘中
};

// 市场优先级枚举  
const MarketPriority = {
  HIGH: 'high',      // 高优先级市场
  MEDIUM: 'medium',  // 中等优先级市场
  LOW: 'low',        // 低优先级市场
  FUTURE: 'future'   // 未来规划市场
};

// 专业领域枚举
const Specialization = {
  MARKET_ANALYSIS: 'market_analysis',     // 市场分析
  COMPETITOR_INTEL: 'competitor_intel',   // 竞品情报
  CONSUMER_RESEARCH: 'consumer_research', // 消费者研究
  TREND_MONITORING: 'trend_monitoring',   // 趋势监控
  DIGITAL_MARKETING: 'digital_marketing', // 数字营销
  SALES_STRATEGY: 'sales_strategy'        // 销售策略
};

// 全球团队数据
const GlobalTeamData = {
  // 团队概览统计
  overview: {
    totalPositions: 6,
    activeEmployees: 3,
    vacantPositions: 2,
    plannedPositions: 1,
    coveragePercentage: 65, // 市场覆盖率
    lastUpdated: new Date().toISOString()
  },

  // 员工详细信息
  employees: [
    {
      id: 'alex_001',
      name: 'Alex Chen',
      title: '市场总监',
      titleEn: 'Market Director',
      country: 'china',
      countryName: '中国',
      region: 'Asia Pacific',
      position: { x: 900, y: 250 },
      status: EmployeeStatus.ACTIVE,
      avatar: '/assets/avatars/alex.jpg',
      specializations: [
        Specialization.MARKET_ANALYSIS,
        Specialization.TREND_MONITORING
      ],
      experience: '8年国际市场经验',
      languages: ['中文', 'English', '日本語'],
      workingHours: 'UTC+8 (09:00-18:00)',
      contactInfo: {
        email: '<EMAIL>',
        timezone: 'Asia/Shanghai'
      },
      performance: {
        reportsCompleted: 156,
        accuracy: 94,
        responseTime: 2.3, // 小时
        clientSatisfaction: 4.8
      },
      recentActivity: [
        { date: '2024-01-15', action: '完成亚太区Q4市场分析报告' },
        { date: '2024-01-12', action: '监控日韩电商平台动态' },
        { date: '2024-01-10', action: '分析中国消费者购买趋势' }
      ]
    },
    {
      id: 'emma_002',
      name: 'Emma Weber',
      title: '欧洲市场专员',
      titleEn: 'European Market Specialist',
      country: 'germany',
      countryName: '德国',
      region: 'Europe',
      position: { x: 600, y: 220 },
      status: EmployeeStatus.ACTIVE,
      avatar: '/assets/avatars/emma.jpg',
      specializations: [
        Specialization.COMPETITOR_INTEL,
        Specialization.CONSUMER_RESEARCH
      ],
      experience: '6年欧洲市场研究经验',
      languages: ['Deutsch', 'English', 'Français'],
      workingHours: 'UTC+1 (09:00-18:00)',
      contactInfo: {
        email: '<EMAIL>',
        timezone: 'Europe/Berlin'
      },
      performance: {
        reportsCompleted: 89,
        accuracy: 92,
        responseTime: 1.8,
        clientSatisfaction: 4.6
      },
      recentActivity: [
        { date: '2024-01-14', action: '完成德国电商竞品分析' },
        { date: '2024-01-11', action: '调研欧盟消费者隐私政策影响' },
        { date: '2024-01-09', action: '监控法国奢侈品市场趋势' }
      ]
    },
    {
      id: 'michael_003',
      name: 'Michael Johnson',
      title: '北美市场专员',
      titleEn: 'North American Market Specialist',
      country: 'usa',
      countryName: '美国',
      region: 'North America',
      position: { x: 300, y: 270 },
      status: EmployeeStatus.ACTIVE,
      avatar: '/assets/avatars/michael.jpg',
      specializations: [
        Specialization.DIGITAL_MARKETING,
        Specialization.SALES_STRATEGY
      ],
      experience: '7年北美数字营销经验',
      languages: ['English', 'Español'],
      workingHours: 'UTC-5 (09:00-18:00)',
      contactInfo: {
        email: '<EMAIL>',
        timezone: 'America/New_York'
      },
      performance: {
        reportsCompleted: 124,
        accuracy: 96,
        responseTime: 1.5,
        clientSatisfaction: 4.9
      },
      recentActivity: [
        { date: '2024-01-16', action: '分析美国社交电商增长趋势' },
        { date: '2024-01-13', action: '完成加拿大市场准入分析' },
        { date: '2024-01-11', action: '监控亚马逊平台政策变化' }
      ]
    }
  ],

  // 职位空缺信息
  vacancies: [
    {
      id: 'uk_vacancy_001',
      country: 'uk',
      countryName: '英国',
      region: 'Europe',
      position: { x: 535, y: 195 },
      status: EmployeeStatus.VACANT,
      title: '英国市场专员',
      titleEn: 'UK Market Specialist',
      priority: MarketPriority.HIGH,
      requiredSpecializations: [
        Specialization.MARKET_ANALYSIS,
        Specialization.COMPETITOR_INTEL
      ],
      marketPotential: {
        size: '6800万消费者',
        value: '£2.1万亿GDP',
        growth: '****%年增长率',
        opportunities: [
          '脱欧后贸易政策调整',
          '数字化转型加速',
          '可持续消费趋势'
        ]
      },
      requirements: [
        '3年以上英国市场经验',
        '流利的英语沟通能力',
        '了解英国消费者行为',
        '熟悉英国法律法规'
      ],
      urgency: 'high',
      expectedStartDate: '2024-03-01',
      recruitmentStatus: 'open_applications',
      applicants: 12
    },
    {
      id: 'france_vacancy_002',
      country: 'france',
      countryName: '法国',
      region: 'Europe',
      position: { x: 560, y: 240 },
      status: EmployeeStatus.VACANT,
      title: '法国市场专员',
      titleEn: 'French Market Specialist',
      priority: MarketPriority.HIGH,
      requiredSpecializations: [
        Specialization.CONSUMER_RESEARCH,
        Specialization.TREND_MONITORING
      ],
      marketPotential: {
        size: '6700万消费者',
        value: '€2.6万亿GDP',
        growth: '+1.8%年增长率',
        opportunities: [
          '奢侈品市场领导地位',
          '绿色能源转型',
          '数字支付普及'
        ]
      },
      requirements: [
        '熟练掌握法语',
        '了解法国文化消费习惯',
        '奢侈品行业经验优先',
        '数据分析能力'
      ],
      urgency: 'medium',
      expectedStartDate: '2024-04-15',
      recruitmentStatus: 'sourcing_candidates',
      applicants: 8
    }
  ],

  // 规划中的职位
  plannedPositions: [
    {
      id: 'japan_planned_001',
      country: 'japan',
      countryName: '日本',
      region: 'Asia Pacific',
      position: { x: 1035, y: 260 },
      status: EmployeeStatus.PLANNED,
      title: '日本市场专员',
      titleEn: 'Japanese Market Specialist',
      priority: MarketPriority.MEDIUM,
      plannedLaunchDate: '2024-06-01',
      requiredSpecializations: [
        Specialization.CONSUMER_RESEARCH,
        Specialization.DIGITAL_MARKETING
      ],
      marketPotential: {
        size: '1.25亿消费者',
        value: '¥540万亿GDP',
        growth: '+0.9%年增长率',
        opportunities: [
          '老龄化社会服务需求',
          '数字化支付普及',
          '可持续产品偏好'
        ]
      },
      preparationTasks: [
        '市场准入政策研究',
        '竞品格局分析',
        '本地化策略制定',
        '合作伙伴洽谈'
      ],
      budget: {
        setup: 150000,
        annual: 120000,
        currency: 'USD'
      }
    }
  ],

  // 市场覆盖情况
  marketCoverage: {
    covered: ['china', 'germany', 'usa'],
    recruiting: ['uk', 'france'],
    planned: ['japan'],
    future: ['australia', 'brazil', 'india'],
    totalMarkets: 9,
    activeMarkets: 3,
    coveragePercentage: 33.3
  },

  // 团队工作时区覆盖
  timezonesCoverage: {
    'UTC+8': ['china'],
    'UTC+1': ['germany'],
    'UTC-5': ['usa'],
    'UTC+0': ['uk'], // 待雇佣
    'UTC+1': ['france'], // 待雇佣  
    'UTC+9': ['japan'], // 规划中
    workingHoursOverlap: {
      'Asia-Europe': '13:00-17:00 UTC',
      'Europe-Americas': '13:00-17:00 UTC',
      'Global-Meeting': '13:00-14:00 UTC'
    }
  },

  // 团队协作统计
  collaboration: {
    crossRegionProjects: 23,
    sharedReports: 156,
    knowledgeExchange: 89,
    mentoringSessions: 45,
    teamMeetings: {
      weekly: 'Mondays 13:00 UTC',
      monthly: 'First Friday 13:00 UTC',
      quarterly: 'Q1 Review - March 15'
    }
  }
};

// 数据操作方法
const TeamDataManager = {
  
  // 获取所有员工数据
  getAllEmployees() {
    return GlobalTeamData.employees;
  },

  // 根据国家获取员工信息
  getEmployeeByCountry(country) {
    return GlobalTeamData.employees.find(emp => emp.country === country);
  },

  // 获取所有职位空缺
  getVacancies() {
    return GlobalTeamData.vacancies;
  },

  // 获取规划中的职位
  getPlannedPositions() {
    return GlobalTeamData.plannedPositions;
  },

  // 获取团队概览统计
  getOverview() {
    return GlobalTeamData.overview;
  },

  // 根据地区获取团队成员
  getTeamByRegion(region) {
    return GlobalTeamData.employees.filter(emp => emp.region === region);
  },

  // 获取市场覆盖情况
  getMarketCoverage() {
    return GlobalTeamData.marketCoverage;
  },

  // 获取指定国家的详细信息
  getCountryDetails(country) {
    // 先检查在职员工
    let employee = this.getEmployeeByCountry(country);
    if (employee) return { type: 'employee', data: employee };

    // 检查职位空缺
    let vacancy = GlobalTeamData.vacancies.find(v => v.country === country);
    if (vacancy) return { type: 'vacancy', data: vacancy };

    // 检查规划中职位
    let planned = GlobalTeamData.plannedPositions.find(p => p.country === country);
    if (planned) return { type: 'planned', data: planned };

    return null;
  },

  // 更新员工信息
  updateEmployee(employeeId, updates) {
    const index = GlobalTeamData.employees.findIndex(emp => emp.id === employeeId);
    if (index !== -1) {
      GlobalTeamData.employees[index] = { ...GlobalTeamData.employees[index], ...updates };
      this.updateOverview();
      return true;
    }
    return false;
  },

  // 添加新员工
  addEmployee(employeeData) {
    GlobalTeamData.employees.push({
      id: `emp_${Date.now()}`,
      ...employeeData,
      status: EmployeeStatus.ACTIVE
    });
    this.updateOverview();
  },

  // 更新概览统计
  updateOverview() {
    const overview = GlobalTeamData.overview;
    overview.activeEmployees = GlobalTeamData.employees.length;
    overview.vacantPositions = GlobalTeamData.vacancies.length;
    overview.plannedPositions = GlobalTeamData.plannedPositions.length;
    overview.totalPositions = overview.activeEmployees + overview.vacantPositions + overview.plannedPositions;
    overview.coveragePercentage = Math.round((overview.activeEmployees / overview.totalPositions) * 100);
    overview.lastUpdated = new Date().toISOString();
  },

  // 获取实时团队状态
  getRealtimeStatus() {
    return {
      timestamp: new Date().toISOString(),
      activeNow: this.getActiveEmployeesNow(),
      upcomingMeetings: this.getUpcomingMeetings(),
      pendingTasks: this.getPendingTasks(),
      recruitmentProgress: this.getRecruitmentProgress()
    };
  },

  // 获取当前在线员工（根据工作时间）
  getActiveEmployeesNow() {
    const now = new Date();
    const utcHour = now.getUTCHours();
    
    return GlobalTeamData.employees.filter(emp => {
      const timezone = emp.contactInfo.timezone;
      // 简化的工作时间判断（9-18点）
      let localHour;
      if (timezone.includes('Shanghai')) localHour = (utcHour + 8) % 24;
      else if (timezone.includes('Berlin')) localHour = (utcHour + 1) % 24;
      else if (timezone.includes('New_York')) localHour = (utcHour - 5 + 24) % 24;
      else return false;
      
      return localHour >= 9 && localHour <= 18;
    });
  },

  // 获取即将到来的会议
  getUpcomingMeetings() {
    // 这里可以集成日历API，暂时返回示例数据
    return [
      {
        title: '周度团队同步会议',
        time: 'Monday 13:00 UTC',
        participants: ['Alex', 'Emma', 'Michael'],
        type: 'recurring'
      },
      {
        title: 'Q1市场回顾会议',
        time: 'March 15 13:00 UTC',
        participants: 'All Team',
        type: 'quarterly'
      }
    ];
  },

  // 获取待处理任务
  getPendingTasks() {
    return [
      { task: '英国市场专员面试安排', deadline: '2024-02-01', assignee: 'HR' },
      { task: '法国市场准入研究', deadline: '2024-02-15', assignee: 'Emma' },
      { task: '日本办公室筹备', deadline: '2024-05-01', assignee: 'Operations' }
    ];
  },

  // 获取招聘进度
  getRecruitmentProgress() {
    return GlobalTeamData.vacancies.map(vacancy => ({
      position: vacancy.title,
      country: vacancy.countryName,
      status: vacancy.recruitmentStatus,
      applicants: vacancy.applicants,
      urgency: vacancy.urgency
    }));
  }
};

// 导出数据和管理器
window.GlobalTeamData = GlobalTeamData;
window.TeamDataManager = TeamDataManager;
window.EmployeeStatus = EmployeeStatus;
window.MarketPriority = MarketPriority;
window.Specialization = Specialization;

// 如果在Node.js环境中
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    GlobalTeamData,
    TeamDataManager,
    EmployeeStatus,
    MarketPriority,
    Specialization
  };
} 
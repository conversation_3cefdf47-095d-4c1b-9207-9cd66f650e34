<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="dingtalk-bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1677FF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0050B3;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="12" cy="12" r="12" fill="url(#dingtalk-bg)"/>
  
  <!-- DingTalk Icon Shape -->
  <!-- Main body -->
  <path d="M7 8 Q7 6 9 6 L15 6 Q17 6 17 8 L17 14 Q17 16 15 16 L13 16 L12 18 L11 16 L9 16 Q7 16 7 14 Z" fill="#FFFFFF"/>
  
  <!-- Message indicator -->
  <circle cx="15" cy="8" r="1.5" fill="#FF4D4F"/>
  <circle cx="15" cy="8" r="0.8" fill="#FFFFFF"/>
  
  <!-- Screen lines -->
  <line x1="9" y1="9" x2="15" y2="9" stroke="#1677FF" stroke-width="0.5"/>
  <line x1="9" y1="11" x2="13" y2="11" stroke="#1677FF" stroke-width="0.5"/>
  <line x1="9" y1="13" x2="14" y2="13" stroke="#1677FF" stroke-width="0.5"/>
  
  <!-- DingTalk character -->
  <text x="12" y="22" text-anchor="middle" fill="#FFFFFF" font-family="Arial" font-size="4" font-weight="bold">钉</text>
</svg> 
<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="wechat-bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1DA1F2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0D8BD9;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="12" cy="12" r="12" fill="url(#wechat-bg)"/>
  
  <!-- WeChat Bubbles -->
  <!-- Left bubble -->
  <ellipse cx="8.5" cy="9" rx="4" ry="3.5" fill="#FFFFFF"/>
  
  <!-- Right bubble -->
  <ellipse cx="15" cy="13" rx="3.5" ry="3" fill="#FFFFFF"/>
  
  <!-- Left bubble eyes -->
  <circle cx="7" cy="8.5" r="0.5" fill="#1DA1F2"/>
  <circle cx="10" cy="8.5" r="0.5" fill="#1DA1F2"/>
  
  <!-- Right bubble eyes -->
  <circle cx="13.5" cy="12.5" r="0.4" fill="#1DA1F2"/>
  <circle cx="16.5" cy="12.5" r="0.4" fill="#1DA1F2"/>
  
  <!-- Chat connection line -->
  <path d="M12 10 Q13 11 14 11.5" stroke="#FFFFFF" stroke-width="0.8" fill="none"/>
  
  <!-- WeChat character -->
  <text x="12" y="20" text-anchor="middle" fill="#FFFFFF" font-family="Arial" font-size="4" font-weight="bold">微</text>
</svg> 
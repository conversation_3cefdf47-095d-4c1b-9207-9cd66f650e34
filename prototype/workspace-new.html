<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emma - 德国市场专员工作区 - Foxu AI Team</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="custom.css">
    
    <style>
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .progress-ring {
            transform: rotate(-90deg);
        }
        
        .progress-ring-circle {
            transition: stroke-dasharray 0.3s ease;
        }
        
        .feed-item {
            transition: all 0.3s ease;
        }
        
        .feed-item:hover {
            transform: translateX(4px);
        }
        
        .feed-item.unread {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-left: 4px solid #0ea5e9;
        }
        
        .feed-item.read {
            background: #f8fafc;
            opacity: 0.8;
        }
        
        .artifact-progress {
            background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
            border: 1px solid #f59e0b;
        }
        
        .artifact-progress.completed {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border: 1px solid #22c55e;
        }
        
        .artifact-progress.in-progress {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            border: 1px solid #3b82f6;
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
        .animate-spin-slow {
            animation: spin 3s linear infinite;
        }

        @keyframes pulse-radar {
            0% {
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
            }
            70% {
                box-shadow: 0 0 0 12px rgba(255, 255, 255, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
            }
        }
        .animate-pulse-radar {
            animation: pulse-radar 2.5s infinite;
            border-radius: 9999px; /* Make shadow circular */
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white/80 backdrop-blur-xl border-b border-gray-100 px-4 rounded-b-3xl shadow-lg">
        <div class="max-w-7xl mx-auto flex justify-between items-center h-16">
            <div class="logo">
                <a href="index.html" class="text-2xl font-black bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent hover:from-purple-600 hover:to-blue-600 transition-all duration-300 cursor-pointer">Foxu AI Team</a>
            </div>
            <nav class="flex items-center gap-8">
                <div class="flex items-center gap-3 relative">
                    <div class="flex items-center gap-2">
                        <div class="w-2 h-2 bg-emerald-500 rounded-full relative animate-pulse"></div>
                        <span class="text-gray-700 font-semibold text-sm">David Chen</span>
                    </div>
                    <div class="w-6 h-6 bg-gradient-to-r from-emerald-500 to-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold cursor-pointer hover:scale-110 transition-transform shadow-md border-2 border-white" id="notificationBadge">3</div>
                    <img src="assets/avatars/david-chen.svg" alt="用户头像" class="w-8 h-8 rounded-full border-2 border-blue-200">
                </div>
            </nav>
        </div>
    </header>

    <main class="max-w-7xl mx-auto px-4 py-8">
        <!-- 上部区域：员工Profile和工作量统计 -->
        <section class="mb-8">
            <!-- 试用状态栏 -->
            <div class="bg-gradient-to-r from-orange-50 to-red-50 p-4 rounded-2xl border border-orange-200 mb-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 bg-orange-100 rounded-xl flex items-center justify-center">
                            <span class="text-2xl">⏰</span>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800">试用期剩余 5 天</h3>
                            <p class="text-sm text-gray-600">试用期间可完整使用所有功能</p>
                        </div>
                    </div>
                    <button class="px-6 py-2 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white rounded-lg transition-all font-medium shadow-lg hover:shadow-xl" id="renewBtn">
                        立即续费
                    </button>
                </div>
            </div>

            <!-- 员工Profile -->
            <div class="bg-white rounded-3xl p-8 shadow-xl border border-gray-100 mb-6">
                <div class="flex items-center gap-8">
                    <div class="relative">
                        <img src="assets/avatars/emma-germany.svg" alt="Emma" class="w-32 h-32 rounded-full border-4 border-green-300 shadow-lg">
                        <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center border-4 border-white">
                            <span class="w-3 h-3 bg-white rounded-full animate-pulse"></span>
                        </div>
                    </div>
                    
                    <div class="flex-1">
                        <div class="flex items-center gap-4 mb-4">
                            <h1 class="text-4xl font-bold text-gray-800">Emma</h1>
                            <span class="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full text-sm font-semibold">德国市场专员</span>
                        </div>
                        
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-800">激活能力</h3>
                                <div class="flex items-center gap-2">
                                    <label class="text-sm text-gray-600">数据月份:</label>
                                    <select id="monthSelector" class="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="2024-01" selected>2024年1月</option>
                                        <option value="2023-12">2023年12月</option>
                                        <option value="2023-11">2023年11月</option>
                                        <option value="2023-10">2023年10月</option>
                                        <option value="2023-09">2023年9月</option>
                                        <option value="2023-08">2023年8月</option>
                                    </select>
                                </div>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-xl">
                                <div class="flex flex-wrap gap-2 text-sm text-gray-700">
                                    <span class="font-medium">A4竞品分析</span> |
                                    <span class="font-medium">A5合作伙伴评估</span> |
                                    <span class="font-medium">A7合规导航</span> |
                                    <span class="font-medium">A9客户画像</span> |
                                    <span class="font-medium">A10定价模式</span> |
                                    <span class="font-medium">A11渠道策略</span> |
                                    <span class="font-medium">A6差异化策略</span> |
                                    <span class="font-medium">A8整合策略</span>
                                </div>
                                <div class="mt-2 text-xs text-gray-500">
                                    共8类能力已激活，当前正在7×24小时持续执行任务
                                </div>
                            </div>
                        </div>
                                            </div>
                </div>
            </div>

            <!-- 数字员工价值统计 -->
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-6" id="statsContainer">
                <!-- 7×24小时工作状态卡片 (第一位) -->
                <div class="bg-gradient-to-br from-purple-500 to-indigo-700 text-white p-6 rounded-2xl hover:shadow-xl hover:-translate-y-1 transition-all duration-300 cursor-pointer">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                            <svg class="w-8 h-8 animate-spin-slow" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 002.572 1.065c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>
                        </div>
                        <div class="text-xs bg-white/20 px-2 py-1 rounded-full">7×24小时</div>
                    </div>
                    <div class="mb-2">
                        <h3 class="text-lg font-bold mb-1">本月任务流</h3>
                        <div class="text-2xl font-extrabold" id="statusStats">127个</div>
                    </div>
                    <div class="text-white/80 text-sm">8项工作职责</div>
                    <div class="text-white/60 text-xs mt-2">持续监控与分析中</div>
                </div>

                <!-- 实时监控中卡片 (第二位) -->
                <div class="bg-gradient-to-br from-cyan-500 to-blue-600 text-white p-6 rounded-2xl hover:shadow-xl hover:-translate-y-1 transition-all duration-300 cursor-pointer">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center animate-pulse-radar">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path></svg>
                        </div>
                        <div class="text-xs bg-white/20 px-2 py-1 rounded-full">广泛的覆盖能力</div>
                    </div>
                    <div class="mb-2">
                        <h3 class="text-lg font-bold mb-3">实时监控中</h3>
                        <div class="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                            <div>
                                <div class="font-bold text-base">20</div>
                                <div class="text-white/80 text-xs">家公司</div>
                            </div>
                            <div>
                                <div class="font-bold text-base">12</div>
                                <div class="text-white/80 text-xs">个官方数据源</div>
                            </div>
                            <div>
                                <div class="font-bold text-base">3</div>
                                <div class="text-white/80 text-xs">个细分市场商机</div>
                            </div>
                            <div>
                                <div class="font-bold text-base">3</div>
                                <div class="text-white/80 text-xs">家行业领先者</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 等效人力价值卡片 (第三位) -->
                <div class="bg-gradient-to-br from-blue-500 to-blue-700 text-white p-6 rounded-2xl hover:shadow-xl hover:-translate-y-1 transition-all duration-300 cursor-pointer">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                            </svg>
                        </div>
                        <div class="text-xs bg-white/20 px-2 py-1 rounded-full">高效</div>
                    </div>
                    <div class="mb-2">
                        <h3 class="text-lg font-bold mb-1">等效人力工作量</h3>
                        <div class="text-2xl font-extrabold" id="workforceStats">45 人天</div>
                    </div>
                    <div class="text-white/80 text-sm">相当于传统分析师工作量</div>
                    <div class="text-white/60 text-xs mt-2">需要3名高级分析师的专业团队</div>
                </div>

                <!-- 成本效益价值卡片 (第四位) -->
                <div class="bg-gradient-to-br from-green-500 to-emerald-700 text-white p-6 rounded-2xl hover:shadow-xl hover:-translate-y-1 transition-all duration-300 cursor-pointer">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                            </svg>
                        </div>
                        <div class="text-xs bg-white/20 px-2 py-1 rounded-full">当月数据</div>
                    </div>
                    <div class="mb-2">
                        <h3 class="text-lg font-bold mb-1">当月累计创造价值</h3>
                        <div class="text-2xl font-extrabold" id="valueCreationStats">78.5万元</div>
                    </div>
                    <div class="text-white/80 text-sm">本月实际消耗：12,000点数</div>
                    <div class="text-white/60 text-xs mt-2">相当于投入成本¥1.2万元</div>
                </div>
            </div>
        </section>

        <!-- 下部区域：Tab导航 -->
        <section>
            <!-- Tab导航 -->
            <div class="flex border-b border-gray-200 mb-8 bg-white rounded-t-2xl px-2">
                <button class="tab-btn px-6 py-4 font-semibold text-blue-600 border-b-2 border-blue-600 bg-blue-50 rounded-t-lg" data-tab="feed">
                    📰 Feed流
                </button>
                <button class="tab-btn px-6 py-4 font-semibold text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors rounded-t-lg" data-tab="kanban">
                    📋 工作看板
                </button>
                <button class="tab-btn px-6 py-4 font-semibold text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors rounded-t-lg" data-tab="reports">
                    📄 交付成果
                </button>
                <button class="tab-btn px-6 py-4 font-semibold text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors rounded-t-lg" data-tab="tasks">
                    📋 任务委托
                </button>
                <button class="tab-btn px-6 py-4 font-semibold text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors rounded-t-lg" data-tab="employee-info">
                    👤 员工信息
                </button>
            </div>

            <!-- Tab 1: Feed流 -->
            <div class="tab-content active bg-white rounded-b-2xl rounded-tr-2xl shadow-lg" id="feed">
                <div class="p-8">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">Emma的工作动态</h2>
                        <div class="flex gap-3">
                            <button class="filter-btn px-4 py-2 bg-blue-600 text-white rounded-lg font-medium" data-filter="all">全部</button>
                            <button class="filter-btn px-4 py-2 bg-gray-100 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg font-medium transition-colors" data-filter="work">工作完成</button>
                            <button class="filter-btn px-4 py-2 bg-gray-100 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg font-medium transition-colors" data-filter="alert">智能预警</button>
                            <button class="filter-btn px-4 py-2 bg-gray-100 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg font-medium transition-colors" data-filter="system">系统通知</button>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <!-- 工作完成通知 -->
                        <div class="feed-item unread p-6 rounded-xl shadow-sm" data-type="work">
                            <div class="flex items-start gap-4">
                                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center text-white">
                                    📊
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-start justify-between mb-2">
                                        <h3 class="text-lg font-semibold text-gray-800">A4竞争对手分析报告已完成</h3>
                                        <div class="flex items-center gap-2">
                                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded-full">工作完成</span>
                                            <span class="text-sm text-gray-500">2小时前</span>
                                        </div>
                                    </div>
                                    <p class="text-gray-600 mb-3">我已完成对德国市场主要竞争对手的深度分析，识别出12家核心竞品，并提供了详细的策略建议。</p>
                                    <div class="flex gap-3">
                                        <button class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors">查看报告</button>
                                        <button class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg text-sm transition-colors">标记已读</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 智能预警通知 -->
                        <div class="feed-item unread p-6 rounded-xl shadow-sm" data-type="alert">
                            <div class="flex items-start gap-4">
                                <div class="w-12 h-12 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center text-white">
                                    🚨
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-start justify-between mb-2">
                                        <h3 class="text-lg font-semibold text-gray-800">竞品价格重大变动预警</h3>
                                        <div class="flex items-center gap-2">
                                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs font-semibold rounded-full">智能预警</span>
                                            <span class="text-sm text-gray-500">1小时前</span>
                                        </div>
                                    </div>
                                    <p class="text-gray-600 mb-3">检测到Bosch在Amazon.de将Core系列产品价格下调15%，这是3个月内最大降幅，可能引发价格战。建议立即评估影响并调整策略。</p>
                                    <div class="flex gap-3">
                                        <button class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm transition-colors">查看详情</button>
                                        <button class="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg text-sm transition-colors">制定应对策略</button>
                                        <button class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg text-sm transition-colors">标记已读</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 系统通知 -->
                        <div class="feed-item read p-6 rounded-xl shadow-sm" data-type="system">
                            <div class="flex items-start gap-4">
                                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white">
                                    🔄
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-start justify-between mb-2">
                                        <h3 class="text-lg font-semibold text-gray-600">A4竞争对手分析已更新</h3>
                                        <div class="flex items-center gap-2">
                                            <span class="px-2 py-1 bg-gray-200 text-gray-600 text-xs font-semibold rounded-full">系统通知</span>
                                            <span class="text-sm text-gray-400">1天前</span>
                                        </div>
                                    </div>
                                    <p class="text-gray-500 mb-3">基于最新市场数据，我已更新德国市场格局分析报告，包含最新的市场规模和增长趋势数据。</p>
                                    <div class="flex gap-3">
                                        <button class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg text-sm transition-colors">查看更新</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 更多Feed项... -->
                        <div class="feed-item read p-6 rounded-xl shadow-sm" data-type="alert">
                            <div class="flex items-start gap-4">
                                <div class="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center text-white">
                                    📈
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-start justify-between mb-2">
                                        <h3 class="text-lg font-semibold text-gray-600">德国智能家居市场异动</h3>
                                        <div class="flex items-center gap-2">
                                            <span class="px-2 py-1 bg-gray-200 text-gray-600 text-xs font-semibold rounded-full">智能预警</span>
                                            <span class="text-sm text-gray-400">3天前</span>
                                        </div>
                                    </div>
                                    <p class="text-gray-500 mb-3">检测到"智能家居"类目搜索量激增200%，出现新的爆款产品趋势，建议关注相关商机。</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 加载更多 -->
                    <div class="text-center mt-8">
                        <button class="px-6 py-3 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                            加载更多动态
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tab 2: 工作看板 -->
            <div class="tab-content bg-white rounded-b-2xl rounded-tr-2xl shadow-lg" id="kanban">
                <div class="p-8">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">Emma的工作看板</h2>
                        <div class="flex items-center gap-4">
                            <span class="px-3 py-1 bg-green-100 text-green-800 text-sm font-semibold rounded-full">按年订阅</span>
                            <span class="text-sm text-gray-600">订阅到期：2025年1月15日</span>
                        </div>
                    </div>

                    <!-- Artifacts进度概览 - 按业务模块分组 -->
                    <div class="space-y-8 mb-8">

                        <!-- 核心要素分析组 -->
                        <div class="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-2xl border border-purple-200">
                            <div class="flex items-center gap-3 mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center text-white">
                                    <span class="text-xl">👥</span>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-xl font-bold text-gray-800">核心要素分析</h3>
                                    <p class="text-sm text-gray-600">深入了解竞争对手、合作伙伴和目标客户</p>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-2 mb-1">
                                        <span class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                                        <span class="text-xs font-medium text-green-600">实时监控</span>
                                    </div>
                                    <span class="text-xs text-gray-500">正在监控 10 家竞品</span>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                                <div class="artifact-progress completed bg-white p-4 rounded-xl shadow-sm">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A4</div>
                                        <div class="flex-1">
                                            <h4 class="font-semibold text-gray-800">核心竞争对手深度剖析</h4>
                                            <div class="flex items-center gap-2 mt-1">
                                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded-full">已完成</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full bg-green-200 rounded-full h-2 mb-3">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                                    </div>
                                    <div class="bg-green-50 p-3 rounded-lg border border-green-200 mb-2">
                                        <div class="flex items-center gap-2 text-sm">
                                            <span class="text-green-600">🔍</span>
                                            <span class="font-medium text-green-800">实时监控：</span>
                                            <span class="text-green-700">正在7×24小时监控 10 家核心竞品动态</span>
                                        </div>
                                    </div>
                                    <span class="text-xs text-gray-500">完成于 1月15日</span>
                                </div>

                                <div class="artifact-progress bg-white p-4 rounded-xl shadow-sm border-2 border-dashed border-gray-300 opacity-60">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center text-white font-bold text-sm">A5</div>
                                        <div class="flex-1">
                                            <h4 class="font-semibold text-gray-600">理想合作伙伴评估与匹配</h4>
                                            <div class="flex items-center gap-2 mt-1">
                                                <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs font-semibold rounded-full">未购买</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                                        <div class="bg-gray-400 h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <span class="text-xs text-gray-400">可选模块</span>
                                </div>

                                <div class="artifact-progress bg-white p-4 rounded-xl shadow-sm">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A9</div>
                                        <div class="flex-1">
                                            <h4 class="font-semibold text-gray-800">目标市场理想客户画像构建</h4>
                                            <div class="flex items-center gap-2 mt-1">
                                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-semibold rounded-full">待开始</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full bg-yellow-200 rounded-full h-2 mb-2">
                                        <div class="bg-yellow-500 h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <span class="text-xs text-gray-500">预计 1月22日开始</span>
                                </div>
                            </div>
                        </div>

                        <!-- 专项策略制定组 -->
                        <div class="bg-gradient-to-r from-orange-50 to-red-50 p-6 rounded-2xl border border-orange-200">
                            <div class="flex items-center gap-3 mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center text-white">
                                    <span class="text-xl">⚔️</span>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-xl font-bold text-gray-800">专项策略制定</h3>
                                    <p class="text-sm text-gray-600">设计具体的市场进入和运营策略</p>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-2 mb-1">
                                        <span class="w-2 h-2 bg-cyan-500 rounded-full animate-pulse"></span>
                                        <span class="text-xs font-medium text-cyan-600">平台监控</span>
                                    </div>
                                    <span class="text-xs text-gray-500">监控 3 个电商平台</span>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                <div class="artifact-progress bg-white p-4 rounded-xl shadow-sm border-2 border-dashed border-gray-300 opacity-60">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center text-white font-bold text-sm">A6</div>
                                        <div class="flex-1">
                                            <h4 class="font-semibold text-gray-600">领先者研究与差异化策略</h4>
                                            <div class="flex items-center gap-2 mt-1">
                                                <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs font-semibold rounded-full">未购买</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                                        <div class="bg-gray-400 h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <span class="text-xs text-gray-400">可选模块</span>
                                </div>

                                <div class="artifact-progress completed bg-white p-4 rounded-xl shadow-sm">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A7</div>
                                        <div class="flex-1">
                                            <h4 class="font-semibold text-gray-800">出海合规风险导航</h4>
                                            <div class="flex items-center gap-2 mt-1">
                                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded-full">已完成</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full bg-green-200 rounded-full h-2 mb-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                                    </div>
                                    <span class="text-xs text-gray-500">完成于 1月12日</span>
                                </div>

                                <div class="artifact-progress bg-white p-4 rounded-xl shadow-sm border-2 border-dashed border-gray-300 opacity-60">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center text-white font-bold text-sm">A10</div>
                                        <div class="flex-1">
                                            <h4 class="font-semibold text-gray-800">目标市场定价与商业模式分析</h4>
                                            <div class="flex items-center gap-2 mt-1">
                                                <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs font-semibold rounded-full">未购买</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                                        <div class="bg-gray-400 h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <span class="text-xs text-gray-400">可选模块</span>
                                </div>

                                <div class="artifact-progress in-progress bg-white p-4 rounded-xl shadow-sm">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A11</div>
                                        <div class="flex-1">
                                            <h4 class="font-semibold text-gray-800">电商渠道进入策略</h4>
                                            <div class="flex items-center gap-2 mt-1">
                                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded-full">进行中</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full bg-blue-200 rounded-full h-2 mb-3">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: 75%"></div>
                                    </div>
                                    <div class="bg-cyan-50 p-3 rounded-lg border border-cyan-200 mb-2">
                                        <div class="flex items-center gap-2 text-sm">
                                            <span class="text-cyan-600">📊</span>
                                            <span class="font-medium text-cyan-800">平台监控：</span>
                                            <span class="text-cyan-700">正在监控 3 个电商平台，按月更新数据</span>
                                        </div>
                                        <div class="flex items-center gap-2 text-xs text-cyan-600 mt-1">
                                            <span>Amazon.de • eBay.de • Otto.de</span>
                                        </div>
                                    </div>
                                    <div class="flex justify-between text-xs text-gray-500">
                                        <span>75% 完成，正在分析平台数据</span>
                                        <span>预计 1月20日</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 整合策略 -->
                        <div class="bg-gradient-to-r from-emerald-50 to-green-50 p-6 rounded-2xl border border-emerald-200">
                            <div class="flex items-center gap-3 mb-6">
                                <div class="w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-500 rounded-xl flex items-center justify-center text-white">
                                    <span class="text-xl">🎯</span>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-xl font-bold text-gray-800">整合策略输出</h3>
                                    <p class="text-sm text-gray-600">形成完整的市场进入方案</p>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-2 mb-1">
                                        <span class="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></span>
                                        <span class="text-xs font-medium text-purple-600">月度复盘</span>
                                    </div>
                                    <span class="text-xs text-gray-500">下次会议：1月30日</span>
                                </div>
                            </div>
                            
                            <div class="space-y-4">
                                <div class="artifact-progress in-progress bg-white p-4 rounded-xl shadow-sm">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A8</div>
                                        <div class="flex-1">
                                            <h4 class="font-semibold text-gray-800">整合策略规划与动态优化</h4>
                                            <div class="flex items-center gap-4 mt-1">
                                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded-full">进行中</span>
                                                <span class="text-xs text-gray-500">预计 1月25日</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full bg-blue-200 rounded-full h-2 mb-3">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: 40%"></div>
                                    </div>
                                    <div class="bg-purple-50 p-3 rounded-lg border border-purple-200 mb-2">
                                        <div class="flex items-center gap-2 text-sm">
                                            <span class="text-purple-600">📅</span>
                                            <span class="font-medium text-purple-800">战略复盘会：</span>
                                            <span class="text-purple-700">1月30日 14:00 - 月度策略复盘与优化</span>
                                        </div>
                                    </div>
                                    <p class="text-xs text-blue-700">正在整合前期分析结果，等待A11完成后进入最终整合阶段</p>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab 3: 交付成果中心 -->
            <div class="tab-content bg-white rounded-b-2xl rounded-tr-2xl shadow-lg" id="reports">
                <div class="p-8">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">交付成果中心</h2>
                        <div class="flex gap-3">
                            <input type="text" placeholder="搜索报告..." class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>全部类型</option>
                                <option>市场分析</option>
                                <option>竞品分析</option>
                                <option>合规报告</option>
                                <option>渠道分析</option>
                            </select>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- A4报告 -->
                        <div class="bg-gradient-to-br from-white to-red-50 rounded-2xl shadow-lg border border-red-100 overflow-hidden hover:shadow-xl transition-all duration-300">
                            <div class="h-48 bg-gradient-to-br from-red-100 to-red-200 flex items-center justify-center relative">
                                <img src="assets/reports/report-a4.svg" alt="A4报告" class="w-40 h-32 object-cover rounded-lg shadow-md">
                                <div class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                                    v1.1
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-3">
                                    <h3 class="text-xl font-bold text-gray-800">A4 - 德国竞争对手深度分析</h3>
                                    <select class="text-sm border border-gray-300 rounded px-2 py-1 bg-white">
                                        <option>v1.1 (季度更新)</option>
                                        <option>v1.0 (初版)</option>
                                    </select>
                                </div>
                                <p class="text-gray-600 mb-4 leading-relaxed">深度分析德国市场主要竞争对手策略、定价模式及市场表现，包含2024年Q1最新数据更新。</p>
                                
                                <!-- 主PPT -->
                                <div class="bg-red-50 p-4 rounded-lg border border-red-200 mb-4">
                                    <div class="flex items-center gap-3 mb-3">
                                        <span class="text-red-600">🎯</span>
                                        <span class="font-semibold text-red-800">主要演示文稿</span>
                                    </div>
                                    <div class="flex items-center gap-4 mb-3">
                                        <span class="px-3 py-1 bg-red-100 text-red-800 text-sm font-semibold rounded-full">A4 PPT</span>
                                        <span class="text-sm text-gray-600">2024-01-10</span>
                                        <span class="text-sm text-green-600 font-medium">45页</span>
                                    </div>
                                    <div class="flex flex-wrap gap-2">
                                        <button class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>👁️</span>
                                            预览PPT
                                        </button>
                                        <button class="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>🎧</span>
                                            AI讲解
                                        </button>
                                        <button class="px-3 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>📥</span>
                                            下载
                                        </button>
                                    </div>
                                </div>

                                <!-- 附件文档 -->
                                <div class="border-t border-gray-200 pt-4">
                                    <div class="flex items-center gap-2 mb-3">
                                        <span class="text-gray-600">📎</span>
                                        <span class="font-medium text-gray-700">附件文档 (3个)</span>
                                        <button class="text-blue-600 text-sm hover:underline">展开</button>
                                    </div>
                                    <div class="space-y-2 text-sm">
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">德国市场数据详细报告.pdf (40页)</span>
                                            <button class="text-blue-600 hover:underline">预览</button>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">竞争格局图表集.pdf (25页)</span>
                                            <button class="text-blue-600 hover:underline">预览</button>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">政策法规摘要.pdf (15页)</span>
                                            <button class="text-blue-600 hover:underline">预览</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- A4报告 -->
                        <div class="bg-gradient-to-br from-white to-red-50 rounded-2xl shadow-lg border border-red-100 overflow-hidden hover:shadow-xl transition-all duration-300">
                            <div class="h-48 bg-gradient-to-br from-red-100 to-rose-200 flex items-center justify-center relative">
                                <img src="assets/reports/report-a4.svg" alt="A4报告" class="w-40 h-32 object-cover rounded-lg shadow-md">
                                <div class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                                    v1.0
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-3">
                                    <h3 class="text-xl font-bold text-gray-800">A4 - 核心竞争对手深度剖析</h3>
                                    <select class="text-sm border border-gray-300 rounded px-2 py-1 bg-white">
                                        <option>v1.0 (初版)</option>
                                    </select>
                                </div>
                                <p class="text-gray-600 mb-4 leading-relaxed">深度分析德国市场12家核心竞争对手的策略、定价、优劣势，实时监控数据更新。</p>
                                
                                <!-- 主PPT -->
                                <div class="bg-red-50 p-4 rounded-lg border border-red-200 mb-4">
                                    <div class="flex items-center gap-3 mb-3">
                                        <span class="text-red-600">🎯</span>
                                        <span class="font-semibold text-red-800">主要演示文稿</span>
                                    </div>
                                    <div class="flex items-center gap-4 mb-3">
                                        <span class="px-3 py-1 bg-red-100 text-red-800 text-sm font-semibold rounded-full">A4 PPT</span>
                                        <span class="text-sm text-gray-600">2024-01-15</span>
                                        <span class="text-sm text-red-600 font-medium">52页</span>
                                    </div>
                                    <div class="flex flex-wrap gap-2">
                                        <button class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>👁️</span>
                                            预览PPT
                                        </button>
                                        <button class="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>🎧</span>
                                            AI讲解
                                        </button>
                                        <button class="px-3 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>📥</span>
                                            下载
                                        </button>
                                    </div>
                                </div>

                                <!-- 附件文档 -->
                                <div class="border-t border-gray-200 pt-4">
                                    <div class="flex items-center gap-2 mb-3">
                                        <span class="text-gray-600">📎</span>
                                        <span class="font-medium text-gray-700">附件文档 (4个)</span>
                                        <button class="text-blue-600 text-sm hover:underline">展开</button>
                                    </div>
                                    <div class="space-y-2 text-sm">
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">竞争对手详细档案.pdf (68页)</span>
                                            <button class="text-blue-600 hover:underline">预览</button>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">价格监控数据表.xlsx</span>
                                            <button class="text-blue-600 hover:underline">预览</button>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">SWOT分析矩阵.pdf (12页)</span>
                                            <button class="text-blue-600 hover:underline">预览</button>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">竞品动态日报.pdf (28页)</span>
                                            <button class="text-blue-600 hover:underline">预览</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- A7报告 -->
                        <div class="bg-gradient-to-br from-white to-yellow-50 rounded-2xl shadow-lg border border-yellow-100 overflow-hidden hover:shadow-xl transition-all duration-300">
                            <div class="h-48 bg-gradient-to-br from-yellow-100 to-amber-200 flex items-center justify-center relative">
                                <img src="assets/reports/report-a7.svg" alt="A7报告" class="w-40 h-32 object-cover rounded-lg shadow-md">
                                <div class="absolute top-4 right-4 bg-yellow-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                                    v1.0
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-3">
                                    <h3 class="text-xl font-bold text-gray-800">A7 - 出海合规风险导航</h3>
                                    <select class="text-sm border border-gray-300 rounded px-2 py-1 bg-white">
                                        <option>v1.0 (初版)</option>
                                    </select>
                                </div>
                                <p class="text-gray-600 mb-4 leading-relaxed">全面梳理德国市场法规环境，识别合规风险点，提供详细的风险缓解方案。</p>
                                
                                <!-- 主PPT -->
                                <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200 mb-4">
                                    <div class="flex items-center gap-3 mb-3">
                                        <span class="text-yellow-600">🎯</span>
                                        <span class="font-semibold text-yellow-800">主要演示文稿</span>
                                    </div>
                                    <div class="flex items-center gap-4 mb-3">
                                        <span class="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm font-semibold rounded-full">A7 PPT</span>
                                        <span class="text-sm text-gray-600">2024-01-12</span>
                                        <span class="text-sm text-yellow-600 font-medium">38页</span>
                                    </div>
                                    <div class="flex flex-wrap gap-2">
                                        <button class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>👁️</span>
                                            预览PPT
                                        </button>
                                        <button class="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>🎧</span>
                                            AI讲解
                                        </button>
                                        <button class="px-3 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors text-sm flex items-center gap-1">
                                            <span>📥</span>
                                            下载
                                        </button>
                                    </div>
                                </div>

                                <!-- 附件文档 -->
                                <div class="border-t border-gray-200 pt-4">
                                    <div class="flex items-center gap-2 mb-3">
                                        <span class="text-gray-600">📎</span>
                                        <span class="font-medium text-gray-700">附件文档 (2个)</span>
                                        <button class="text-blue-600 text-sm hover:underline">展开</button>
                                    </div>
                                    <div class="space-y-2 text-sm">
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">德国法规详细条文.pdf (30页)</span>
                                            <button class="text-blue-600 hover:underline">预览</button>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-gray-700">合规检查清单.pdf (8页)</span>
                                            <button class="text-blue-600 hover:underline">预览</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 即将完成的A11报告预览 -->
                        <div class="bg-gradient-to-br from-white to-blue-50 rounded-2xl shadow-lg border border-blue-100 overflow-hidden hover:shadow-xl transition-all duration-300 opacity-75">
                            <div class="h-48 bg-gradient-to-br from-blue-100 to-cyan-200 flex items-center justify-center relative">
                                <div class="w-40 h-32 bg-white/50 rounded-lg shadow-md flex items-center justify-center">
                                    <span class="text-4xl">📊</span>
                                </div>
                                <div class="absolute top-4 right-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                                    75% 完成
                                </div>
                            </div>
                            <div class="p-6">
                                <h3 class="text-xl font-bold text-gray-700 mb-3">A11 - 电商渠道进入策略</h3>
                                <p class="text-gray-500 mb-4 leading-relaxed">正在分析Amazon.de、eBay.de、Otto.de等平台的多渠道战略布局...</p>
                                <div class="flex items-center gap-4 mb-4">
                                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-semibold rounded-full">A11 渠道分析</span>
                                    <span class="text-sm text-gray-400">预计 2024-01-20</span>
                                    <span class="text-sm text-blue-600 font-medium">制作中...</span>
                                </div>
                                <div class="w-full bg-blue-200 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: 75%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab 4: 任务委托中心 -->
            <div class="tab-content bg-white rounded-b-2xl rounded-tr-2xl shadow-lg" id="tasks">
                <div class="p-8">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-800 mb-4">Emma的任务委托中心</h2>
                        <p class="text-gray-600">委托Emma执行特定的市场分析任务，直接在各任务卡片中购买或管理配置</p>
                    </div>

                    <div class="grid grid-cols-4 gap-8">
                        <!-- 左侧：Artifacts配置 -->
                        <div class="col-span-3">
                            <!-- 核心要素分析组 -->
                            <div class="mb-8">
                                <div class="flex items-center gap-3 mb-6">
                                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-xl flex items-center justify-center text-white">
                                        <span class="text-xl">👥</span>
                                    </div>
                                    <div>
                                        <h3 class="text-xl font-bold text-gray-800">核心要素分析</h3>
                                        <p class="text-sm text-gray-600">深入了解竞争对手、合作伙伴和目标客户</p>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                                    <!-- A4 - 已购买状态 -->
                                    <div class="artifact-config border-2 rounded-xl p-6 cursor-pointer transition-all border-green-200 bg-green-50" data-artifact="A4" data-price="2000" data-status="purchased">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A4</div>
                                            <div class="flex-1">
                                                <h4 class="font-bold text-gray-800">核心竞争对手深度剖析</h4>
                                                <div class="flex items-center gap-2 mt-1">
                                                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded-full">✓ 已激活</span>
                                                    <span class="text-xs text-gray-500">按月订阅</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="text-sm text-gray-700">• 主要竞品策略深度分析</div>
                                            <div class="text-sm text-gray-700">• 定价策略对比研究</div>
                                            <div class="text-sm text-gray-700">• 产品优劣势评估</div>
                                            <div class="text-sm text-gray-700">• 竞品动态实时追踪</div>
                                        </div>
                                        <div class="bg-green-100 p-3 rounded-lg border border-green-200 mb-4">
                                            <div class="flex items-center gap-2 text-sm">
                                                <span class="text-green-600">🔍</span>
                                                <span class="font-medium text-green-800">监控中：</span>
                                                <span class="text-green-700">10 家竞争对手 (最多20家)</span>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <span class="text-green-600 font-semibold">2,000 点数/月</span>
                                                <span class="text-xs text-gray-500 line-through">¥2,000</span>
                                            </div>
                                            <button class="px-3 py-1 bg-green-600 text-white rounded-lg text-sm hover:bg-green-700 transition-colors" onclick="openA4ConfigModal()">
                                                管理配置
                                            </button>
                                        </div>
                                    </div>

                                    <!-- A5 - 未购买状态 -->
                                    <div class="artifact-config border-2 rounded-xl p-6 cursor-pointer transition-all border-gray-300 bg-gray-50" data-artifact="A5" data-price="20000" data-status="unpurchased">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center text-white font-bold text-sm">A5</div>
                                            <div class="flex-1">
                                                <h4 class="font-bold text-gray-800">理想合作伙伴评估与匹配</h4>
                                                <div class="flex items-center gap-2 mt-1">
                                                    <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs font-semibold rounded-full">未购买</span>
                                                    <span class="text-xs text-gray-500">按年订阅</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="text-sm text-gray-600">• 潜在合作伙伴识别</div>
                                            <div class="text-sm text-gray-600">• 合作模式分析</div>
                                            <div class="text-sm text-gray-600">• 风险评估与建议</div>
                                            <div class="text-sm text-gray-600">• 合作谈判要点</div>
                                        </div>
                                        <div class="bg-gray-100 p-3 rounded-lg border border-gray-200 mb-4">
                                            <div class="flex items-center gap-2 text-sm">
                                                <span class="text-gray-500">📅</span>
                                                <span class="font-medium text-gray-600">更新频率：</span>
                                                <span class="text-gray-500">按季度更新</span>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <span class="text-purple-600 font-semibold">20,000 点数/年</span>
                                                <span class="text-xs text-gray-500 line-through">¥20,000</span>
                                            </div>
                                            <button class="px-3 py-1 bg-purple-600 text-white rounded-lg text-sm hover:bg-purple-700 transition-colors" onclick="purchaseArtifact('A5', 20000)">
                                                立即购买
                                            </button>
                                        </div>
                                    </div>

                                    <!-- A9 -->
                                    <div class="artifact-config border-2 rounded-xl p-6 cursor-pointer transition-all border-purple-200 bg-purple-50" data-artifact="A9" data-price="20000" data-status="active">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A9</div>
                                            <div class="flex-1">
                                                <h4 class="font-bold text-gray-800">目标市场理想客户画像构建</h4>
                                                <div class="flex items-center gap-2 mt-1">
                                                    <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs font-semibold rounded-full">已激活</span>
                                                    <span class="text-xs text-gray-500">按年订阅</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="text-sm text-gray-700">• 构建德国市场理想客户画像(ICP)</div>
                                            <div class="text-sm text-gray-700">• 客户需求深度分析</div>
                                            <div class="text-sm text-gray-700">• 购买决策流程研究</div>
                                            <div class="text-sm text-gray-700">• 客户获取渠道建议</div>
                                        </div>
                                        <div class="bg-purple-100 p-3 rounded-lg border border-purple-200 mb-4">
                                            <div class="flex items-center gap-2 text-sm">
                                                <span class="text-purple-600">📅</span>
                                                <span class="font-medium text-purple-800">更新频率：</span>
                                                <span class="text-purple-700">按季度更新</span>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <span class="text-purple-600 font-semibold">20,000 点数/年</span>
                                                <span class="text-xs text-gray-500 line-through">¥20,000</span>
                                            </div>
                                            <button class="px-3 py-1 bg-purple-600 text-white rounded-lg text-sm hover:bg-purple-700 transition-colors" onclick="openSubscriptionConfigModal('A9')">
                                                管理配置
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 专项策略制定组 -->
                            <div class="mb-8">
                                <div class="flex items-center gap-3 mb-6">
                                    <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center text-white">
                                        <span class="text-xl">⚔️</span>
                                    </div>
                                    <div>
                                        <h3 class="text-xl font-bold text-gray-800">专项策略制定</h3>
                                        <p class="text-sm text-gray-600">设计具体的市场进入和运营策略</p>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                    <!-- A6 -->
                                    <div class="artifact-config border-2 rounded-xl p-6 cursor-pointer transition-all border-orange-200 bg-orange-50" data-artifact="A6" data-price="50000" data-status="active">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A6</div>
                                            <div class="flex-1">
                                                <h4 class="font-bold text-gray-800">领先者研究与差异化策略</h4>
                                                <div class="flex items-center gap-2 mt-1">
                                                    <span class="px-2 py-1 bg-orange-100 text-orange-800 text-xs font-semibold rounded-full">已激活</span>
                                                    <span class="text-xs text-gray-500">按年订阅</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="text-sm text-gray-700">• 行业领先者深度研究</div>
                                            <div class="text-sm text-gray-700">• 差异化机会识别</div>
                                            <div class="text-sm text-gray-700">• 竞争优势构建</div>
                                            <div class="text-sm text-gray-700">• 突围策略设计</div>
                                        </div>
                                        <div class="bg-orange-100 p-3 rounded-lg border border-orange-200 mb-4">
                                            <div class="flex items-center gap-2 text-sm">
                                                <span class="text-orange-600">🏢</span>
                                                <span class="font-medium text-orange-800">研究中：</span>
                                                <span class="text-orange-700">3 家领先者 (最多3家)</span>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <span class="text-orange-600 font-semibold">50,000 点数/年</span>
                                                <span class="text-xs text-gray-500 line-through">¥50,000</span>
                                            </div>
                                            <button class="px-3 py-1 bg-orange-600 text-white rounded-lg text-sm hover:bg-orange-700 transition-colors" onclick="openA6ConfigModal()">
                                                管理配置
                                            </button>
                                        </div>
                                    </div>

                                    <!-- A7 -->
                                    <div class="artifact-config border-2 rounded-xl p-6 cursor-pointer transition-all border-green-200 bg-green-50" data-artifact="A7" data-price="20000" data-status="active">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A7</div>
                                            <div class="flex-1">
                                                <h4 class="font-bold text-gray-800">出海合规风险导航</h4>
                                                <div class="flex items-center gap-2 mt-1">
                                                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded-full">已激活</span>
                                                    <span class="text-xs text-gray-500">按年订阅</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="text-sm text-gray-700">• 全面梳理德国市场法规环境</div>
                                            <div class="text-sm text-gray-700">• 识别合规风险点</div>
                                            <div class="text-sm text-gray-700">• 提供详细风险缓解方案</div>
                                            <div class="text-sm text-gray-700">• 合规操作指导手册</div>
                                        </div>
                                        <div class="bg-green-100 p-3 rounded-lg border border-green-200 mb-4">
                                            <div class="flex items-center gap-2 text-sm">
                                                <span class="text-green-600">📅</span>
                                                <span class="font-medium text-green-800">更新频率：</span>
                                                <span class="text-green-700">按季度更新</span>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <span class="text-green-600 font-semibold">20,000 点数/年</span>
                                                <span class="text-xs text-gray-500 line-through">¥20,000</span>
                                            </div>
                                            <button class="px-3 py-1 bg-green-600 text-white rounded-lg text-sm hover:bg-green-700 transition-colors" onclick="openA7ConfigModal()">
                                                管理配置
                                            </button>
                                        </div>
                                    </div>

                                    <!-- A10 -->
                                    <div class="artifact-config border-2 rounded-xl p-6 cursor-pointer transition-all border-teal-200 bg-teal-50" data-artifact="A10" data-price="20000" data-status="active">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A10</div>
                                            <div class="flex-1">
                                                <h4 class="font-bold text-gray-800">目标市场定价与商业模式分析</h4>
                                                <div class="flex items-center gap-2 mt-1">
                                                    <span class="px-2 py-1 bg-teal-100 text-teal-800 text-xs font-semibold rounded-full">已激活</span>
                                                    <span class="text-xs text-gray-500">按年订阅</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="text-sm text-gray-700">• 市场定价策略分析</div>
                                            <div class="text-sm text-gray-700">• 商业模式优化</div>
                                            <div class="text-sm text-gray-700">• 盈利模式设计</div>
                                            <div class="text-sm text-gray-700">• 收入预测模型</div>
                                        </div>
                                        <div class="bg-teal-100 p-3 rounded-lg border border-teal-200 mb-4">
                                            <div class="flex items-center gap-2 text-sm">
                                                <span class="text-teal-600">📅</span>
                                                <span class="font-medium text-teal-800">更新频率：</span>
                                                <span class="text-teal-700">按季度更新</span>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <span class="text-teal-600 font-semibold">20,000 点数/年</span>
                                                <span class="text-xs text-gray-500 line-through">¥20,000</span>
                                            </div>
                                            <button class="px-3 py-1 bg-teal-600 text-white rounded-lg text-sm hover:bg-teal-700 transition-colors" onclick="openSubscriptionConfigModal('A10')">
                                                管理配置
                                            </button>
                                        </div>
                                    </div>

                                    <!-- A11 -->
                                    <div class="artifact-config border-2 rounded-xl p-6 cursor-pointer transition-all border-cyan-200 bg-cyan-50" data-artifact="A11" data-price="50000" data-status="active">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A11</div>
                                            <div class="flex-1">
                                                <h4 class="font-bold text-gray-800">电商渠道进入策略</h4>
                                                <div class="flex items-center gap-2 mt-1">
                                                    <span class="px-2 py-1 bg-cyan-100 text-cyan-800 text-xs font-semibold rounded-full">已激活</span>
                                                    <span class="text-xs text-gray-500">按年订阅</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="text-sm text-gray-700">• Amazon.de平台深度分析</div>
                                            <div class="text-sm text-gray-700">• eBay.de、Otto.de等平台对比</div>
                                            <div class="text-sm text-gray-700">• 渠道表现优化建议</div>
                                            <div class="text-sm text-gray-700">• 销售数据解读与洞察</div>
                                        </div>
                                        <div class="bg-cyan-100 p-3 rounded-lg border border-cyan-200 mb-4">
                                            <div class="flex items-center gap-2 text-sm">
                                                <span class="text-cyan-600">🛒</span>
                                                <span class="font-medium text-cyan-800">监控平台：</span>
                                                <span class="text-cyan-700">3 个电商平台 (基础包含)</span>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <span class="text-cyan-600 font-semibold">50,000 点数/年</span>
                                                <span class="text-xs text-gray-500 line-through">¥50,000</span>
                                            </div>
                                            <button class="px-3 py-1 bg-cyan-600 text-white rounded-lg text-sm hover:bg-cyan-700 transition-colors" onclick="openA11ConfigModal()">
                                                管理配置
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 整合策略输出组 -->
                            <div class="mb-8">
                                <div class="flex items-center gap-3 mb-6">
                                    <div class="w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-500 rounded-xl flex items-center justify-center text-white">
                                        <span class="text-xl">🎯</span>
                                    </div>
                                    <div>
                                        <h3 class="text-xl font-bold text-gray-800">整合策略输出</h3>
                                        <p class="text-sm text-gray-600">形成完整的市场进入方案</p>
                                    </div>
                                </div>
                                
                                <div class="space-y-4">
                                    <!-- A8 -->
                                    <div class="artifact-config border-2 rounded-xl p-6 cursor-pointer transition-all border-emerald-200 bg-emerald-50" data-artifact="A8" data-price="20000" data-status="active">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center text-white font-bold text-sm">A8</div>
                                            <div class="flex-1">
                                                <h4 class="font-bold text-gray-800">整合策略规划与动态优化</h4>
                                                <div class="flex items-center gap-4 mt-1">
                                                    <span class="px-2 py-1 bg-emerald-100 text-emerald-800 text-xs font-semibold rounded-full">已激活</span>
                                                    <span class="text-xs text-gray-500">按年订阅</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="text-sm text-gray-700">• 按需定制分析报告</div>
                                            <div class="text-sm text-gray-700">• 可视化数据呈现</div>
                                            <div class="text-sm text-gray-700">• 操作建议与行动方案</div>
                                            <div class="text-sm text-gray-700">• 定期总结与复盘</div>
                                        </div>
                                        <div class="bg-emerald-100 p-3 rounded-lg border border-emerald-200 mb-4">
                                            <div class="flex items-center gap-2 text-sm">
                                                <span class="text-emerald-600">📅</span>
                                                <span class="font-medium text-emerald-800">更新频率：</span>
                                                <span class="text-emerald-700">按季度更新</span>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <span class="text-emerald-600 font-semibold">20,000 点数/年</span>
                                                <span class="text-xs text-gray-500 line-through">¥20,000</span>
                                            </div>
                                            <button class="px-3 py-1 bg-emerald-600 text-white rounded-lg text-sm hover:bg-emerald-700 transition-colors" onclick="openSubscriptionConfigModal('A8')">
                                                管理配置
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <!-- 右侧：任务控制面板 -->
                        <div class="col-span-1">
                            <div class="bg-gray-50 rounded-2xl p-6 sticky top-8">
                                <h3 class="text-lg font-bold text-gray-800 mb-4">任务控制面板</h3>
                                
                                <!-- 点数管理区域 -->
                                <div class="mb-6">
                                    <h4 class="text-sm font-semibold text-gray-700 mb-3">点数管理</h4>
                                    <div class="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-xl border border-blue-200">
                                        <div class="flex items-center justify-between mb-3">
                                            <div class="flex items-center gap-2">
                                                <div class="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-xs">💎</div>
                                                <span class="text-sm font-medium text-gray-700">当前余额</span>
                                            </div>
                                            <span class="text-xl font-bold text-blue-600" id="sidebarPointsBalance">8,250</span>
                                        </div>
                                        <button class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all shadow-md text-sm" onclick="openRechargeModal()">
                                            💳 充值点数
                                        </button>
                                    </div>
                                </div>

                                <!-- 任务状态概览 -->
                                <div class="mb-6">
                                    <h4 class="text-sm font-semibold text-gray-700 mb-3">任务状态概览</h4>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between p-2 bg-green-50 rounded-lg border border-green-200">
                                            <div class="flex items-center gap-2">
                                                <span class="w-4 h-4 bg-green-500 rounded-full text-xs flex items-center justify-center text-white font-bold">A4</span>
                                                <span class="text-xs text-green-700">竞品分析</span>
                                            </div>
                                            <span class="text-xs text-green-600">✓ 已激活</span>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-green-50 rounded-lg border border-green-200">
                                            <div class="flex items-center gap-2">
                                                <span class="w-4 h-4 bg-blue-500 rounded-full text-xs flex items-center justify-center text-white font-bold">A6</span>
                                                <span class="text-xs text-blue-700">领先者研究</span>
                                            </div>
                                            <span class="text-xs text-blue-600">✓ 已激活</span>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-green-50 rounded-lg border border-green-200">
                                            <div class="flex items-center gap-2">
                                                <span class="w-4 h-4 bg-green-500 rounded-full text-xs flex items-center justify-center text-white font-bold">A7</span>
                                                <span class="text-xs text-green-700">合规导航</span>
                                            </div>
                                            <span class="text-xs text-green-600">✓ 已激活</span>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-green-50 rounded-lg border border-green-200">
                                            <div class="flex items-center gap-2">
                                                <span class="w-4 h-4 bg-purple-500 rounded-full text-xs flex items-center justify-center text-white font-bold">A9</span>
                                                <span class="text-xs text-purple-700">客户画像</span>
                                            </div>
                                            <span class="text-xs text-purple-600">✓ 已激活</span>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-green-50 rounded-lg border border-green-200">
                                            <div class="flex items-center gap-2">
                                                <span class="w-4 h-4 bg-teal-500 rounded-full text-xs flex items-center justify-center text-white font-bold">A10</span>
                                                <span class="text-xs text-teal-700">定价模式</span>
                                            </div>
                                            <span class="text-xs text-teal-600">✓ 已激活</span>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-green-50 rounded-lg border border-green-200">
                                            <div class="flex items-center gap-2">
                                                <span class="w-4 h-4 bg-cyan-500 rounded-full text-xs flex items-center justify-center text-white font-bold">A11</span>
                                                <span class="text-xs text-cyan-700">电商渠道</span>
                                            </div>
                                            <span class="text-xs text-cyan-600">✓ 已激活</span>
                                        </div>
                                        <div class="flex items-center justify-between p-2 bg-green-50 rounded-lg border border-green-200">
                                            <div class="flex items-center gap-2">
                                                <span class="w-4 h-4 bg-emerald-500 rounded-full text-xs flex items-center justify-center text-white font-bold">A8</span>
                                                <span class="text-xs text-emerald-700">整合策略</span>
                                            </div>
                                            <span class="text-xs text-emerald-600">✓ 已激活</span>
                                        </div>
                                        
                                        <!-- 未购买任务 -->
                                        <div class="border-t border-gray-300 pt-2 mt-3">
                                            <div class="flex items-center justify-between p-2 bg-gray-100 rounded-lg border border-gray-200 opacity-60">
                                                <div class="flex items-center gap-2">
                                                    <span class="w-4 h-4 bg-gray-400 rounded-full text-xs flex items-center justify-center text-white font-bold">A5</span>
                                                    <span class="text-xs text-gray-600">合作伙伴</span>
                                                </div>
                                                <span class="text-xs text-gray-500">未购买</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 快速统计 -->
                                <div class="mb-6">
                                    <h4 class="text-sm font-semibold text-gray-700 mb-3">任务统计</h4>
                                    <div class="grid grid-cols-2 gap-3">
                                        <div class="bg-white p-3 rounded-lg border border-gray-200 text-center">
                                            <div class="text-lg font-bold text-blue-600">7</div>
                                            <div class="text-xs text-gray-600">已激活</div>
                                        </div>
                                        <div class="bg-white p-3 rounded-lg border border-gray-200 text-center">
                                            <div class="text-lg font-bold text-gray-500">1</div>
                                            <div class="text-xs text-gray-600">待购买</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 月度费用概览 -->
                                <div class="mb-6">
                                    <h4 class="text-sm font-semibold text-gray-700 mb-3">月度费用</h4>
                                    <div class="bg-white p-4 rounded-lg border border-gray-200">
                                        <div class="flex justify-between text-sm mb-2">
                                            <span class="text-gray-600">月度订阅</span>
                                            <span class="font-semibold text-gray-800">2,000 点数</span>
                                        </div>
                                        <div class="flex justify-between text-sm mb-2">
                                            <span class="text-gray-600">年度订阅</span>
                                            <span class="font-semibold text-gray-800">170,000 点数</span>
                                        </div>
                                        <div class="border-t border-gray-300 pt-2 flex justify-between">
                                            <span class="font-bold text-gray-800 text-sm">月均成本</span>
                                            <span class="font-bold text-blue-600">16,167 点数</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-xs text-gray-500">
                                    <p>💡 提示：点击任务卡片直接购买或管理配置</p>
                                    <p>⚡ 已激活的任务Emma正在为您执行</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab 5: 员工信息 -->
            <div class="tab-content bg-white rounded-b-2xl rounded-tr-2xl shadow-lg" id="employee-info">
                <div class="p-8">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-800 mb-4">Emma - 员工信息管理</h2>
                        <p class="text-gray-600">管理Emma的基本信息和工作配置</p>
                    </div>

                    <div class="max-w-4xl mx-auto">
                        <!-- 基本信息编辑 -->
                        <div class="space-y-6">
                            <!-- 员工头像和基本信息 -->
                            <div class="bg-gradient-to-br from-white to-green-50 rounded-2xl p-6 border border-green-200">
                                <h3 class="text-xl font-bold text-gray-800 mb-6">基本信息</h3>
                                
                                <div class="flex items-start gap-6">
                                    <!-- 头像上传区域 -->
                                    <div class="text-center">
                                        <div class="relative">
                                            <img id="employeeAvatar" src="assets/avatars/emma-germany.svg" alt="Emma头像" class="w-24 h-24 rounded-full border-4 border-green-300 shadow-lg">
                                            <button class="absolute bottom-0 right-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors" onclick="triggerAvatarUpload()">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                            </button>
                                        </div>
                                        <input type="file" id="avatarUpload" accept="image/jpeg,image/png" class="hidden" onchange="handleAvatarUpload(this)">
                                        <button class="mt-3 text-sm text-blue-600 hover:underline" onclick="resetToDefaultAvatar()">
                                            恢复默认头像
                                        </button>
                                    </div>

                                    <!-- 基本信息表单 -->
                                    <div class="flex-1 space-y-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">员工姓名</label>
                                            <input type="text" id="employeeName" value="Emma" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" maxlength="20" oninput="updatePreview()">
                                            <p class="text-xs text-gray-500 mt-1">建议使用2-8个字符的名字</p>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">工作描述</label>
                                            <textarea id="employeeDescription" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows="3" maxlength="200" oninput="updatePreview()">专注于德国市场的战术执行与深度分析，基于总监的市场研究提供精准的竞品监控和渠道洞察。</textarea>
                                            <p class="text-xs text-gray-500 mt-1">200字以内，描述员工的主要工作职责</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 高级设置 -->
                                <div class="mt-6 bg-gray-50 rounded-xl p-4 border border-gray-200">
                                    <h4 class="text-sm font-semibold text-gray-700 mb-4">高级设置</h4>
                                    <div class="space-y-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <span class="font-medium text-gray-800">启用/停用</span>
                                                <p class="text-sm text-gray-600">控制员工是否继续工作</p>
                                            </div>
                                            <label class="relative inline-flex items-center cursor-pointer">
                                                <input type="checkbox" class="sr-only peer" checked>
                                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                            </label>
                                        </div>

                                        <div class="flex items-center justify-between">
                                            <div>
                                                <span class="font-medium text-gray-800">邮件通知</span>
                                                <p class="text-sm text-gray-600">重要工作完成时发送邮件提醒</p>
                                            </div>
                                            <label class="relative inline-flex items-center cursor-pointer">
                                                <input type="checkbox" class="sr-only peer" checked>
                                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-6 flex gap-4">
                                    <button class="flex-1 bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-all" onclick="saveEmployeeInfo()">
                                        保存修改
                                    </button>
                                    <button class="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg font-semibold hover:bg-gray-200 transition-all" onclick="resetEmployeeInfo()">
                                        重置
                                    </button>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeArtifactsConfig();
            initializeTabSystem();
            initializeEmployeeInfoTab();
        });

        // Tab切换功能
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const targetTab = this.dataset.tab;
                
                // 更新tab按钮状态
                document.querySelectorAll('.tab-btn').forEach(b => {
                    b.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600', 'bg-blue-50');
                    b.classList.add('text-gray-600');
                });
                this.classList.remove('text-gray-600');
                this.classList.add('text-blue-600', 'border-b-2', 'border-blue-600', 'bg-blue-50');
                
                // 更新tab内容
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById(targetTab).classList.add('active');
            });
        });

        // Feed筛选功能
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const filter = this.dataset.filter;
                
                // 更新筛选按钮状态
                document.querySelectorAll('.filter-btn').forEach(b => {
                    b.classList.remove('bg-blue-600', 'text-white');
                    b.classList.add('bg-gray-100', 'text-gray-700');
                });
                this.classList.remove('bg-gray-100', 'text-gray-700');
                this.classList.add('bg-blue-600', 'text-white');
                
                // 筛选feed项
                document.querySelectorAll('.feed-item').forEach(item => {
                    if (filter === 'all' || item.dataset.type === filter) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });

        // 统计时间维度切换
        function toggleTimeframe(type) {
            const periodSpan = document.getElementById(`${type}-period`);
            const valueSpan = document.getElementById(`${type}-value`);
            
            if (periodSpan.textContent === '本月') {
                periodSpan.textContent = '本年';
                // 更新为年度数据
                if (type === 'workHours') valueSpan.textContent = '1,856';
                if (type === 'value') valueSpan.textContent = '342.8';
                if (type === 'tasks') valueSpan.textContent = '1,068';
            } else {
                periodSpan.textContent = '本月';
                // 更新为月度数据
                if (type === 'workHours') valueSpan.textContent = '152';
                if (type === 'value') valueSpan.textContent = '28.5';
                if (type === 'tasks') valueSpan.textContent = '89';
            }
        }

        // 任务委托相关功能
        let selectedTasks = new Set();
        let selectedMode = '';
        
        const taskPrices = {
            'A5': 600,
            'A6': 900,
            'A10': 800,
            'monitoring': 899
        };

        // 任务选择
        document.querySelectorAll('.task-option').forEach(option => {
            option.addEventListener('click', function() {
                const checkbox = this.querySelector('.task-checkbox');
                const artifact = this.dataset.artifact;
                const price = parseInt(this.dataset.price);
                
                checkbox.checked = !checkbox.checked;
                
                if (checkbox.checked) {
                    selectedTasks.add(artifact);
                    this.classList.add('border-blue-400', 'bg-blue-50');
                } else {
                    selectedTasks.delete(artifact);
                    this.classList.remove('border-blue-400', 'bg-blue-50');
                }
                
                updateTaskSummary();
            });
        });

        // 服务模式选择
        document.querySelectorAll('.service-mode').forEach(mode => {
            mode.addEventListener('click', function() {
                document.querySelectorAll('.service-mode').forEach(m => {
                    m.classList.remove('border-blue-400', 'border-purple-400');
                });
                
                selectedMode = this.dataset.mode;
                
                if (selectedMode === 'onetime') {
                    this.classList.add('border-blue-400');
                } else {
                    this.classList.add('border-purple-400');
                }
                
                updateTaskSummary();
            });
        });

        // 更新任务摘要
        function updateTaskSummary() {
            const count = selectedTasks.size;
            const estimatedTime = count > 0 ? `${count * 3}-${count * 5}天` : '-';
            
            let total = 0;
            selectedTasks.forEach(task => {
                total += taskPrices[task] || 0;
            });
            
            document.getElementById('selected-count').textContent = `${count}个`;
            document.getElementById('estimated-time').textContent = estimatedTime;
            document.getElementById('service-mode').textContent = selectedMode === 'onetime' ? '一次性项目' : selectedMode === 'subscription' ? '按年订阅' : '未选择';
            
            // 更新价格
            document.getElementById('onetime-total').textContent = `¥${total.toLocaleString()}`;
            document.getElementById('subscription-total').textContent = `¥${(total + 3000).toLocaleString()}`;
            document.getElementById('total-cost').textContent = selectedMode === 'subscription' ? `¥${(total + 3000).toLocaleString()}` : `¥${total.toLocaleString()}`;
            
            // 更新按钮状态
            const assignBtn = document.getElementById('assignTaskBtn');
            if (count > 0 && selectedMode) {
                assignBtn.disabled = false;
                assignBtn.classList.remove('opacity-50');
            } else {
                assignBtn.disabled = true;
                assignBtn.classList.add('opacity-50');
            }
        }

        // 委托任务
        document.getElementById('assignTaskBtn').addEventListener('click', function() {
            if (selectedTasks.size > 0 && selectedMode) {
                const taskList = Array.from(selectedTasks).join(', ');
                const modeText = selectedMode === 'onetime' ? '一次性项目' : '按年订阅';
                alert(`已成功委托任务！\n\n任务：${taskList}\n服务模式：${modeText}\n\nEmma将在1个工作日内开始执行，您会收到任务开始的通知。`);
                
                // 重置选择
                selectedTasks.clear();
                selectedMode = '';
                document.querySelectorAll('.task-option').forEach(option => {
                    option.classList.remove('border-blue-400', 'bg-blue-50');
                    option.querySelector('.task-checkbox').checked = false;
                });
                document.querySelectorAll('.service-mode').forEach(mode => {
                    mode.classList.remove('border-blue-400', 'border-purple-400');
                });
                updateTaskSummary();
            }
        });

        // 查看工作职责
        function viewResponsibilities() {
            // 切换到任务委托tab
            document.querySelector('[data-tab="tasks"]').click();
        }

        // Artifacts配置中心功能
        function initializeArtifactsConfig() {
            const artifactCheckboxes = document.querySelectorAll('.artifact-checkbox');
            const serviceMode = document.querySelector('#serviceMode');
            let selectedArtifacts = new Set();
            let currentServiceMode = 'onetime';

            // Artifact选择功能
            artifactCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const artifactCard = this.closest('.artifact-config');
                    const artifactId = artifactCard.dataset.artifact;
                    
                    if (this.checked) {
                        selectedArtifacts.add(artifactId);
                        artifactCard.classList.add('border-blue-400', 'bg-blue-50');
                    } else {
                        selectedArtifacts.delete(artifactId);
                        artifactCard.classList.remove('border-blue-400', 'bg-blue-50');
                    }
                    
                    updateConfigSummary();
                });
            });

            // 服务模式选择
            if (serviceMode) {
                serviceMode.addEventListener('change', function() {
                    currentServiceMode = this.value;
                    updateConfigSummary();
                });
            }

            // 更新配置摘要
            function updateConfigSummary() {
                const summaryContainer = document.querySelector('#configSummary');
                if (!summaryContainer) return;

                let totalPrice = 0;
                const selectedList = [];

                selectedArtifacts.forEach(artifactId => {
                    const card = document.querySelector(`[data-artifact="${artifactId}"]`);
                    const price = parseInt(card.dataset.price);
                    totalPrice += price;
                    selectedList.push(artifactId);
                });

                // 如果是订阅模式，添加年度服务费
                if (currentServiceMode === 'subscription' && selectedArtifacts.size > 0) {
                    totalPrice += selectedArtifacts.size * 200; // 每个Artifact年度服务费200元
                }

                // 更新摘要显示
                const selectedTasksEl = document.querySelector('#selectedTasks');
                const totalCostEl = document.querySelector('#totalCost');
                const estimatedTimeEl = document.querySelector('#estimatedTime');
                const configureBtn = document.querySelector('#configureBtn');

                if (selectedTasksEl) {
                    selectedTasksEl.textContent = selectedList.length > 0 ? selectedList.join(', ') : '请选择Artifacts';
                }
                
                if (totalCostEl) {
                    totalCostEl.textContent = `¥${totalPrice}`;
                }
                
                if (estimatedTimeEl) {
                    estimatedTimeEl.textContent = selectedList.length > 0 ? `${selectedList.length * 3}-${selectedList.length * 5}天` : '-';
                }
                
                if (configureBtn) {
                    configureBtn.disabled = selectedList.length === 0;
                }
            }

            // 配置确认
            const configureBtn = document.querySelector('#configureBtn');
            if (configureBtn) {
                configureBtn.addEventListener('click', function() {
                    if (selectedArtifacts.size > 0) {
                        const artifactList = Array.from(selectedArtifacts).join(', ');
                        const modeText = currentServiceMode === 'onetime' ? '一次性项目' : '按年订阅';
                        
                        // 显示成功提示
                        const modal = document.createElement('div');
                        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                        modal.innerHTML = `
                            <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <span class="text-2xl text-white">✅</span>
                                    </div>
                                    <h3 class="text-2xl font-bold text-gray-800 mb-4">配置成功！</h3>
                                    <div class="bg-green-50 p-4 rounded-xl border border-green-200 mb-6">
                                        <div class="text-sm text-green-700 mb-2">已配置Artifacts：${artifactList}</div>
                                        <div class="text-sm text-green-700 mb-2">服务模式：${modeText}</div>
                                        <div class="text-xs text-green-600 mt-2">Emma将在1个工作日内开始执行</div>
                                    </div>
                                    <button class="w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove();">
                                        确定
                                    </button>
                                </div>
                            </div>
                        `;
                        document.body.appendChild(modal);
                        
                        // 重置选择
                        selectedArtifacts.clear();
                        artifactCheckboxes.forEach(checkbox => {
                            checkbox.checked = false;
                            checkbox.closest('.artifact-config').classList.remove('border-blue-400', 'bg-blue-50');
                        });
                        updateConfigSummary();
                    }
                });
            }
        }

        // Tab系统增强
        function initializeTabSystem() {
            const tabButtons = document.querySelectorAll('[data-tab]');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetTab = this.dataset.tab;
                    
                    // 更新按钮状态
                    tabButtons.forEach(btn => {
                        btn.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600', 'bg-blue-50');
                        btn.classList.add('text-gray-600', 'hover:text-blue-600', 'hover:bg-blue-50');
                    });
                    
                    this.classList.remove('text-gray-600', 'hover:text-blue-600', 'hover:bg-blue-50');
                    this.classList.add('text-blue-600', 'border-b-2', 'border-blue-600', 'bg-blue-50');
                    
                    // 更新内容显示
                    tabContents.forEach(content => {
                        if (content.id === targetTab) {
                            content.classList.add('active');
                            content.style.display = 'block';
                        } else {
                            content.classList.remove('active');
                            content.style.display = 'none';
                        }
                    });
                });
            });
        }

        // 员工信息Tab功能
        function initializeEmployeeInfoTab() {
            // 初始化技能标签功能
            
            // 实时预览更新
            updatePreview();
        }

        // 头像上传功能
        function triggerAvatarUpload() {
            document.getElementById('avatarUpload').click();
        }

        function handleAvatarUpload(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const avatarImg = document.getElementById('employeeAvatar');
                    const previewImg = document.getElementById('previewAvatar');
                    
                    avatarImg.src = e.target.result;
                    previewImg.src = e.target.result;
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        function resetToDefaultAvatar() {
            const defaultAvatar = 'assets/avatars/emma-germany.svg';
            document.getElementById('employeeAvatar').src = defaultAvatar;
            document.getElementById('previewAvatar').src = defaultAvatar;
        }

        // 实时预览更新
        function updatePreview() {
            const name = document.getElementById('employeeName').value || 'Emma';
            const countrySelect = document.getElementById('countrySelect');
            const selectedOption = countrySelect.options[countrySelect.selectedIndex];
            const countryName = selectedOption.text.split(' ')[1] || '德国'; // 获取国家名称，去掉旗帜
            const description = document.getElementById('employeeDescription').value || '专注于德国市场的战术执行与深度分析';
            
            document.getElementById('previewName').textContent = name;
            document.getElementById('previewCountry').textContent = countryName;
            document.getElementById('previewDescription').textContent = description;
        }


        // 保存员工信息
        function saveEmployeeInfo() {
            const name = document.getElementById('employeeName').value;
            const countrySelect = document.getElementById('countrySelect');
            const selectedOption = countrySelect.options[countrySelect.selectedIndex];
            const countryName = selectedOption.text.split(' ')[1] || '德国';
            const description = document.getElementById('employeeDescription').value;
            
            // 显示保存成功提示
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl text-white">✅</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">保存成功！</h3>
                        <div class="bg-green-50 p-4 rounded-xl border border-green-200 mb-6">
                            <div class="text-sm text-green-700 mb-2">员工信息已更新</div>
                            <div class="text-sm text-green-700 mb-2">姓名：${name}</div>
                            <div class="text-sm text-green-700 mb-2">负责国家：${countryName}</div>
                            <div class="text-xs text-green-600 mt-2">修改将在下次刷新后生效</div>
                        </div>
                        <button class="w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove();">
                            确定
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 重置员工信息
        function resetEmployeeInfo() {
            document.getElementById('employeeName').value = 'Emma';
            document.getElementById('countrySelect').value = 'germany';
            document.getElementById('employeeDescription').value = '专注于德国市场的战术执行与深度分析，基于总监的市场研究提供精准的竞品监控和渠道洞察。';
            
            resetToDefaultAvatar();
            updatePreview();
        }

        // 确认删除员工
        function confirmDeleteEmployee() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl text-white">⚠️</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">确认删除员工？</h3>
                        <div class="bg-red-50 p-4 rounded-xl border border-red-200 mb-6">
                            <div class="text-sm text-red-700 mb-2">此操作将永久删除Emma的所有数据：</div>
                            <div class="text-xs text-red-600">• 工作历史记录</div>
                            <div class="text-xs text-red-600">• 服务配置信息</div>
                            <div class="text-xs text-red-600">• 交付成果档案</div>
                            <div class="text-xs text-red-600 mt-2 font-semibold">此操作不可撤销！</div>
                        </div>
                        <div class="flex gap-4">
                            <button class="flex-1 bg-gray-600 text-white py-3 rounded-lg font-semibold hover:bg-gray-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove();">
                                取消
                            </button>
                            <button class="flex-1 bg-red-600 text-white py-3 rounded-lg font-semibold hover:bg-red-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove(); alert('员工已删除（演示功能）');">
                                确认删除
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // Artifacts配置管理模态框功能
        
        // A4 竞争对手管理配置
        function openA4ConfigModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-6 max-w-4xl w-full mx-4 shadow-2xl max-h-[90vh] overflow-y-auto">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center text-white">
                            <span class="text-xl">A4</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-800">核心竞争对手深度剖析 - 配置管理</h3>
                            <p class="text-gray-600">管理竞争对手列表，每月费用2000元，最多支持20个竞争对手</p>
                        </div>
                    </div>

                    <!-- 价格信息 -->
                    <div class="bg-green-50 p-4 rounded-xl border border-green-200 mb-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-lg font-bold text-green-800">当前配置：¥2,000/月</div>
                                <div class="text-sm text-green-600">已监控 10/20 个竞争对手 | 每增加1个竞争对手 +¥200/月</div>
                            </div>
                            <div class="text-2xl text-green-600">🔍</div>
                        </div>
                    </div>

                    <!-- 竞争对手列表 -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-bold text-gray-800">竞争对手列表</h4>
                            <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors" onclick="addNewCompetitor()">
                                + 添加竞争对手
                            </button>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-xl overflow-hidden">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">公司名称</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">类别</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">网址</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-4 py-3 text-sm text-gray-800">Bosch</td>
                                        <td class="px-4 py-3"><span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">本土竞争对手</span></td>
                                        <td class="px-4 py-3 text-sm text-blue-600">www.bosch.de</td>
                                        <td class="px-4 py-3">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm mr-3">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 text-sm text-gray-800">Siemens</td>
                                        <td class="px-4 py-3"><span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">本土竞争对手</span></td>
                                        <td class="px-4 py-3 text-sm text-blue-600">www.siemens.de</td>
                                        <td class="px-4 py-3">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm mr-3">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 text-sm text-gray-800">海尔</td>
                                        <td class="px-4 py-3"><span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">中国竞争对手</span></td>
                                        <td class="px-4 py-3 text-sm text-blue-600">www.haier.de</td>
                                        <td class="px-4 py-3">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm mr-3">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 text-sm text-gray-800">LG Electronics</td>
                                        <td class="px-4 py-3"><span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">其他国际竞争对手</span></td>
                                        <td class="px-4 py-3 text-sm text-blue-600">www.lg.com</td>
                                        <td class="px-4 py-3">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm mr-3">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex gap-4">
                        <button class="flex-1 bg-gray-600 text-white py-3 rounded-lg font-semibold hover:bg-gray-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove();">
                            关闭
                        </button>
                        <button class="flex-1 bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-all" onclick="saveA4Config()">
                            保存配置
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // A6 领先者管理配置
        function openA6ConfigModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-6 max-w-4xl w-full mx-4 shadow-2xl max-h-[90vh] overflow-y-auto">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center text-white">
                            <span class="text-xl">A6</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-800">领先者研究与差异化策略 - 配置管理</h3>
                            <p class="text-gray-600">管理领先者列表，年费5万元，最多支持3个领先者</p>
                        </div>
                    </div>

                    <!-- 价格信息 -->
                    <div class="bg-orange-50 p-4 rounded-xl border border-orange-200 mb-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-lg font-bold text-orange-800">当前配置：¥5万/年</div>
                                <div class="text-sm text-orange-600">已研究 2/3 个领先者 | 每增加1个领先者 +¥1万/年</div>
                            </div>
                            <div class="text-2xl text-orange-600">👑</div>
                        </div>
                    </div>

                    <!-- 领先者列表 -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-bold text-gray-800">领先者列表</h4>
                            <button class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors" onclick="addNewLeader()">
                                + 添加领先者
                            </button>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-xl overflow-hidden">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">公司名称</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">领域</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">网址</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-4 py-3 text-sm text-gray-800">Tesla</td>
                                        <td class="px-4 py-3"><span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">电动汽车</span></td>
                                        <td class="px-4 py-3 text-sm text-blue-600">www.tesla.com</td>
                                        <td class="px-4 py-3">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm mr-3">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 text-sm text-gray-800">Apple</td>
                                        <td class="px-4 py-3"><span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">智能设备</span></td>
                                        <td class="px-4 py-3 text-sm text-blue-600">www.apple.com</td>
                                        <td class="px-4 py-3">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm mr-3">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex gap-4">
                        <button class="flex-1 bg-gray-600 text-white py-3 rounded-lg font-semibold hover:bg-gray-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove();">
                            关闭
                        </button>
                        <button class="flex-1 bg-orange-600 text-white py-3 rounded-lg font-semibold hover:bg-orange-700 transition-all" onclick="saveA6Config()">
                            保存配置
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // A7 订阅管理配置
        function openA7ConfigModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-6 max-w-2xl w-full mx-4 shadow-2xl">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center text-white">
                            <span class="text-xl">A7</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-800">出海合规风险导航 - 订阅管理</h3>
                            <p class="text-gray-600">按季度更新，年费2万元</p>
                        </div>
                    </div>

                    <!-- 当前订阅状态 -->
                    <div class="bg-blue-50 p-4 rounded-xl border border-blue-200 mb-6">
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <div class="text-lg font-bold text-blue-800">当前订阅：¥2万/年</div>
                                <div class="text-sm text-blue-600">有效期至：2024年12月31日</div>
                            </div>
                            <div class="text-2xl text-blue-600">📋</div>
                        </div>
                        <div class="text-sm text-blue-700">下次更新：2024年4月1日（Q2季度更新）</div>
                    </div>

                    <!-- 订阅选项 -->
                    <div class="mb-6">
                        <h4 class="text-lg font-bold text-gray-800 mb-4">订阅选项</h4>
                        <div class="space-y-3">
                            <div class="p-4 border border-gray-200 rounded-xl hover:border-blue-300 transition-colors cursor-pointer" onclick="selectSubscriptionOption('renew-1')">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-semibold text-gray-800">续订 1 年</div>
                                        <div class="text-sm text-gray-600">继续享受季度更新服务</div>
                                    </div>
                                    <div class="text-lg font-bold text-green-600">¥2万</div>
                                </div>
                            </div>
                            <div class="p-4 border border-gray-200 rounded-xl hover:border-blue-300 transition-colors cursor-pointer" onclick="selectSubscriptionOption('renew-2')">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-semibold text-gray-800">续订 2 年</div>
                                        <div class="text-sm text-gray-600">长期订阅享受95折优惠</div>
                                    </div>
                                    <div class="text-lg font-bold text-green-600">¥3.8万 <span class="text-sm text-gray-500 line-through">¥4万</span></div>
                                </div>
                            </div>
                            <div class="p-4 border border-red-200 bg-red-50 rounded-xl">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-semibold text-red-800">取消订阅</div>
                                        <div class="text-sm text-red-600">服务将在当前周期结束后停止</div>
                                    </div>
                                    <button class="text-red-600 hover:text-red-800 font-medium" onclick="cancelA7Subscription()">
                                        取消订阅
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex gap-4">
                        <button class="flex-1 bg-gray-600 text-white py-3 rounded-lg font-semibold hover:bg-gray-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove();">
                            关闭
                        </button>
                        <button class="flex-1 bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-all" onclick="saveA7Config()">
                            确认操作
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // A11 电商平台管理配置
        function openA11ConfigModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-6 max-w-4xl w-full mx-4 shadow-2xl max-h-[90vh] overflow-y-auto">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="w-12 h-12 bg-cyan-500 rounded-xl flex items-center justify-center text-white">
                            <span class="text-xl">A11</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-800">电商渠道进入策略 - 配置管理</h3>
                            <p class="text-gray-600">管理电商平台列表，年费5万元（含3个平台），每增加1个平台+1万元/年</p>
                        </div>
                    </div>

                    <!-- 价格信息 -->
                    <div class="bg-cyan-50 p-4 rounded-xl border border-cyan-200 mb-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-lg font-bold text-cyan-800">当前配置：¥5万/年</div>
                                <div class="text-sm text-cyan-600">已配置 3/3 个平台（基础套餐）| 每增加1个平台 +¥1万/年</div>
                            </div>
                            <div class="text-2xl text-cyan-600">🛒</div>
                        </div>
                    </div>

                    <!-- 电商平台列表 -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-bold text-gray-800">电商平台列表</h4>
                            <button class="bg-cyan-600 hover:bg-cyan-700 text-white px-4 py-2 rounded-lg font-medium transition-colors" onclick="addNewPlatform()">
                                + 添加平台
                            </button>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-xl overflow-hidden">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">平台名称</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">网址</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">备注</th>
                                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-4 py-3 text-sm text-gray-800">Amazon.de</td>
                                        <td class="px-4 py-3 text-sm text-blue-600">www.amazon.de</td>
                                        <td class="px-4 py-3 text-sm text-gray-600">德国最大电商平台</td>
                                        <td class="px-4 py-3">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm mr-3">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 text-sm text-gray-800">eBay.de</td>
                                        <td class="px-4 py-3 text-sm text-blue-600">www.ebay.de</td>
                                        <td class="px-4 py-3 text-sm text-gray-600">C2C和B2C综合平台</td>
                                        <td class="px-4 py-3">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm mr-3">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 text-sm text-gray-800">Otto.de</td>
                                        <td class="px-4 py-3 text-sm text-blue-600">www.otto.de</td>
                                        <td class="px-4 py-3 text-sm text-gray-600">德国本土电商巨头</td>
                                        <td class="px-4 py-3">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm mr-3">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex gap-4">
                        <button class="flex-1 bg-gray-600 text-white py-3 rounded-lg font-semibold hover:bg-gray-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove();">
                            关闭
                        </button>
                        <button class="flex-1 bg-cyan-600 text-white py-3 rounded-lg font-semibold hover:bg-cyan-700 transition-all" onclick="saveA11Config()">
                            保存配置
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 通用订阅配置管理
        function openSubscriptionConfigModal(artifactId) {
            const artifactNames = {
                'A5': '理想合作伙伴评估与匹配',
                'A8': '整合策略规划与动态优化',
                'A9': '目标市场理想客户画像构建',
                'A10': '目标市场定价与商业模式分析'
            };

            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-6 max-w-2xl w-full mx-4 shadow-2xl">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center text-white">
                            <span class="text-xl">${artifactId}</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-800">${artifactNames[artifactId]} - 订阅管理</h3>
                            <p class="text-gray-600">按季度更新，年费2万元</p>
                        </div>
                    </div>

                    <!-- 当前订阅状态 -->
                    <div class="bg-purple-50 p-4 rounded-xl border border-purple-200 mb-6">
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <div class="text-lg font-bold text-purple-800">当前订阅：¥2万/年</div>
                                <div class="text-sm text-purple-600">有效期至：2024年12月31日</div>
                            </div>
                            <div class="text-2xl text-purple-600">⭐</div>
                        </div>
                        <div class="text-sm text-purple-700">下次更新：2024年4月1日（Q2季度更新）</div>
                    </div>

                    <!-- 订阅选项 -->
                    <div class="mb-6">
                        <h4 class="text-lg font-bold text-gray-800 mb-4">订阅选项</h4>
                        <div class="space-y-3">
                            <div class="p-4 border border-gray-200 rounded-xl hover:border-purple-300 transition-colors cursor-pointer" onclick="selectSubscriptionOption('renew-1')">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-semibold text-gray-800">续订 1 年</div>
                                        <div class="text-sm text-gray-600">继续享受季度更新服务</div>
                                    </div>
                                    <div class="text-lg font-bold text-green-600">¥2万</div>
                                </div>
                            </div>
                            <div class="p-4 border border-gray-200 rounded-xl hover:border-purple-300 transition-colors cursor-pointer" onclick="selectSubscriptionOption('renew-2')">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-semibold text-gray-800">续订 2 年</div>
                                        <div class="text-sm text-gray-600">长期订阅享受95折优惠</div>
                                    </div>
                                    <div class="text-lg font-bold text-green-600">¥3.8万 <span class="text-sm text-gray-500 line-through">¥4万</span></div>
                                </div>
                            </div>
                            <div class="p-4 border border-red-200 bg-red-50 rounded-xl">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-semibold text-red-800">取消订阅</div>
                                        <div class="text-sm text-red-600">服务将在当前周期结束后停止</div>
                                    </div>
                                    <button class="text-red-600 hover:text-red-800 font-medium" onclick="cancelSubscription('${artifactId}')">
                                        取消订阅
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex gap-4">
                        <button class="flex-1 bg-gray-600 text-white py-3 rounded-lg font-semibold hover:bg-gray-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove();">
                            关闭
                        </button>
                        <button class="flex-1 bg-purple-600 text-white py-3 rounded-lg font-semibold hover:bg-purple-700 transition-all" onclick="saveSubscriptionConfig('${artifactId}')">
                            确认操作
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 保存配置函数
        function saveA4Config() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl text-white">✅</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">配置保存成功！</h3>
                        <div class="bg-green-50 p-4 rounded-xl border border-green-200 mb-6">
                            <div class="text-sm text-green-700">A4竞争对手配置已更新</div>
                            <div class="text-xs text-green-600 mt-2">监控服务将在1小时内生效</div>
                        </div>
                        <button class="w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove(); document.querySelectorAll('.fixed.inset-0')[0].remove();">
                            确定
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function saveA6Config() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl text-white">✅</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">配置保存成功！</h3>
                        <div class="bg-orange-50 p-4 rounded-xl border border-orange-200 mb-6">
                            <div class="text-sm text-orange-700">A6领先者配置已更新</div>
                            <div class="text-xs text-orange-600 mt-2">研究计划将在1个工作日内调整</div>
                        </div>
                        <button class="w-full bg-orange-600 text-white py-3 rounded-lg font-semibold hover:bg-orange-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove(); document.querySelectorAll('.fixed.inset-0')[0].remove();">
                            确定
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function saveA7Config() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl text-white">✅</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">订阅操作成功！</h3>
                        <div class="bg-blue-50 p-4 rounded-xl border border-blue-200 mb-6">
                            <div class="text-sm text-blue-700">A7订阅配置已更新</div>
                            <div class="text-xs text-blue-600 mt-2">订阅变更将在下个计费周期生效</div>
                        </div>
                        <button class="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove(); document.querySelectorAll('.fixed.inset-0')[0].remove();">
                            确定
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function saveA11Config() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl text-white">✅</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">配置保存成功！</h3>
                        <div class="bg-cyan-50 p-4 rounded-xl border border-cyan-200 mb-6">
                            <div class="text-sm text-cyan-700">A11电商平台配置已更新</div>
                            <div class="text-xs text-cyan-600 mt-2">渠道分析将在1个工作日内调整</div>
                        </div>
                        <button class="w-full bg-cyan-600 text-white py-3 rounded-lg font-semibold hover:bg-cyan-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove(); document.querySelectorAll('.fixed.inset-0')[0].remove();">
                            确定
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function saveSubscriptionConfig(artifactId) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl text-white">✅</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">订阅操作成功！</h3>
                        <div class="bg-purple-50 p-4 rounded-xl border border-purple-200 mb-6">
                            <div class="text-sm text-purple-700">${artifactId}订阅配置已更新</div>
                            <div class="text-xs text-purple-600 mt-2">订阅变更将在下个计费周期生效</div>
                        </div>
                        <button class="w-full bg-purple-600 text-white py-3 rounded-lg font-semibold hover:bg-purple-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove(); document.querySelectorAll('.fixed.inset-0')[0].remove();">
                            确定
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 点数充值模态框
        function openRechargeModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">💎</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">充值点数</h3>
                        <p class="text-gray-600">1元 = 1点数，充值后即可购买任务</p>
                    </div>

                    <!-- 当前余额 -->
                    <div class="bg-blue-50 p-4 rounded-xl border border-blue-200 mb-6">
                        <div class="flex items-center justify-between">
                            <span class="text-blue-700 font-medium">当前余额</span>
                            <span class="text-2xl font-bold text-blue-600" id="modalPointsBalance">8,250</span>
                        </div>
                    </div>

                    <!-- 充值选项 -->
                    <div class="space-y-3 mb-6">
                        <div class="text-sm font-medium text-gray-700 mb-3">选择充值金额</div>
                        <div class="grid grid-cols-2 gap-3">
                            <div class="recharge-option p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-colors" data-amount="100">
                                <div class="text-center">
                                    <div class="font-semibold text-gray-800">100 点数</div>
                                    <div class="text-sm text-gray-600">¥100</div>
                                </div>
                            </div>
                            <div class="recharge-option p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-colors" data-amount="500">
                                <div class="text-center">
                                    <div class="font-semibold text-gray-800">500 点数</div>
                                    <div class="text-sm text-gray-600">¥500</div>
                                </div>
                            </div>
                            <div class="recharge-option p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-colors" data-amount="1000">
                                <div class="text-center">
                                    <div class="font-semibold text-gray-800">1,000 点数</div>
                                    <div class="text-sm text-gray-600">¥1,000</div>
                                </div>
                            </div>
                            <div class="recharge-option p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-colors" data-amount="2000">
                                <div class="text-center">
                                    <div class="font-semibold text-gray-800">2,000 点数</div>
                                    <div class="text-sm text-gray-600">¥2,000</div>
                                </div>
                            </div>
                            <div class="recharge-option p-3 border border-purple-300 bg-purple-50 rounded-lg cursor-pointer hover:border-purple-400 transition-colors" data-amount="5000">
                                <div class="text-center">
                                    <div class="font-semibold text-purple-800">5,000 点数</div>
                                    <div class="text-sm text-purple-600">¥5,000</div>
                                    <div class="text-xs text-purple-500 mt-1">推荐</div>
                                </div>
                            </div>
                            <div class="recharge-option p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-colors" data-amount="10000">
                                <div class="text-center">
                                    <div class="font-semibold text-gray-800">10,000 点数</div>
                                    <div class="text-sm text-gray-600">¥10,000</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 支付方式 -->
                    <div class="mb-6">
                        <div class="text-sm font-medium text-gray-700 mb-3">支付方式</div>
                        <div class="flex gap-3">
                            <div class="payment-option flex-1 p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-green-300 hover:bg-green-50 transition-colors" data-method="wechat">
                                <div class="text-center">
                                    <div class="text-2xl mb-1">💚</div>
                                    <div class="text-sm font-medium">微信支付</div>
                                </div>
                            </div>
                            <div class="payment-option flex-1 p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-colors" data-method="alipay">
                                <div class="text-center">
                                    <div class="text-2xl mb-1">💙</div>
                                    <div class="text-sm font-medium">支付宝</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex gap-4">
                        <button class="flex-1 bg-gray-600 text-white py-3 rounded-lg font-semibold hover:bg-gray-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove();">
                            取消
                        </button>
                        <button id="confirmRechargeBtn" class="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all disabled:opacity-50" onclick="confirmRecharge()" disabled>
                            确认充值
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // 添加选择逻辑
            let selectedAmount = 0;
            let selectedMethod = '';

            modal.querySelectorAll('.recharge-option').forEach(option => {
                option.addEventListener('click', function() {
                    modal.querySelectorAll('.recharge-option').forEach(opt => {
                        opt.classList.remove('border-blue-400', 'bg-blue-100');
                    });
                    this.classList.add('border-blue-400', 'bg-blue-100');
                    selectedAmount = parseInt(this.dataset.amount);
                    updateRechargeButton();
                });
            });

            modal.querySelectorAll('.payment-option').forEach(option => {
                option.addEventListener('click', function() {
                    modal.querySelectorAll('.payment-option').forEach(opt => {
                        opt.classList.remove('border-blue-400', 'bg-blue-100', 'border-green-400', 'bg-green-100');
                    });
                    selectedMethod = this.dataset.method;
                    if (selectedMethod === 'wechat') {
                        this.classList.add('border-green-400', 'bg-green-100');
                    } else {
                        this.classList.add('border-blue-400', 'bg-blue-100');
                    }
                    updateRechargeButton();
                });
            });

            function updateRechargeButton() {
                const btn = modal.querySelector('#confirmRechargeBtn');
                btn.disabled = !(selectedAmount > 0 && selectedMethod);
            }

            window.confirmRecharge = function() {
                // 模拟充值成功
                const currentBalance = parseInt(document.getElementById('sidebarPointsBalance').textContent.replace(',', ''));
                const newBalance = currentBalance + selectedAmount;
                
                // 更新余额显示
                document.getElementById('sidebarPointsBalance').textContent = newBalance.toLocaleString();
                
                // 关闭充值弹窗
                modal.remove();
                
                // 显示成功提示
                showRechargeSuccess(selectedAmount, newBalance);
            };
        }

        // 充值成功提示
        function showRechargeSuccess(amount, newBalance) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl text-white">✅</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">充值成功！</h3>
                        <div class="bg-green-50 p-4 rounded-xl border border-green-200 mb-6">
                            <div class="text-sm text-green-700">成功充值 ${amount.toLocaleString()} 点数</div>
                            <div class="text-lg font-bold text-green-800 mt-2">当前余额：${newBalance.toLocaleString()} 点数</div>
                        </div>
                        <button class="w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove();">
                            确定
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 购买Artifact功能
        function purchaseArtifact(artifactId, price) {
            const currentBalance = parseInt(document.getElementById('sidebarPointsBalance').textContent.replace(',', ''));
            
            if (currentBalance < price) {
                // 余额不足，提示充值
                showInsufficientBalanceModal(price, currentBalance);
                return;
            }

            // 确认购买弹窗
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            
            const artifactNames = {
                'A4': '核心竞争对手深度剖析',
                'A5': '理想合作伙伴评估与匹配',
                'A6': '领先者研究与差异化策略',
                'A7': '出海合规风险导航',
                'A8': '整合策略规划与动态优化',
                'A9': '目标市场理想客户画像构建',
                'A10': '目标市场定价与商业模式分析',
                'A11': '电商渠道进入策略'
            };

            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">🛒</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">确认购买</h3>
                        <p class="text-gray-600">您正在购买以下任务</p>
                    </div>

                    <!-- 任务信息 -->
                    <div class="bg-purple-50 p-4 rounded-xl border border-purple-200 mb-6">
                        <div class="flex items-center gap-3 mb-3">
                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-sm">${artifactId}</div>
                            <div class="font-semibold text-purple-800">${artifactNames[artifactId]}</div>
                        </div>
                        <div class="text-sm text-purple-700">价格：${price.toLocaleString()} 点数</div>
                    </div>

                    <!-- 余额信息 -->
                    <div class="bg-blue-50 p-4 rounded-xl border border-blue-200 mb-6">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-blue-700">当前余额</span>
                            <span class="font-bold text-blue-800">${currentBalance.toLocaleString()} 点数</span>
                        </div>
                        <div class="flex items-center justify-between text-sm mt-2">
                            <span class="text-blue-700">购买后余额</span>
                            <span class="font-bold text-blue-800">${(currentBalance - price).toLocaleString()} 点数</span>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex gap-4">
                        <button class="flex-1 bg-gray-600 text-white py-3 rounded-lg font-semibold hover:bg-gray-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove();">
                            取消
                        </button>
                        <button class="flex-1 bg-purple-600 text-white py-3 rounded-lg font-semibold hover:bg-purple-700 transition-all" onclick="confirmPurchase('${artifactId}', ${price})">
                            确认购买
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 确认购买
        function confirmPurchase(artifactId, price) {
            const currentBalance = parseInt(document.getElementById('sidebarPointsBalance').textContent.replace(',', ''));
            const newBalance = currentBalance - price;
            
            // 更新余额
            document.getElementById('sidebarPointsBalance').textContent = newBalance.toLocaleString();
            
            // 更新Artifact状态
            const card = document.querySelector(`[data-artifact="${artifactId}"]`);
            if (card) {
                // 更改为已购买状态
                card.dataset.status = 'purchased';
                card.className = card.className.replace('border-gray-300 bg-gray-50', 'border-green-200 bg-green-50');
                card.querySelector('.bg-gray-400').className = 'w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm';
                card.querySelector('.bg-gray-100.text-gray-600').className = 'px-2 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded-full';
                card.querySelector('.bg-gray-100.text-gray-600').textContent = '✓ 已激活';
                
                // 更新按钮
                const button = card.querySelector('button');
                button.className = 'px-3 py-1 bg-green-600 text-white rounded-lg text-sm hover:bg-green-700 transition-colors';
                button.textContent = '管理配置';
                button.onclick = function() {
                    if (artifactId === 'A4') openA4ConfigModal();
                    else if (artifactId === 'A6') openA6ConfigModal();
                    else if (artifactId === 'A7') openA7ConfigModal();
                    else if (artifactId === 'A11') openA11ConfigModal();
                    else openSubscriptionConfigModal(artifactId);
                };
                
                // 更新内容颜色
                card.querySelectorAll('.text-gray-600').forEach(el => {
                    el.className = el.className.replace('text-gray-600', 'text-gray-700');
                });
                card.querySelectorAll('.text-gray-500').forEach(el => {
                    if (!el.classList.contains('line-through')) {
                        el.className = el.className.replace('text-gray-500', 'text-gray-500');
                    }
                });
                card.querySelectorAll('.bg-gray-100.border-gray-200').forEach(el => {
                    el.className = el.className.replace('bg-gray-100 border-gray-200', 'bg-green-100 border-green-200');
                });
            }
            
            // 关闭购买弹窗
            document.querySelectorAll('.fixed.inset-0').forEach(modal => modal.remove());
            
            // 显示购买成功提示
            showPurchaseSuccess(artifactId, price);
        }

        // 余额不足提示
        function showInsufficientBalanceModal(price, currentBalance) {
            const needed = price - currentBalance;
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl text-white">⚠️</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">余额不足</h3>
                        <div class="bg-orange-50 p-4 rounded-xl border border-orange-200 mb-6">
                            <div class="text-sm text-orange-700">所需点数：${price.toLocaleString()}</div>
                            <div class="text-sm text-orange-700">当前余额：${currentBalance.toLocaleString()}</div>
                            <div class="text-lg font-bold text-orange-800 mt-2">还需充值：${needed.toLocaleString()} 点数</div>
                        </div>
                        <div class="flex gap-4">
                            <button class="flex-1 bg-gray-600 text-white py-3 rounded-lg font-semibold hover:bg-gray-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove();">
                                取消
                            </button>
                            <button class="flex-1 bg-orange-600 text-white py-3 rounded-lg font-semibold hover:bg-orange-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove(); openRechargeModal();">
                                去充值
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 购买成功提示
        function showPurchaseSuccess(artifactId, price) {
            const artifactNames = {
                'A4': '核心竞争对手深度剖析',
                'A5': '理想合作伙伴评估与匹配',
                'A6': '领先者研究与差异化策略',
                'A7': '出海合规风险导航',
                'A8': '整合策略规划与动态优化',
                'A9': '目标市场理想客户画像构建',
                'A10': '目标市场定价与商业模式分析',
                'A11': '电商渠道进入策略'
            };

            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl text-white">🎉</span>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">购买成功！</h3>
                        <div class="bg-green-50 p-4 rounded-xl border border-green-200 mb-6">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm">${artifactId}</div>
                                <div class="font-semibold text-green-800">${artifactNames[artifactId]}</div>
                            </div>
                            <div class="text-sm text-green-700">已成功激活，Emma将开始为您执行此任务</div>
                            <div class="text-xs text-green-600 mt-2">您可以在"管理配置"中查看和调整任务设置</div>
                        </div>
                        <button class="w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-all" onclick="this.parentElement.parentElement.parentElement.remove();">
                            确定
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 月份选择器功能
        function initializeMonthSelector() {
            const monthSelector = document.getElementById('monthSelector');
            if (monthSelector) {
                monthSelector.addEventListener('change', updateStatsForMonth);
            }
        }

        // 根据选择的月份更新统计数据
        function updateStatsForMonth() {
            const selectedMonth = document.getElementById('monthSelector').value;
            
            // 模拟不同月份的数据
            const monthlyData = {
                '2024-01': {
                    status: '8个任务流',
                    workforce: '45 人天',
                    costSaving: '¥13万/月',
                    coverage: '156 竞品'
                },
                '2023-12': {
                    status: '7个任务流',
                    workforce: '38 人天',
                    costSaving: '¥11万/月',
                    coverage: '142 竞品'
                },
                '2023-11': {
                    status: '6个任务流',
                    workforce: '32 人天',
                    costSaving: '¥9.5万/月',
                    coverage: '128 竞品'
                },
                '2023-10': {
                    status: '6个任务流',
                    workforce: '35 人天',
                    costSaving: '¥10.5万/月',
                    coverage: '135 竞品'
                },
                '2023-09': {
                    status: '5个任务流',
                    workforce: '28 人天',
                    costSaving: '¥8万/月',
                    coverage: '118 竞品'
                },
                '2023-08': {
                    status: '4个任务流',
                    workforce: '24 人天',
                    costSaving: '¥7万/月',
                    coverage: '105 竞品'
                }
            };

            const data = monthlyData[selectedMonth] || monthlyData['2024-01'];
            
            // 更新统计卡片数据
            document.getElementById('statusStats').textContent = data.status;
            document.getElementById('workforceStats').textContent = data.workforce;
            document.getElementById('costSavingStats').textContent = data.costSaving;
            document.getElementById('coverageStats').textContent = data.coverage;

            // 添加切换动画效果
            const statsContainer = document.getElementById('statsContainer');
            statsContainer.style.opacity = '0.7';
            setTimeout(() => {
                statsContainer.style.opacity = '1';
            }, 200);
        }

        // 在页面加载时初始化月份选择器
        document.addEventListener('DOMContentLoaded', function() {
            initializeMonthSelector();
        });
    </script>
</body>
</html>
/* 必要的自定义动画和组件 - Foxu AI Team */

/* 背景动画效果 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 15% 85%, rgba(79, 70, 229, 0.12) 0%, transparent 60%),
        radial-gradient(circle at 85% 15%, rgba(168, 85, 247, 0.1) 0%, transparent 60%),
        radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.05) 0%, transparent 70%);
    pointer-events: none;
    z-index: -1;
    animation: atmosphericFloat 20s ease-in-out infinite alternate;
}

/* 关键帧动画 */
@keyframes atmosphericFloat {
    0% {
        transform: translateY(0px) scale(1);
        opacity: 0.8;
    }
    100% {
        transform: translateY(-10px) scale(1.02);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(79, 70, 229, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(79, 70, 229, 0.6), 0 0 30px rgba(79, 70, 229, 0.4);
    }
}

@keyframes pulse-online {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(1.8);
        opacity: 0;
    }
}

/* 页面加载动画 */
body {
    animation: fadeIn 0.6s ease-out;
}

/* 在线状态指示器增强动画 */
.status-indicator.online::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: currentColor;
    animation: pulse-online 2s infinite;
}

/* 聊天弹窗激活状态 */
#chatModal.active {
    opacity: 1 !important;
    visibility: visible !important;
}

#chatModal.active > div {
    transform: translateX(0) !important;
}

/* 用户下拉菜单基础样式 */
.user-dropdown-menu {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    width: 280px;
    background: rgba(255, 255, 255, 0.98);
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    backdrop-filter: blur(12px);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px) scale(0.95);
    transition: all 0.2s ease-out;
}

.dropdown-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
}

.user-avatar-menu {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #ddd6fe;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.user-name-menu {
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.user-email {
    color: #6b7280;
    font-size: 12px;
}

.dropdown-divider {
    height: 1px;
    background: #e5e7eb;
    margin: 0 8px;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    text-decoration: none;
    color: #374151;
    font-size: 14px;
    transition: background-color 0.15s ease;
}

.dropdown-item:hover {
    background-color: #f9fafb;
}

.dropdown-item.text-red {
    color: #dc2626;
}

.dropdown-item.text-red:hover {
    background-color: #fef2f2;
}

.item-icon {
    font-size: 16px;
}

/* 用户下拉菜单激活状态 */
.user-dropdown.active .user-dropdown-menu {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) scale(1) !important;
}

.user-dropdown.active .dropdown-arrow {
    transform: rotate(180deg);
}

/* 雇佣弹窗激活状态 */
.modal.active {
    opacity: 1 !important;
    visibility: visible !important;
}

.modal.active .bg-white {
    transform: scale(1) !important;
}

/* 选项卡切换状态 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 聊天消息样式 */
.message {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.message-avatar {
    flex-shrink: 0;
}

.message-avatar img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.message-content {
    flex: 1;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.message-header strong {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
}

.message-time {
    font-size: 12px;
    color: #6b7280;
}

.message-text {
    background: #f3f4f6;
    padding: 12px;
    border-radius: 8px;
    font-size: 14px;
    color: #374151;
    line-height: 1.5;
    margin: 4px 0;
}

.user-message .message-text {
    background: #dbeafe;
    color: #1e40af;
}

.ai-message .message-text {
    background: #f3f4f6;
    color: #374151;
}

/* 自定义滚动条 */
.chat-messages::-webkit-scrollbar,
#chatMessages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track,
#chatMessages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb,
#chatMessages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover,
#chatMessages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 复杂的渐变效果（TailwindCSS无法实现的特殊渐变） */
.specialty-gradient {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.02) 0%, rgba(168, 85, 247, 0.02) 100%);
}

/* 工作区图表自定义样式 */
.chart-bars {
    display: flex;
    align-items: end;
    justify-content: space-between;
    height: 120px;
    gap: 8px;
    padding: 10px;
}

.bar-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
}

.bar {
    width: 100%;
    max-width: 24px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-radius: 4px 4px 0 0;
    transition: all 0.3s ease;
    position: relative;
}

.bar:hover {
    opacity: 0.8;
    transform: scale(1.05);
}

.bar-label {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 8px;
    font-weight: 500;
}

.bar-value {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    font-weight: 600;
    color: #374151;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.bar-item:hover .bar-value {
    opacity: 1;
}

/* 热力图样式 */
.heatmap-grid {
    display: grid;
    gap: 2px;
}

.heatmap-row {
    display: flex;
    gap: 2px;
}

.heatmap-cell {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.heatmap-cell:hover {
    transform: scale(1.2);
    border-radius: 3px;
}

.heatmap-cell.level-0 { background-color: #f3f4f6; }
.heatmap-cell.level-1 { background-color: #d1fae5; }
.heatmap-cell.level-2 { background-color: #a7f3d0; }
.heatmap-cell.level-3 { background-color: #6ee7b7; }
.heatmap-cell.level-4 { background-color: #10b981; }

.legend-scale {
    display: flex;
    gap: 2px;
    margin: 0 8px;
}

.legend-cell {
    width: 10px;
    height: 10px;
    border-radius: 2px;
}

/* 进度条动画 */
.progress-fill {
    transition: width 1s ease-in-out;
    background: linear-gradient(90deg, #10b981, #059669);
}

/* 高级阴影效果 */
.shadow-depth {
    box-shadow: 
        0 4px 16px rgba(0, 0, 0, 0.08), 
        0 8px 32px rgba(0, 0, 0, 0.04), 
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.shadow-floating {
    box-shadow: 
        0 20px 50px rgba(79, 70, 229, 0.2), 
        0 10px 25px rgba(79, 70, 229, 0.15);
}

.shadow-glow {
    box-shadow: 
        0 0 20px rgba(79, 70, 229, 0.3), 
        0 0 40px rgba(79, 70, 229, 0.1);
}

/* 工作职责页面样式 */
.responsibilities-section {
    max-width: none;
    padding: 0;
}

.responsibilities-section > h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.responsibilities-section > p {
    color: #6b7280;
    font-size: 1.125rem;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.responsibility-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.responsibility-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.responsibility-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.responsibility-icon {
    width: 3rem;
    height: 3rem;
    margin-bottom: 1rem;
    color: #3b82f6;
}

.responsibility-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.responsibility-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.responsibility-card li {
    padding: 0.5rem 0;
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
    position: relative;
    padding-left: 1.5rem;
}

.responsibility-card li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

.task-assignment-cta {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    border-radius: 1.5rem;
    padding: 2rem;
    margin-top: 2rem;
}

.task-assignment-cta h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.task-assignment-cta > p {
    color: #6b7280;
    margin-bottom: 2rem;
}

.artifacts-checklist {
    display: grid;
    gap: 2rem;
    margin-bottom: 2rem;
}

.artifact-category h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #3b82f6;
}

.artifact-items {
    display: grid;
    gap: 1rem;
}

.artifact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 2px solid #e5e7eb;
    cursor: pointer;
    transition: all 0.3s ease;
}

.artifact-item:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.artifact-item input[type="checkbox"] {
    width: 1.25rem;
    height: 1.25rem;
    accent-color: #3b82f6;
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.artifact-item input[type="checkbox"]:checked + .artifact-info {
    opacity: 1;
}

.artifact-item input[type="checkbox"]:not(:checked) + .artifact-info {
    opacity: 0.7;
}

.artifact-info {
    flex: 1;
    transition: opacity 0.3s ease;
}

.artifact-info h5 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.artifact-info p {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 0.75rem;
}

.artifact-price {
    display: inline-block;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.task-summary {
    margin-top: 2rem;
}

.summary-box {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.summary-box h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
    text-align: center;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.summary-item:last-of-type {
    border-bottom: none;
    margin-top: 0.5rem;
    padding-top: 1rem;
    border-top: 2px solid #e5e7eb;
}

.summary-item.total {
    font-weight: 600;
    font-size: 1.125rem;
}

.summary-item span:first-child {
    color: #6b7280;
}

.summary-item span:last-child {
    color: #1f2937;
    font-weight: 500;
}

.summary-item.total span:last-child {
    color: #3b82f6;
    font-size: 1.25rem;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.5rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-primary:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    width: 100%;
    margin-top: 1.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    body::before {
        opacity: 0.5;
    }
    
    .chart-bars {
        height: 80px;
        gap: 4px;
    }
    
    .bar {
        max-width: 16px;
    }
    
    .heatmap-cell {
        width: 8px;
        height: 8px;
    }
    
    .responsibility-categories {
        grid-template-columns: 1fr;
    }
    
    .artifact-item {
        flex-direction: column;
        text-align: center;
    }
    
    .artifact-item input[type="checkbox"] {
        align-self: center;
    }
}

@media (max-width: 480px) {
    .chart-bars {
        height: 60px;
        gap: 2px;
        padding: 5px;
    }
    
    .bar {
        max-width: 12px;
    }
    
    .task-assignment-cta {
        padding: 1.5rem;
    }
    
    .summary-box {
        padding: 1.5rem;
    }
} 
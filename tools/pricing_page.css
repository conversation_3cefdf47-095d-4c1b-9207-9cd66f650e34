/* --- Global Styles --- */
body {
    font-family: 'Noto Sans SC', sans-serif;
    margin: 0;
    color: #333;
    background-color: #f8f9fa;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    color: #1a2b4d;
}

/* --- Buttons --- */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background-color: #0052cc;
    color: #fff;
}

.btn-primary:hover {
    background-color: #0041a3;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: #e9ecef;
    color: #1a2b4d;
}

.btn-secondary:hover {
    background-color: #d3d9df;
}

.btn-large {
    padding: 15px 35px;
    font-size: 1.1rem;
}

/* --- Header --- */
.main-header {
    background-color: #fff;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.main-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: #0052cc;
}

.main-header nav a {
    margin: 0 15px;
    text-decoration: none;
    color: #333;
    font-weight: 500;
}

/* --- Hero Section --- */
.hero {
    background-image: linear-gradient(rgba(15, 23, 42, 0.8), rgba(15, 23, 42, 0.8)), url('https://images.unsplash.com/photo-1518779578993-cfa65b86c04a?q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=1920&h=1080&fit=crop&ixid=M3wxMjA3fDB8MXxzZWFyY2h8MXx8YWJzdHJhY3QlMjB0ZWNobm9ncmFwaHl8ZW58MHx8fHx8MTcxOTkyNjQwMHww');
    background-size: cover;
    background-position: center;
    color: #f0f0f0; /* Light text color for contrast */
    text-align: center;
    padding: 80px 0;
    border-bottom: 1px solid #2a3a55;
}

.hero h1 {
    font-size: 2.8rem;
    margin-bottom: 20px;
    color: #fff; /* White heading */
}

.hero .subtitle {
    font-size: 1.4rem;
    color: #a0aec0; /* Lighter subtitle color */
    margin-bottom: 40px;
}

.value-props {
    display: flex;
    justify-content: space-around;
    gap: 30px;
}

.prop {
    max-width: 300px;
    background: rgba(255, 255, 255, 0.05); /* Subtle card background */
    padding: 20px;
    border-radius: 8px;
}

.prop h3 {
    font-size: 1.5rem;
    color: #63aeff; /* Vibrant light blue for prop titles */
}

/* --- Unified Comparison Matrix --- */
.comparison-matrix {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    background: #fff;
    box-shadow: 0 8px 30px rgba(0,0,0,0.07);
}

.plans-header {
    display: grid;
    grid-template-columns: 25% 25% 25% 25%;
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
    position: -webkit-sticky; /* Safari */
    position: sticky;
    top: 70px; /* Adjust based on header height */
    z-index: 900;
}

.plan-column {
    padding: 20px;
    text-align: center;
    border-left: 1px solid #e0e0e0;
}

.plan-column.recommended {
    border: 2px solid #ff9900;
    border-radius: 12px 12px 0 0;
    position: relative;
    background-color: #fffaf2;
}

.plan-column h3 {
    margin-top: 0;
}

.plan-column .price {
    font-size: 1.6rem;
    font-weight: 700;
    color: #0052cc;
    margin: 10px 0 20px;
}

.plan-column .price span {
    font-size: 2.5rem;
}

.feature-section-title {
    grid-column: 1 / -1;
    background-color: #f8f9fa;
    padding: 15px 20px;
    font-weight: 700;
    font-size: 1.2rem;
    color: #1a2b4d;
    border-top: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
}

.features-body {
    display: grid;
    grid-template-columns: 25% 25% 25% 25%;
}

.feature-row {
    display: contents; /* Allows grid layout to pass through */
}

.feature-section-title {
    grid-column: 1 / -1;
    background-color: #f8f9fa;
    padding: 15px 20px;
    font-weight: 700;
    font-size: 1.2rem;
    color: #1a2b4d;
    border-top: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
}

.features-body {
    display: grid;
    grid-template-columns: 25% 25% 25% 25%;
}

.feature-row {
    display: contents; /* Allows grid layout to pass through */
}

.feature-name, .plan-feature {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
}

.feature-name {
    font-weight: 500;
}

.plan-feature {
    justify-content: center;
    text-align: center;
    border-left: 1px solid #e0e0e0;
}

.plan-feature.highlight {
    color: #0052cc;
    font-weight: 700;
}

.recommend-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: #ff9900;
    color: #fff;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 700;
}

.pricing-card.recommended {
    border: 2px solid #ff9900;
    transform: scale(1.05);
}

.recommend-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: #ff9900;
    color: #fff;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 700;
}

.card-header {
    padding: 30px;
    border-bottom: 1px solid #e9ecef;
}

.card-header h3 {
    font-size: 1.6rem;
    margin: 0 0 10px;
}

.price {
    font-size: 1.8rem;
    font-weight: 700;
    color: #0052cc;
}

.price span {
    font-size: 3rem;
}

.audience {
    color: #666;
    font-size: 0.9rem;
}

.card-body {
    padding: 30px;
    flex-grow: 1;
}

.core-value {
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 20px;
}

.card-body ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.card-body ul li {
    margin-bottom: 15px;
    color: #555;
}

.card-footer {
    padding: 30px;
    border-top: 1px solid #e9ecef;
}

.card-footer .btn {
    width: 100%;
    text-align: center;
}

/* --- Details Section --- */
.details-section {
    padding: 80px 0;
    background: #fff;
}

.details-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
}

.plan-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.plan-detail-card {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 8px;
}

/* --- Cases & Testimonials --- */
.cases-section, .testimonials-section {
    padding: 80px 0;
}

.cases-section h2, .testimonials-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
}

.case-study-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.case-card, .testimonial-card {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.testimonial-card {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.testimonial-card blockquote {
    font-size: 1.2rem;
    font-style: italic;
    margin: 0 0 20px;
    border: none;
    padding: 0;
}

.testimonial-card cite {
    font-weight: 700;
    color: #0052cc;
}

/* --- CTA Section --- */
.cta-section {
    background: #1a2b4d;
    color: #fff;
    padding: 80px 0;
    text-align: center;
}

.cta-section h2 {
    color: #fff;
    font-size: 2.5rem;
}

.cta-section p {
    font-size: 1.2rem;
    margin-bottom: 40px;
}

.cta-section .btn-primary {
    background-color: #ff9900;
    color: #1a2b4d;
}

.cta-section .btn-primary:hover {
    background-color: #ffad33;
}

.contact-info {
    margin-top: 20px;
}

/* --- Footer --- */
.main-footer-bottom {
    background: #f8f9fa;
    color: #666;
    text-align: center;
    padding: 20px 0;
    font-size: 0.9rem;
}

/* --- Responsive Design --- */
@media (max-width: 992px) {
    .pricing-table {
        flex-direction: column;
        align-items: center;
    }
    .pricing-card {
        width: 100%;
        max-width: 450px;
        margin-bottom: 30px;
    }
    .pricing-card.recommended {
        transform: scale(1);
    }
}

@media (max-width: 768px) {
    .main-header .container {
        flex-direction: column;
    }
    .main-header nav {
        margin: 15px 0;
    }
    .hero h1 { font-size: 2.2rem; }
    .hero .subtitle { font-size: 1.2rem; }
    .value-props {
        flex-direction: column;
        align-items: center;
    }
}
